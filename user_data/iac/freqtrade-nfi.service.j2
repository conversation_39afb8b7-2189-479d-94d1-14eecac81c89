[Unit]
Description=FreqTrade Bot (NFI)
After=network.target
Requires=network.target

[Service]
User=freqtrade
Group=freqtrade
WorkingDirectory=/home/<USER>/freqtrade

# Activate virtual environment and run
ExecStart={{ venv_dir }}/bin/freqtrade trade --config user_data/config-nfi.json --logfile user_data/logs/freqtrade-nfi.log

# Process management
ExecReload=/bin/kill -HUP $MAINPID
KillSignal=SIGINT
Restart=always
RestartSec=10
StartLimitInterval=60
StartLimitBurst=3

# Security hardening
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=full
ReadWritePaths=/home/<USER>/freqtrade/user_data/
CapabilityBoundingSet=

# Environment
Environment="PYTHONPATH=/home/<USER>/freqtrade"
Environment="PATH={{ venv_dir }}/bin:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"

[Install]
WantedBy=multi-user.target
