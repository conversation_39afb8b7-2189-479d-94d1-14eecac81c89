---
- name: Set up system requirements for Freqtrade bot
  hosts: admin
  gather_facts: no

  tasks:
    - name: Install Python and pip
      apt:
        name:
          - build-essential
          - python3-pip
          - python3-venv
          - python3-dev
          - git
        state: present
        update_cache: yes

    - name: Create non-root user
      user:
        name: freqtrade
        create_home: yes
        shell: /bin/bash

    - name: Add user to sudoers
      lineinfile:
        path: /etc/sudoers
        line: "freqtrade ALL=(ALL) NOPASSWD: ALL"
        state: present

    - name: Ensure .ssh directory exists
      file:
        path: /home/<USER>/.ssh
        state: directory
        mode: '0700'
        owner: freqtrade
        group: freqtrade

    - name: Copy SSH key for user (optional)
      copy:
        src: ~/.ssh/id_ed25519.pub
        dest: "/home/<USER>/.ssh/authorized_keys"
        mode: '0600'
        owner: freqtrade
        group: freqtrade
