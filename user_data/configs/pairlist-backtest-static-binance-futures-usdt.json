{"stake_currency": "USDT", "exchange": {"name": "binance", "pair_whitelist": ["1000000MOG/USDT:USDT", "1000BONK/USDT:USDT", "1000CAT/USDT:USDT", "1000CHEEMS/USDT:USDT", "1000FLOKI/USDT:USDT", "1000LUNC/USDT:USDT", "1000PEPE/USDT:USDT", "1000RATS/USDT:USDT", "1000SATS/USDT:USDT", "1000SHIB/USDT:USDT", "1000WHY/USDT:USDT", "1000X/USDT:USDT", "1000XEC/USDT:USDT", "1INCH/USDT:USDT", "1MBABYDOGE/USDT:USDT", "AAVE/USDT:USDT", "ACE/USDT:USDT", "ACH/USDT:USDT", "ACT/USDT:USDT", "ACX/USDT:USDT", "ADA/USDT:USDT", "AERGO/USDT:USDT", "AERO/USDT:USDT", "AEVO/USDT:USDT", "AGLD/USDT:USDT", "AI/USDT:USDT", "AI16Z/USDT:USDT", "AIXBT/USDT:USDT", "AKT/USDT:USDT", "ALGO/USDT:USDT", "ALICE/USDT:USDT", "ALPACA/USDT:USDT", "ALPHA/USDT:USDT", "ALT/USDT:USDT", "AMB/USDT:USDT", "ANKR/USDT:USDT", "APE/USDT:USDT", "API3/USDT:USDT", "APT/USDT:USDT", "AR/USDT:USDT", "ARB/USDT:USDT", "ARK/USDT:USDT", "ARKM/USDT:USDT", "ARPA/USDT:USDT", "ASTR/USDT:USDT", "ATA/USDT:USDT", "ATOM/USDT:USDT", "AUCTION/USDT:USDT", "AVA/USDT:USDT", "AVAX/USDT:USDT", "AXL/USDT:USDT", "AXS/USDT:USDT", "BADGER/USDT:USDT", "BAKE/USDT:USDT", "BAL/USDT:USDT", "BAN/USDT:USDT", "BANANA/USDT:USDT", "BAND/USDT:USDT", "BAT/USDT:USDT", "BB/USDT:USDT", "BCH/USDT:USDT", "BEAMX/USDT:USDT", "BEL/USDT:USDT", "BICO/USDT:USDT", "BIGTIME/USDT:USDT", "BIO/USDT:USDT", "BLUR/USDT:USDT", "BNB/USDT:USDT", "BNT/USDT:USDT", "BNX/USDT:USDT", "BOME/USDT:USDT", "BRETT/USDT:USDT", "BSV/USDT:USDT", "BSW/USDT:USDT", "BTC/USDT:USDT", "BTCDOM/USDT:USDT", "C98/USDT:USDT", "CAKE/USDT:USDT", "CATI/USDT:USDT", "CELO/USDT:USDT", "CELR/USDT:USDT", "CETUS/USDT:USDT", "CFX/USDT:USDT", "CGPT/USDT:USDT", "CHESS/USDT:USDT", "CHILLGUY/USDT:USDT", "CHR/USDT:USDT", "CHZ/USDT:USDT", "CKB/USDT:USDT", "COMBO/USDT:USDT", "COMP/USDT:USDT", "COS/USDT:USDT", "COTI/USDT:USDT", "COW/USDT:USDT", "CRV/USDT:USDT", "CTSI/USDT:USDT", "CYBER/USDT:USDT", "DASH/USDT:USDT", "DEFI/USDT:USDT", "DEGEN/USDT:USDT", "DEGO/USDT:USDT", "DENT/USDT:USDT", "DEXE/USDT:USDT", "DF/USDT:USDT", "DIA/USDT:USDT", "DODOX/USDT:USDT", "DOGE/USDT:USDT", "DOGS/USDT:USDT", "DOT/USDT:USDT", "DRIFT/USDT:USDT", "DUSK/USDT:USDT", "DYDX/USDT:USDT", "DYM/USDT:USDT", "EDU/USDT:USDT", "EGLD/USDT:USDT", "EIGEN/USDT:USDT", "ENA/USDT:USDT", "ENJ/USDT:USDT", "ENS/USDT:USDT", "EOS/USDT:USDT", "ETC/USDT:USDT", "ETH/USDT:USDT", "ETHFI/USDT:USDT", "ETHW/USDT:USDT", "FARTCOIN/USDT:USDT", "FET/USDT:USDT", "FIDA/USDT:USDT", "FIL/USDT:USDT", "FIO/USDT:USDT", "FLM/USDT:USDT", "FLOW/USDT:USDT", "FLUX/USDT:USDT", "FTM/USDT:USDT", "FXS/USDT:USDT", "G/USDT:USDT", "GALA/USDT:USDT", "GAS/USDT:USDT", "GHST/USDT:USDT", "GLM/USDT:USDT", "GMT/USDT:USDT", "GMX/USDT:USDT", "GOAT/USDT:USDT", "GRASS/USDT:USDT", "GRIFFAIN/USDT:USDT", "GRT/USDT:USDT", "GTC/USDT:USDT", "HBAR/USDT:USDT", "HFT/USDT:USDT", "HIFI/USDT:USDT", "HIGH/USDT:USDT", "HIPPO/USDT:USDT", "HIVE/USDT:USDT", "HMSTR/USDT:USDT", "HOOK/USDT:USDT", "HOT/USDT:USDT", "ICP/USDT:USDT", "ICX/USDT:USDT", "ID/USDT:USDT", "ILV/USDT:USDT", "IMX/USDT:USDT", "INJ/USDT:USDT", "IO/USDT:USDT", "IOST/USDT:USDT", "IOTA/USDT:USDT", "IOTX/USDT:USDT", "JASMY/USDT:USDT", "JOE/USDT:USDT", "JTO/USDT:USDT", "JUP/USDT:USDT", "KAIA/USDT:USDT", "KAS/USDT:USDT", "KAVA/USDT:USDT", "KDA/USDT:USDT", "KMNO/USDT:USDT", "KNC/USDT:USDT", "KOMA/USDT:USDT", "KSM/USDT:USDT", "LDO/USDT:USDT", "LEVER/USDT:USDT", "LINA/USDT:USDT", "LINK/USDT:USDT", "LISTA/USDT:USDT", "LIT/USDT:USDT", "LOKA/USDT:USDT", "LPT/USDT:USDT", "LQTY/USDT:USDT", "LRC/USDT:USDT", "LSK/USDT:USDT", "LTC/USDT:USDT", "LUMIA/USDT:USDT", "LUNA2/USDT:USDT", "MAGIC/USDT:USDT", "MANA/USDT:USDT", "MANTA/USDT:USDT", "MASK/USDT:USDT", "MAV/USDT:USDT", "MBOX/USDT:USDT", "ME/USDT:USDT", "MEME/USDT:USDT", "METIS/USDT:USDT", "MEW/USDT:USDT", "MINA/USDT:USDT", "MKR/USDT:USDT", "MOCA/USDT:USDT", "MOODENG/USDT:USDT", "MORPHO/USDT:USDT", "MOVE/USDT:USDT", "MOVR/USDT:USDT", "MTL/USDT:USDT", "MYRO/USDT:USDT", "NEAR/USDT:USDT", "NEIRO/USDT:USDT", "NEIROETH/USDT:USDT", "NEO/USDT:USDT", "NFP/USDT:USDT", "NKN/USDT:USDT", "NMR/USDT:USDT", "NOT/USDT:USDT", "NTRN/USDT:USDT", "NULS/USDT:USDT", "OGN/USDT:USDT", "OM/USDT:USDT", "OMG/USDT:USDT", "OMNI/USDT:USDT", "ONDO/USDT:USDT", "ONE/USDT:USDT", "ONG/USDT:USDT", "ONT/USDT:USDT", "OP/USDT:USDT", "ORCA/USDT:USDT", "ORDI/USDT:USDT", "OXT/USDT:USDT", "PENDLE/USDT:USDT", "PENGU/USDT:USDT", "PEOPLE/USDT:USDT", "PERP/USDT:USDT", "PHA/USDT:USDT", "PHB/USDT:USDT", "PIXEL/USDT:USDT", "PNUT/USDT:USDT", "POL/USDT:USDT", "POLYX/USDT:USDT", "PONKE/USDT:USDT", "POPCAT/USDT:USDT", "PORTAL/USDT:USDT", "POWR/USDT:USDT", "PYTH/USDT:USDT", "QNT/USDT:USDT", "QTUM/USDT:USDT", "QUICK/USDT:USDT", "RARE/USDT:USDT", "RAYSOL/USDT:USDT", "RDNT/USDT:USDT", "REEF/USDT:USDT", "REI/USDT:USDT", "RENDER/USDT:USDT", "REZ/USDT:USDT", "RIF/USDT:USDT", "RLC/USDT:USDT", "RONIN/USDT:USDT", "ROSE/USDT:USDT", "RPL/USDT:USDT", "RSR/USDT:USDT", "RUNE/USDT:USDT", "RVN/USDT:USDT", "SAFE/USDT:USDT", "SAGA/USDT:USDT", "SAND/USDT:USDT", "SANTOS/USDT:USDT", "SCR/USDT:USDT", "SCRT/USDT:USDT", "SEI/USDT:USDT", "SFP/USDT:USDT", "SKL/USDT:USDT", "SLERF/USDT:USDT", "SNX/USDT:USDT", "SOL/USDT:USDT", "SPELL/USDT:USDT", "SPX/USDT:USDT", "SSV/USDT:USDT", "STEEM/USDT:USDT", "STG/USDT:USDT", "STMX/USDT:USDT", "STORJ/USDT:USDT", "STRK/USDT:USDT", "STX/USDT:USDT", "SUI/USDT:USDT", "SUN/USDT:USDT", "SUPER/USDT:USDT", "SUSHI/USDT:USDT", "SWELL/USDT:USDT", "SXP/USDT:USDT", "SYN/USDT:USDT", "SYS/USDT:USDT", "T/USDT:USDT", "TAO/USDT:USDT", "THE/USDT:USDT", "THETA/USDT:USDT", "TIA/USDT:USDT", "TLM/USDT:USDT", "TNSR/USDT:USDT", "TOKEN/USDT:USDT", "TON/USDT:USDT", "TRB/USDT:USDT", "TROY/USDT:USDT", "TRU/USDT:USDT", "TRX/USDT:USDT", "TURBO/USDT:USDT", "TWT/USDT:USDT", "UMA/USDT:USDT", "UNI/USDT:USDT", "USTC/USDT:USDT", "USUAL/USDT:USDT", "UXLINK/USDT:USDT", "VANA/USDT:USDT", "VANRY/USDT:USDT", "VELODROME/USDT:USDT", "VET/USDT:USDT", "VIDT/USDT:USDT", "VIRTUAL/USDT:USDT", "VOXEL/USDT:USDT", "W/USDT:USDT", "WAXP/USDT:USDT", "WIF/USDT:USDT", "WLD/USDT:USDT", "WOO/USDT:USDT", "XAI/USDT:USDT", "XLM/USDT:USDT", "XMR/USDT:USDT", "XRP/USDT:USDT", "XTZ/USDT:USDT", "XVG/USDT:USDT", "XVS/USDT:USDT", "YFI/USDT:USDT", "YGG/USDT:USDT", "ZEC/USDT:USDT", "ZEN/USDT:USDT", "ZEREBRO/USDT:USDT", "ZETA/USDT:USDT", "ZIL/USDT:USDT", "ZK/USDT:USDT", "ZRO/USDT:USDT", "ZRX/USDT:USDT"]}, "pairlists": [{"method": "StaticPairList"}], "entry_pricing": {"use_order_book": true, "order_book_top": 1, "check_depth_of_market": {"enabled": false, "bids_to_ask_delta": 1}}, "exit_pricing": {"use_order_book": true, "order_book_top": 1}}