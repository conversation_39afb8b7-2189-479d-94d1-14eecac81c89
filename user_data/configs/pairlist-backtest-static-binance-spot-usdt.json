{"exchange": {"name": "binance", "pair_whitelist": ["1INCH/USDT", "ADA/USDT", "ADX/USDT", "AGLD/USDT", "ALGO/USDT", "ALICE/USDT", "ALPACA/USDT", "ALPHA/USDT", "AR/USDT", "ARPA/USDT", "ATM/USDT", "ATOM/USDT", "AUCTION/USDT", "AUDIO/USDT", "AVA/USDT", "AVAX/USDT", "AXS/USDT", "BADGER/USDT", "BAKE/USDT", "BAND/USDT", "BAR/USDT", "BCH/USDT", "BEL/USDT", "BETA/USDT", "BNT/USDT", "BNX/USDT", "BTC/USDT", "BURGER/USDT", "C98/USDT", "CAKE/USDT", "CELR/USDT", "CFX/USDT", "CHR/USDT", "CHZ/USDT", "CKB/USDT", "COTI/USDT", "CRV/USDT", "DASH/USDT", "DATA/USDT", "DEGO/USDT", "DEXE/USDT", "DF/USDT", "DGB/USDT", "DODO/USDT", "DOGE/USDT", "DOT/USDT", "DYDX/USDT", "EGLD/USDT", "ENJ/USDT", "ENS/USDT", "EOS/USDT", "ERN/USDT", "ETC/USDT", "ETH/USDT", "FARM/USDT", "FET/USDT", "FIL/USDT", "FIS/USDT", "FLM/USDT", "FLOW/USDT", "FLUX/USDT", "FORTH/USDT", "FTM/USDT", "FUN/USDT", "FXS/USDT", "GALA/USDT", "GRT/USDT", "GTC/USDT", "HARD/USDT", "HIGH/USDT", "HIVE/USDT", "HOT/USDT", "ICP/USDT", "IDEX/USDT", "INJ/USDT", "IOTA/USDT", "IOTX/USDT", "KMD/USDT", "KSM/USDT", "LINA/USDT", "LINK/USDT", "LIT/USDT", "LRC/USDT", "LTC/USDT", "LTO/USDT", "MANA/USDT", "MASK/USDT", "MBL/USDT", "MBOX/USDT", "MDT/USDT", "MINA/USDT", "MKR/USDT", "MLN/USDT", "MOVR/USDT", "MTL/USDT", "NEAR/USDT", "NEO/USDT", "NKN/USDT", "OG/USDT", "OM/USDT", "ONE/USDT", "ONT/USDT", "PEOPLE/USDT", "PERP/USDT", "PHA/USDT", "POND/USDT", "PUNDIX/USDT", "PYR/USDT", "QI/USDT", "QNT/USDT", "QTUM/USDT", "RAD/USDT", "RARE/USDT", "RAY/USDT", "REQ/USDT", "RIF/USDT", "RLC/USDT", "ROSE/USDT", "RUNE/USDT", "SAND/USDT", "SANTOS/USDT", "SFP/USDT", "SHIB/USDT", "SKL/USDT", "SLP/USDT", "SNX/USDT", "SOL/USDT", "SPELL/USDT", "STORJ/USDT", "STPT/USDT", "STRAX/USDT", "STX/USDT", "SUPER/USDT", "SUSHI/USDT", "SXP/USDT", "TFUEL/USDT", "THETA/USDT", "TKO/USDT", "TLM/USDT", "TRU/USDT", "TRX/USDT", "TWT/USDT", "UMA/USDT", "UNI/USDT", "VET/USDT", "VIDT/USDT", "VTHO/USDT", "WAXP/USDT", "WIN/USDT", "WING/USDT", "XEC/USDT", "XLM/USDT", "XRP/USDT", "XTZ/USDT", "XVG/USDT", "XVS/USDT", "YFI/USDT", "YGG/USDT", "ZEN/USDT", "ZRX/USDT"]}, "pairlists": [{"method": "StaticPairList"}]}