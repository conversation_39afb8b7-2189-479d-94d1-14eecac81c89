
import requests
import json
from pathlib import Path
from datetime import datetime, timezone

# --- Configuration ---
# You will need to find the correct API endpoint from the Hyperliquid documentation
HYPERLIQUID_API_ENDPOINT = "https://api.hyperliquid.com/info" # <-- IMPORTANT: Replace with actual endpoint if different
DATA_DIR = Path("user_data/data/hyperliquid")

# --- Main Function ---
def fetch_and_save_data(pair: str, timeframe: str, start_date_str: str):
    """
    Fetches historical OHLCV data from Hyperliquid and saves it in Freqtrade format.
    """
    print(f"Fetching data for {pair} on timeframe {timeframe} since {start_date_str}...")

    # Create the data directory if it doesn't exist
    DATA_DIR.mkdir(parents=True, exist_ok=True)

    # Convert start date to timestamp in milliseconds
    start_dt = datetime.strptime(start_date_str, "%Y-%m-%d").replace(tzinfo=timezone.utc)
    start_timestamp_ms = int(start_dt.timestamp() * 1000)

    # --- Step 1: Make the API Request ---
    # This part is HIGHLY dependent on the Hyperliquid API.
    # You MUST consult their documentation for the correct parameters.
    # The following is a hypothetical example.
    payload = {
        "type": "candles",
        "coin": pair.split('/')[0], # e.g., 'BTC'
        "interval": timeframe, # e.g., '5m'
        "startTime": start_timestamp_ms
    }
    
    try:
        # It's common for APIs to return JSON directly
        response = requests.post(HYPERLIQUID_API_ENDPOINT, json=payload)
        response.raise_for_status()  # Raises an exception for bad status codes (4xx or 5xx)
        api_data = response.json()

    except requests.exceptions.RequestException as e:
        print(f"Error fetching data from API: {e}")
        return
    except json.JSONDecodeError:
        print(f"Error decoding JSON from response: {response.text}")
        return

    # --- Step 2: Format the Data ---
    # The structure of `api_data` will depend on the Hyperliquid API.
    # You need to loop through their response and build the Freqtrade-compatible list of lists.
    # Let's assume their response is a list of dictionaries like:
    # [{"t": 1640995200000, "o": 47000.0, "h": 47100.0, "l": 46900.0, "c": 47050.0, "v": 100.0}, ...]
    
    freqtrade_data = []
    for candle in api_data:
        # Adapt these keys ('t', 'o', 'h', 'l', 'c', 'v') to match the actual API response
        try:
            timestamp = int(candle['t'])
            open_price = float(candle['o'])
            high_price = float(candle['h'])
            low_price = float(candle['l'])
            close_price = float(candle['c'])
            volume = float(candle['v'])
            
            freqtrade_data.append([timestamp, open_price, high_price, low_price, close_price, volume])
        except (KeyError, TypeError) as e:
            print(f"Skipping candle due to formatting error: {e}. Raw candle data: {candle}")
            continue

    if not freqtrade_data:
        print("No data was formatted. Please check the API response and the formatting logic.")
        return

    # --- Step 3: Save the File ---
    # Sanitize the pair name for the filename (e.g., "BTC/USDT" -> "BTC_USDT")
    safe_pair_name = pair.replace("/", "_")
    file_path = DATA_DIR / f"{safe_pair_name}-{timeframe}.json"

    try:
        with open(file_path, "w") as f:
            json.dump(freqtrade_data, f)
        print(f"Successfully saved data to {file_path}")
    except IOError as e:
        print(f"Error saving data to file: {e}")


if __name__ == "__main__":
    # --- Example Usage ---
    # Customize these values for your needs
    target_pair = "ETH/USDT"
    target_timeframe = "5m"
    target_start_date = "2024-01-01" # YYYY-MM-DD

    fetch_and_save_data(target_pair, target_timeframe, target_start_date)
