import{_ as v}from"./plus-box-outline-CDxaZbJP.js";import{d as g,aV as h,a5 as x,c as n,a as o,e as u,b as l,F as V,m as w,s as k,g as y,h as r,B,H as C}from"./index-Cwqm8wBn.js";const $={class:"flex flex-row gap-2"},L={class:"flex gap-1 flex-col w-full"},F=g({__name:"BaseStringList",props:h({placeholder:{default:""},size:{default:"small"}},{modelValue:{required:!0},modelModifiers:{}}),emits:["update:modelValue"],setup(s){const e=x(s,"modelValue");return(d,a)=>{const m=k,_=B,i=y,p=v;return o(),n("div",$,[u("div",L,[(o(!0),n(V,null,w(e.value,(f,t)=>(o(),n("div",{key:t,class:"flex flex-row gap-1"},[l(m,{modelValue:e.value[t],"onUpdate:modelValue":c=>e.value[t]=c,size:"small",class:"w-full",placeholder:d.placeholder},null,8,["modelValue","onUpdate:modelValue","placeholder"]),l(i,{severity:"secondary",variant:"outlined",title:"Delete this value.",class:"flex align-items-center justify-content-center",onClick:c=>e.value.splice(t,1)},{icon:r(()=>[l(_)]),_:2},1032,["onClick"])]))),128))]),l(i,{title:"Add new value",severity:"secondary",class:"mt-auto flex align-items-center justify-content-center",onClick:a[0]||(a[0]=f=>e.value.push(""))},{icon:r(()=>[l(p)]),_:1})])}}}),M={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function z(s,e){return o(),n("svg",M,e[0]||(e[0]=[u("path",{fill:"currentColor",d:"M7.41 15.41L12 10.83l4.59 4.58L18 14l-6-6l-6 6z"},null,-1)]))}const N=C({name:"mdi-chevron-up",render:z});export{F as _,N as a};
//# sourceMappingURL=chevron-up-DmDiFdj7.js.map
