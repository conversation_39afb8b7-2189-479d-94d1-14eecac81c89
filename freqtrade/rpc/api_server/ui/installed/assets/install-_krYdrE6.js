import{bv as _,b$ as ke,aB as Ce,r as We,x as ga,bY as Ve,bL as Be,cU as Se,g as fa,aP as ca,cv as ua,en as ha,eo as da,bZ as se,am as ve,B as Ye,ay as pa,T as Xe,a5 as ya,ar as Me,a2 as X,ep as ma,o as Te,eq as ba,er as $e,es as La,j as oe,et as xa,dl as Sa,s as le,aT as ze,v as pe,bt as Ma,bo as Ta,bi as Aa,aF as wa,q as Ue,a4 as Je,_ as Ie,bp as Ke,z as $,bu as Da,H as Pa,aZ as Ca,bX as ce,t as Ia,y as _a,F as Na,C as Oa,D as Ra,b9 as Ea,L as je,M as Ga,aw as Qe,ax as ka,eu as Va,aG as Ba,S as Ya,aJ as Xa}from"./installCanvasRenderer-SA1tPojE.js";var ie=Math.PI*2,W=ua.CMD,za=["top","right","bottom","left"];function Ua(n,i,t,e,a){var o=t.width,r=t.height;switch(n){case"top":e.set(t.x+o/2,t.y-i),a.set(0,-1);break;case"bottom":e.set(t.x+o/2,t.y+r+i),a.set(0,1);break;case"left":e.set(t.x-i,t.y+r/2),a.set(-1,0);break;case"right":e.set(t.x+o+i,t.y+r/2),a.set(1,0);break}}function ja(n,i,t,e,a,o,r,l,g){r-=n,l-=i;var f=Math.sqrt(r*r+l*l);r/=f,l/=f;var s=r*t+n,h=l*t+i;if(Math.abs(e-a)%ie<1e-4)return g[0]=s,g[1]=h,f-t;if(o){var u=e;e=se(a),a=se(u)}else e=se(e),a=se(a);e>a&&(a+=ie);var v=Math.atan2(l,r);if(v<0&&(v+=ie),v>=e&&v<=a||v+ie>=e&&v+ie<=a)return g[0]=s,g[1]=h,f-t;var c=t*Math.cos(e)+n,d=t*Math.sin(e)+i,p=t*Math.cos(a)+n,y=t*Math.sin(a)+i,L=(c-r)*(c-r)+(d-l)*(d-l),m=(p-r)*(p-r)+(y-l)*(y-l);return L<m?(g[0]=c,g[1]=d,Math.sqrt(L)):(g[0]=p,g[1]=y,Math.sqrt(m))}function ye(n,i,t,e,a,o,r,l){var g=a-n,f=o-i,s=t-n,h=e-i,u=Math.sqrt(s*s+h*h);s/=u,h/=u;var v=g*s+f*h,c=v/u;l&&(c=Math.min(Math.max(c,0),1)),c*=u;var d=r[0]=n+c*s,p=r[1]=i+c*h;return Math.sqrt((d-a)*(d-a)+(p-o)*(p-o))}function ea(n,i,t,e,a,o,r){t<0&&(n=n+t,t=-t),e<0&&(i=i+e,e=-e);var l=n+t,g=i+e,f=r[0]=Math.min(Math.max(a,n),l),s=r[1]=Math.min(Math.max(o,i),g);return Math.sqrt((f-a)*(f-a)+(s-o)*(s-o))}var Y=[];function qa(n,i,t){var e=ea(i.x,i.y,i.width,i.height,n.x,n.y,Y);return t.set(Y[0],Y[1]),e}function Fa(n,i,t){for(var e=0,a=0,o=0,r=0,l,g,f=1/0,s=i.data,h=n.x,u=n.y,v=0;v<s.length;){var c=s[v++];v===1&&(e=s[v],a=s[v+1],o=e,r=a);var d=f;switch(c){case W.M:o=s[v++],r=s[v++],e=o,a=r;break;case W.L:d=ye(e,a,s[v],s[v+1],h,u,Y,!0),e=s[v++],a=s[v++];break;case W.C:d=da(e,a,s[v++],s[v++],s[v++],s[v++],s[v],s[v+1],h,u,Y),e=s[v++],a=s[v++];break;case W.Q:d=ha(e,a,s[v++],s[v++],s[v],s[v+1],h,u,Y),e=s[v++],a=s[v++];break;case W.A:var p=s[v++],y=s[v++],L=s[v++],m=s[v++],S=s[v++],M=s[v++];v+=1;var D=!!(1-s[v++]);l=Math.cos(S)*L+p,g=Math.sin(S)*m+y,v<=1&&(o=l,r=g);var C=(h-p)*m/L+p;d=ja(p,y,m,S,S+M,D,C,u,Y),e=Math.cos(S+M)*L+p,a=Math.sin(S+M)*m+y;break;case W.R:o=e=s[v++],r=a=s[v++];var b=s[v++],T=s[v++];d=ea(o,r,b,T,h,u,Y);break;case W.Z:d=ye(e,a,o,r,h,u,Y,!0),e=o,a=r;break}d<f&&(f=d,t.set(Y[0],Y[1]))}return f}var z=new _,x=new _,w=new _,q=new _,j=new _;function qe(n,i){if(n){var t=n.getTextGuideLine(),e=n.getTextContent();if(e&&t){var a=n.textGuideLineConfig||{},o=[[0,0],[0,0],[0,0]],r=a.candidates||za,l=e.getBoundingRect().clone();l.applyTransform(e.getComputedTransform());var g=1/0,f=a.anchor,s=n.getComputedTransform(),h=s&&fa([],s),u=i.get("length2")||0;f&&w.copy(f);for(var v=0;v<r.length;v++){var c=r[v];Ua(c,0,l,z,q),_.scaleAndAdd(x,z,q,u),x.transform(h);var d=n.getBoundingRect(),p=f?f.distance(x):n instanceof ca?Fa(x,n.path,w):qa(x,d,w);p<g&&(g=p,x.transform(s),w.transform(s),w.toArray(o[0]),x.toArray(o[1]),z.toArray(o[2]))}aa(o,i.get("minTurnAngle")),t.setShape({points:o})}}}var me=[],R=new _;function aa(n,i){if(i<=180&&i>0){i=i/180*Math.PI,z.fromArray(n[0]),x.fromArray(n[1]),w.fromArray(n[2]),_.sub(q,z,x),_.sub(j,w,x);var t=q.len(),e=j.len();if(!(t<.001||e<.001)){q.scale(1/t),j.scale(1/e);var a=q.dot(j),o=Math.cos(i);if(o<a){var r=ye(x.x,x.y,w.x,w.y,z.x,z.y,me,!1);R.fromArray(me),R.scaleAndAdd(j,r/Math.tan(Math.PI-i));var l=w.x!==x.x?(R.x-x.x)/(w.x-x.x):(R.y-x.y)/(w.y-x.y);if(isNaN(l))return;l<0?_.copy(R,x):l>1&&_.copy(R,w),R.toArray(n[1])}}}}function Ha(n,i,t){if(t<=180&&t>0){t=t/180*Math.PI,z.fromArray(n[0]),x.fromArray(n[1]),w.fromArray(n[2]),_.sub(q,x,z),_.sub(j,w,x);var e=q.len(),a=j.len();if(!(e<.001||a<.001)){q.scale(1/e),j.scale(1/a);var o=q.dot(i),r=Math.cos(t);if(o<r){var l=ye(x.x,x.y,w.x,w.y,z.x,z.y,me,!1);R.fromArray(me);var g=Math.PI/2,f=Math.acos(j.dot(i)),s=g+f-t;if(s>=g)_.copy(R,w);else{R.scaleAndAdd(j,l/Math.tan(Math.PI/2-s));var h=w.x!==x.x?(R.x-x.x)/(w.x-x.x):(R.y-x.y)/(w.y-x.y);if(isNaN(h))return;h<0?_.copy(R,x):h>1&&_.copy(R,w)}R.toArray(n[1])}}}}function Ae(n,i,t,e){var a=t==="normal",o=a?n:n.ensureState(t);o.ignore=i;var r=e.get("smooth");r&&r===!0&&(r=.3),o.shape=o.shape||{},r>0&&(o.shape.smooth=r);var l=e.getModel("lineStyle").getLineStyle();a?n.useStyle(l):o.style=l}function Za(n,i){var t=i.smooth,e=i.points;if(e)if(n.moveTo(e[0][0],e[0][1]),t>0&&e.length>=3){var a=Be(e[0],e[1]),o=Be(e[1],e[2]);if(!a||!o){n.lineTo(e[1][0],e[1][1]),n.lineTo(e[2][0],e[2][1]);return}var r=Math.min(a,o)*t,l=Se([],e[1],e[0],r/a),g=Se([],e[1],e[2],r/o),f=Se([],l,g,.5);n.bezierCurveTo(l[0],l[1],l[0],l[1],f[0],f[1]),n.bezierCurveTo(g[0],g[1],g[0],g[1],e[2][0],e[2][1])}else for(var s=1;s<e.length;s++)n.lineTo(e[s][0],e[s][1])}function ta(n,i,t){var e=n.getTextGuideLine(),a=n.getTextContent();if(!a){e&&n.removeTextGuideLine();return}for(var o=i.normal,r=o.get("show"),l=a.ignore,g=0;g<ke.length;g++){var f=ke[g],s=i[f],h=f==="normal";if(s){var u=s.get("show"),v=h?l:Ce(a.states[f]&&a.states[f].ignore,l);if(v||!Ce(u,r)){var c=h?e:e&&e.states[f];c&&(c.ignore=!0),e&&Ae(e,!0,f,s);continue}e||(e=new We,n.setTextGuideLine(e),!h&&(l||!r)&&Ae(e,!0,"normal",i.normal),n.stateProxy&&(e.stateProxy=n.stateProxy)),Ae(e,!1,f,s)}}if(e){ga(e.style,t),e.style.fill=null;var d=o.get("showAbove"),p=n.textGuideLineConfig=n.textGuideLineConfig||{};p.showAbove=d||!1,e.buildPath=Za}}function ra(n,i){i=i||"labelLine";for(var t={normal:n.getModel(i)},e=0;e<Ve.length;e++){var a=Ve[e];t[a]=n.getModel([a,i])}return t}function Wa(n){if(n){for(var i=[],t=0;t<n.length;t++)i.push(n[t].slice());return i}}function $a(n,i){var t=n.label,e=i&&i.getTextGuideLine();return{dataIndex:n.dataIndex,dataType:n.dataType,seriesIndex:n.seriesModel.seriesIndex,text:n.label.style.text,rect:n.hostRect,labelRect:n.rect,align:t.style.align,verticalAlign:t.style.verticalAlign,labelLinePoints:Wa(e&&e.shape.points)}}var Fe=["align","verticalAlign","width","height","fontSize"],O=new pa,we=ve(),Ja=ve();function ue(n,i,t){for(var e=0;e<t.length;e++){var a=t[e];i[a]!=null&&(n[a]=i[a])}}var he=["x","y","rotation"],Ka=function(){function n(){this._labelList=[],this._chartViewList=[]}return n.prototype.clearLabels=function(){this._labelList=[],this._chartViewList=[]},n.prototype._addLabel=function(i,t,e,a,o){var r=a.style,l=a.__hostTarget,g=l.textConfig||{},f=a.getComputedTransform(),s=a.getBoundingRect().plain();Ye.applyTransform(s,s,f),f?O.setLocalTransform(f):(O.x=O.y=O.rotation=O.originX=O.originY=0,O.scaleX=O.scaleY=1),O.rotation=se(O.rotation);var h=a.__hostTarget,u;if(h){u=h.getBoundingRect().plain();var v=h.getComputedTransform();Ye.applyTransform(u,u,v)}var c=u&&h.getTextGuideLine();this._labelList.push({label:a,labelLine:c,seriesModel:e,dataIndex:i,dataType:t,layoutOption:o,computedLayoutOption:null,rect:s,hostRect:u,priority:u?u.width*u.height:0,defaultAttr:{ignore:a.ignore,labelGuideIgnore:c&&c.ignore,x:O.x,y:O.y,scaleX:O.scaleX,scaleY:O.scaleY,rotation:O.rotation,style:{x:r.x,y:r.y,align:r.align,verticalAlign:r.verticalAlign,width:r.width,height:r.height,fontSize:r.fontSize},cursor:a.cursor,attachedPos:g.position,attachedRot:g.rotation}})},n.prototype.addLabelsOfSeries=function(i){var t=this;this._chartViewList.push(i);var e=i.__model,a=e.get("labelLayout");(Xe(a)||ya(a).length)&&i.group.traverse(function(o){if(o.ignore)return!0;var r=o.getTextContent(),l=Me(o);r&&!r.disableLabelLayout&&t._addLabel(l.dataIndex,l.dataType,e,r,a)})},n.prototype.updateLayoutConfig=function(i){var t=i.getWidth(),e=i.getHeight();function a(m,S){return function(){qe(m,S)}}for(var o=0;o<this._labelList.length;o++){var r=this._labelList[o],l=r.label,g=l.__hostTarget,f=r.defaultAttr,s=void 0;Xe(r.layoutOption)?s=r.layoutOption($a(r,g)):s=r.layoutOption,s=s||{},r.computedLayoutOption=s;var h=Math.PI/180;g&&g.setTextConfig({local:!1,position:s.x!=null||s.y!=null?null:f.attachedPos,rotation:s.rotate!=null?s.rotate*h:f.attachedRot,offset:[s.dx||0,s.dy||0]});var u=!1;if(s.x!=null?(l.x=X(s.x,t),l.setStyle("x",0),u=!0):(l.x=f.x,l.setStyle("x",f.style.x)),s.y!=null?(l.y=X(s.y,e),l.setStyle("y",0),u=!0):(l.y=f.y,l.setStyle("y",f.style.y)),s.labelLinePoints){var v=g.getTextGuideLine();v&&(v.setShape({points:s.labelLinePoints}),u=!1)}var c=we(l);c.needsUpdateLabelLine=u,l.rotation=s.rotate!=null?s.rotate*h:f.rotation,l.scaleX=f.scaleX,l.scaleY=f.scaleY;for(var d=0;d<Fe.length;d++){var p=Fe[d];l.setStyle(p,s[p]!=null?s[p]:f.style[p])}if(s.draggable){if(l.draggable=!0,l.cursor="move",g){var y=r.seriesModel;if(r.dataIndex!=null){var L=r.seriesModel.getData(r.dataType);y=L.getItemModel(r.dataIndex)}l.on("drag",a(g,y.getModel("labelLine")))}}else l.off("drag"),l.cursor=f.cursor}},n.prototype.layout=function(i){var t=i.getWidth(),e=i.getHeight(),a=ma(this._labelList),o=Te(a,function(g){return g.layoutOption.moveOverlap==="shiftX"}),r=Te(a,function(g){return g.layoutOption.moveOverlap==="shiftY"});ba(o,0,t),$e(r,0,e);var l=Te(a,function(g){return g.layoutOption.hideOverlap});La(l)},n.prototype.processLabelsOverall=function(){var i=this;oe(this._chartViewList,function(t){var e=t.__model,a=t.ignoreLabelLineUpdate,o=e.isAnimationEnabled();t.group.traverse(function(r){if(r.ignore&&!r.forceLabelAnimation)return!0;var l=!a,g=r.getTextContent();!l&&g&&(l=we(g).needsUpdateLabelLine),l&&i._updateLabelLine(r,e),o&&i._animateLabels(r,e)})})},n.prototype._updateLabelLine=function(i,t){var e=i.getTextContent(),a=Me(i),o=a.dataIndex;if(e&&o!=null){var r=t.getData(a.dataType),l=r.getItemModel(o),g={},f=r.getItemVisual(o,"style");if(f){var s=r.getVisual("drawType");g.stroke=f[s]}var h=l.getModel("labelLine");ta(i,ra(l),g),qe(i,h)}},n.prototype._animateLabels=function(i,t){var e=i.getTextContent(),a=i.getTextGuideLine();if(e&&(i.forceLabelAnimation||!e.ignore&&!e.invisible&&!i.disableLabelAnimation&&!xa(i))){var o=we(e),r=o.oldLayout,l=Me(i),g=l.dataIndex,f={x:e.x,y:e.y,rotation:e.rotation},s=t.getData(l.dataType);if(r){e.attr(r);var u=i.prevStates;u&&(ze(u,"select")>=0&&e.attr(o.oldLayoutSelect),ze(u,"emphasis")>=0&&e.attr(o.oldLayoutEmphasis)),pe(e,f,t,g)}else if(e.attr(f),!Sa(e).valueAnimation){var h=Ce(e.style.opacity,1);e.style.opacity=0,le(e,{style:{opacity:h}},t,g)}if(o.oldLayout=f,e.states.select){var v=o.oldLayoutSelect={};ue(v,f,he),ue(v,e.states.select,he)}if(e.states.emphasis){var c=o.oldLayoutEmphasis={};ue(c,f,he),ue(c,e.states.emphasis,he)}Ma(e,g,s,t,t)}if(a&&!a.ignore&&!a.invisible){var o=Ja(a),r=o.oldLayout,d={points:a.shape.points};r?(a.attr({shape:r}),pe(a,{shape:d},t)):(a.setShape(d),a.style.strokePercent=0,le(a,{style:{strokePercent:1}},t)),o.oldLayout=d}},n}(),De=ve();function ft(n){n.registerUpdateLifecycle("series:beforeupdate",function(i,t,e){var a=De(t).labelManager;a||(a=De(t).labelManager=new Ka),a.clearLabels()}),n.registerUpdateLifecycle("series:layoutlabels",function(i,t,e){var a=De(t).labelManager;e.updatedSeries.forEach(function(o){a.addLabelsOfSeries(t.getViewOfSeriesModel(o))}),a.updateLayoutConfig(t),a.layout(t),a.processLabelsOverall()})}var He=Math.PI*2,de=Math.PI/180;function na(n,i){return wa(n.getBoxLayoutParams(),{width:i.getWidth(),height:i.getHeight()})}function ia(n,i){var t=na(n,i),e=n.get("center"),a=n.get("radius");Ue(a)||(a=[0,a]);var o=X(t.width,i.getWidth()),r=X(t.height,i.getHeight()),l=Math.min(o,r),g=X(a[0],l/2),f=X(a[1],l/2),s,h,u=n.coordinateSystem;if(u){var v=u.dataToPoint(e);s=v[0]||0,h=v[1]||0}else Ue(e)||(e=[e,e]),s=X(e[0],o)+t.x,h=X(e[1],r)+t.y;return{cx:s,cy:h,r0:g,r:f}}function Qa(n,i,t){i.eachSeriesByType(n,function(e){var a=e.getData(),o=a.mapDimension("value"),r=na(e,t),l=ia(e,t),g=l.cx,f=l.cy,s=l.r,h=l.r0,u=-e.get("startAngle")*de,v=e.get("endAngle"),c=e.get("padAngle")*de;v=v==="auto"?u-He:-v*de;var d=e.get("minAngle")*de,p=d+c,y=0;a.each(o,function(V){!isNaN(V)&&y++});var L=a.getSum(o),m=Math.PI/(L||y)*2,S=e.get("clockwise"),M=e.get("roseType"),D=e.get("stillShowZeroSum"),C=a.getDataExtent(o);C[0]=0;var b=S?1:-1,T=[u,v],G=b*c/2;Ta(T,!S),u=T[0],v=T[1];var H=sa(e);H.startAngle=u,H.endAngle=v,H.clockwise=S;var U=Math.abs(v-u),B=U,J=0,k=u;if(a.setLayout({viewRect:r,r:s}),a.each(o,function(V,E){var A;if(isNaN(V)){a.setItemLayout(E,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:S,cx:g,cy:f,r0:h,r:M?NaN:s});return}M!=="area"?A=L===0&&D?m:V*m:A=U/y,A<p?(A=p,B-=p):J+=V;var N=k+b*A,I=0,P=0;c>A?(I=k+b*A/2,P=I):(I=k+G,P=N-G),a.setItemLayout(E,{angle:A,startAngle:I,endAngle:P,clockwise:S,cx:g,cy:f,r0:h,r:M?Aa(V,C,[h,s]):s}),k=N}),B<He&&y)if(B<=.001){var F=U/y;a.each(o,function(V,E){if(!isNaN(V)){var A=a.getItemLayout(E);A.angle=F;var N=0,I=0;F<c?(N=u+b*(E+1/2)*F,I=N):(N=u+b*E*F+G,I=u+b*(E+1)*F-G),A.startAngle=N,A.endAngle=I}})}else m=B/J,k=u,a.each(o,function(V,E){if(!isNaN(V)){var A=a.getItemLayout(E),N=A.angle===p?p:V*m,I=0,P=0;N<c?(I=k+b*N/2,P=I):(I=k+G,P=k+b*N-G),A.startAngle=I,A.endAngle=P,k+=b*N}})})}var sa=ve();function et(n){return{seriesType:n,reset:function(i,t){var e=t.findComponents({mainType:"legend"});if(!(!e||!e.length)){var a=i.getData();a.filterSelf(function(o){for(var r=a.getName(o),l=0;l<e.length;l++)if(!e[l].isSelected(r))return!1;return!0})}}}}var at=Math.PI/180;function Ze(n,i,t,e,a,o,r,l,g,f){if(n.length<2)return;function s(d){for(var p=d.rB,y=p*p,L=0;L<d.list.length;L++){var m=d.list[L],S=Math.abs(m.label.y-t),M=e+m.len,D=M*M,C=Math.sqrt(Math.abs((1-S*S/y)*D)),b=i+(C+m.len2)*a,T=b-m.label.x,G=m.targetTextWidth-T*a;oa(m,G,!0),m.label.x=b}}function h(d){for(var p={list:[],maxY:0},y={list:[],maxY:0},L=0;L<d.length;L++)if(d[L].labelAlignTo==="none"){var m=d[L],S=m.label.y>t?y:p,M=Math.abs(m.label.y-t);if(M>=S.maxY){var D=m.label.x-i-m.len2*a,C=e+m.len,b=Math.abs(D)<C?Math.sqrt(M*M/(1-D*D/C/C)):C;S.rB=b,S.maxY=M}S.list.push(m)}s(p),s(y)}for(var u=n.length,v=0;v<u;v++)if(n[v].position==="outer"&&n[v].labelAlignTo==="labelLine"){var c=n[v].label.x-f;n[v].linePoints[1][0]+=c,n[v].label.x=f}$e(n,g,g+r)&&h(n)}function tt(n,i,t,e,a,o,r,l){for(var g=[],f=[],s=Number.MAX_VALUE,h=-Number.MAX_VALUE,u=0;u<n.length;u++){var v=n[u].label;Pe(n[u])||(v.x<i?(s=Math.min(s,v.x),g.push(n[u])):(h=Math.max(h,v.x),f.push(n[u])))}for(var u=0;u<n.length;u++){var c=n[u];if(!Pe(c)&&c.linePoints){if(c.labelStyleWidth!=null)continue;var v=c.label,d=c.linePoints,p=void 0;c.labelAlignTo==="edge"?v.x<i?p=d[2][0]-c.labelDistance-r-c.edgeDistance:p=r+a-c.edgeDistance-d[2][0]-c.labelDistance:c.labelAlignTo==="labelLine"?v.x<i?p=s-r-c.bleedMargin:p=r+a-h-c.bleedMargin:v.x<i?p=v.x-r-c.bleedMargin:p=r+a-v.x-c.bleedMargin,c.targetTextWidth=p,oa(c,p)}}Ze(f,i,t,e,1,a,o,r,l,h),Ze(g,i,t,e,-1,a,o,r,l,s);for(var u=0;u<n.length;u++){var c=n[u];if(!Pe(c)&&c.linePoints){var v=c.label,d=c.linePoints,y=c.labelAlignTo==="edge",L=v.style.padding,m=L?L[1]+L[3]:0,S=v.style.backgroundColor?0:m,M=c.rect.width+S,D=d[1][0]-d[2][0];y?v.x<i?d[2][0]=r+c.edgeDistance+M+c.labelDistance:d[2][0]=r+a-c.edgeDistance-M-c.labelDistance:(v.x<i?d[2][0]=v.x+c.labelDistance:d[2][0]=v.x-c.labelDistance,d[1][0]=d[2][0]+D),d[1][1]=d[2][1]=v.y}}}function oa(n,i,t){if(t===void 0&&(t=!1),n.labelStyleWidth==null){var e=n.label,a=e.style,o=n.rect,r=a.backgroundColor,l=a.padding,g=l?l[1]+l[3]:0,f=a.overflow,s=o.width+(r?0:g);if(i<s||t){var h=o.height;if(f&&f.match("break")){e.setStyle("backgroundColor",null),e.setStyle("width",i-g);var u=e.getBoundingRect();e.setStyle("width",Math.ceil(u.width)),e.setStyle("backgroundColor",r)}else{var v=i-g,c=i<s?v:t?v>n.unconstrainedWidth?null:v:null;e.setStyle("width",c)}var d=e.getBoundingRect();o.width=d.width;var p=(e.style.margin||0)+2.1;o.height=d.height+p,o.y-=(o.height-h)/2}}}function Pe(n){return n.position==="center"}function rt(n){var i=n.getData(),t=[],e,a,o=!1,r=(n.get("minShowLabelAngle")||0)*at,l=i.getLayout("viewRect"),g=i.getLayout("r"),f=l.width,s=l.x,h=l.y,u=l.height;function v(D){D.ignore=!0}function c(D){if(!D.ignore)return!0;for(var C in D.states)if(D.states[C].ignore===!1)return!0;return!1}i.each(function(D){var C=i.getItemGraphicEl(D),b=C.shape,T=C.getTextContent(),G=C.getTextGuideLine(),H=i.getItemModel(D),U=H.getModel("label"),B=U.get("position")||H.get(["emphasis","label","position"]),J=U.get("distanceToLabelLine"),k=U.get("alignTo"),F=X(U.get("edgeDistance"),f),V=U.get("bleedMargin"),E=H.getModel("labelLine"),A=E.get("length");A=X(A,f);var N=E.get("length2");if(N=X(N,f),Math.abs(b.endAngle-b.startAngle)<r){oe(T.states,v),T.ignore=!0,G&&(oe(G.states,v),G.ignore=!0);return}if(c(T)){var I=(b.startAngle+b.endAngle)/2,P=Math.cos(I),Z=Math.sin(I),ae,ge,_e,fe;e=b.cx,a=b.cy;var K=B==="inside"||B==="inner";if(B==="center")ae=b.cx,ge=b.cy,fe="center";else{var be=(K?(b.r+b.r0)/2*P:b.r*P)+e,Le=(K?(b.r+b.r0)/2*Z:b.r*Z)+a;if(ae=be+P*3,ge=Le+Z*3,!K){var Ne=be+P*(A+g-b.r),Oe=Le+Z*(A+g-b.r),Re=Ne+(P<0?-1:1)*N,Ee=Oe;k==="edge"?ae=P<0?s+F:s+f-F:ae=Re+(P<0?-J:J),ge=Ee,_e=[[be,Le],[Ne,Oe],[Re,Ee]]}fe=K?"center":k==="edge"?P>0?"right":"left":P>0?"left":"right"}var te=Math.PI,Q=0,re=U.get("rotate");if(Je(re))Q=re*(te/180);else if(B==="center")Q=0;else if(re==="radial"||re===!0){var la=P<0?-I+te:-I;Q=la}else if(re==="tangential"&&B!=="outside"&&B!=="outer"){var ee=Math.atan2(P,Z);ee<0&&(ee=te*2+ee);var va=Z>0;va&&(ee=te+ee),Q=ee-te}if(o=!!Q,T.x=ae,T.y=ge,T.rotation=Q,T.setStyle({verticalAlign:"middle"}),K){T.setStyle({align:fe});var xe=T.states.select;xe&&(xe.x+=T.x,xe.y+=T.y)}else{var ne=T.getBoundingRect().clone();ne.applyTransform(T.getComputedTransform());var Ge=(T.style.margin||0)+2.1;ne.y-=Ge/2,ne.height+=Ge,t.push({label:T,labelLine:G,position:B,len:A,len2:N,minTurnAngle:E.get("minTurnAngle"),maxSurfaceAngle:E.get("maxSurfaceAngle"),surfaceNormal:new _(P,Z),linePoints:_e,textAlign:fe,labelDistance:J,labelAlignTo:k,edgeDistance:F,bleedMargin:V,rect:ne,unconstrainedWidth:ne.width,labelStyleWidth:T.style.width})}C.setTextConfig({inside:K})}}),!o&&n.get("avoidLabelOverlap")&&tt(t,e,a,g,f,u,s,h);for(var d=0;d<t.length;d++){var p=t[d],y=p.label,L=p.labelLine,m=isNaN(y.x)||isNaN(y.y);if(y){y.setStyle({align:p.textAlign}),m&&(oe(y.states,v),y.ignore=!0);var S=y.states.select;S&&(S.x+=y.x,S.y+=y.y)}if(L){var M=p.linePoints;m||!M?(oe(L.states,v),L.ignore=!0):(aa(M,p.minTurnAngle),Ha(M,p.surfaceNormal,p.maxSurfaceAngle),L.setShape({points:M}),y.__hostTarget.textGuideLineConfig={anchor:new _(M[0][0],M[0][1])})}}}var nt=function(n){Ie(i,n);function i(t,e,a){var o=n.call(this)||this;o.z2=2;var r=new Ca;return o.setTextContent(r),o.updateData(t,e,a,!0),o}return i.prototype.updateData=function(t,e,a,o){var r=this,l=t.hostModel,g=t.getItemModel(e),f=g.getModel("emphasis"),s=t.getItemLayout(e),h=$(ce(g.getModel("itemStyle"),s,!0),s);if(isNaN(h.startAngle)){r.setShape(h);return}if(o){r.setShape(h);var u=l.getShallow("animationType");l.ecModel.ssr?(le(r,{scaleX:0,scaleY:0},l,{dataIndex:e,isFrom:!0}),r.originX=h.cx,r.originY=h.cy):u==="scale"?(r.shape.r=s.r0,le(r,{shape:{r:s.r}},l,e)):a!=null?(r.setShape({startAngle:a,endAngle:a}),le(r,{shape:{startAngle:s.startAngle,endAngle:s.endAngle}},l,e)):(r.shape.endAngle=s.startAngle,pe(r,{shape:{endAngle:s.endAngle}},l,e))}else Ia(r),pe(r,{shape:h},l,e);r.useStyle(t.getItemVisual(e,"style")),_a(r,g);var v=(s.startAngle+s.endAngle)/2,c=l.get("selectedOffset"),d=Math.cos(v)*c,p=Math.sin(v)*c,y=g.getShallow("cursor");y&&r.attr("cursor",y),this._updateLabel(l,t,e),r.ensureState("emphasis").shape=$({r:s.r+(f.get("scale")&&f.get("scaleSize")||0)},ce(f.getModel("itemStyle"),s)),$(r.ensureState("select"),{x:d,y:p,shape:ce(g.getModel(["select","itemStyle"]),s)}),$(r.ensureState("blur"),{shape:ce(g.getModel(["blur","itemStyle"]),s)});var L=r.getTextGuideLine(),m=r.getTextContent();L&&$(L.ensureState("select"),{x:d,y:p}),$(m.ensureState("select"),{x:d,y:p}),Na(this,f.get("focus"),f.get("blurScope"),f.get("disabled"))},i.prototype._updateLabel=function(t,e,a){var o=this,r=e.getItemModel(a),l=r.getModel("labelLine"),g=e.getItemVisual(a,"style"),f=g&&g.fill,s=g&&g.opacity;Oa(o,Ra(r),{labelFetcher:e.hostModel,labelDataIndex:a,inheritColor:f,defaultOpacity:s,defaultText:t.getFormattedLabel(a,"normal")||e.getName(a)});var h=o.getTextContent();o.setTextConfig({position:null,rotation:null}),h.attr({z2:10});var u=t.get(["label","position"]);if(u!=="outside"&&u!=="outer")o.removeTextGuideLine();else{var v=this.getTextGuideLine();v||(v=new We,this.setTextGuideLine(v)),ta(this,ra(r),{stroke:f,opacity:Ea(l.get(["lineStyle","opacity"]),s,1)})}},i}(Ke),it=function(n){Ie(i,n);function i(){var t=n!==null&&n.apply(this,arguments)||this;return t.ignoreLabelLineUpdate=!0,t}return i.prototype.render=function(t,e,a,o){var r=t.getData(),l=this._data,g=this.group,f;if(!l&&r.count()>0){for(var s=r.getItemLayout(0),h=1;isNaN(s&&s.startAngle)&&h<r.count();++h)s=r.getItemLayout(h);s&&(f=s.startAngle)}if(this._emptyCircleSector&&g.remove(this._emptyCircleSector),r.count()===0&&t.get("showEmptyCircle")){var u=sa(t),v=new Ke({shape:$(ia(t,a),u)});v.useStyle(t.getModel("emptyCircleStyle").getItemStyle()),this._emptyCircleSector=v,g.add(v)}r.diff(l).add(function(c){var d=new nt(r,c,f);r.setItemGraphicEl(c,d),g.add(d)}).update(function(c,d){var p=l.getItemGraphicEl(d);p.updateData(r,c,f),p.off("click"),g.add(p),r.setItemGraphicEl(c,p)}).remove(function(c){var d=l.getItemGraphicEl(c);Da(d,t,c)}).execute(),rt(t),t.get("animationTypeUpdate")!=="expansion"&&(this._data=r)},i.prototype.dispose=function(){},i.prototype.containPoint=function(t,e){var a=e.getData(),o=a.getItemLayout(0);if(o){var r=t[0]-o.cx,l=t[1]-o.cy,g=Math.sqrt(r*r+l*l);return g<=o.r&&g>=o.r0}},i.type="pie",i}(Pa),st=function(){function n(i,t){this._getDataWithEncodedVisual=i,this._getRawData=t}return n.prototype.getAllNames=function(){var i=this._getRawData();return i.mapArray(i.getName)},n.prototype.containName=function(i){var t=this._getRawData();return t.indexOfName(i)>=0},n.prototype.indexOfName=function(i){var t=this._getDataWithEncodedVisual();return t.indexOfName(i)},n.prototype.getItemVisual=function(i,t){var e=this._getDataWithEncodedVisual();return e.getItemVisual(i,t)},n}(),ot=ve(),lt=function(n){Ie(i,n);function i(){return n!==null&&n.apply(this,arguments)||this}return i.prototype.init=function(t){n.prototype.init.apply(this,arguments),this.legendVisualProvider=new st(je(this.getData,this),je(this.getRawData,this)),this._defaultLabelLine(t)},i.prototype.mergeOption=function(){n.prototype.mergeOption.apply(this,arguments)},i.prototype.getInitialData=function(){return Ga(this,{coordDimensions:["value"],encodeDefaulter:Qe(ka,this)})},i.prototype.getDataParams=function(t){var e=this.getData(),a=ot(e),o=a.seats;if(!o){var r=[];e.each(e.mapDimension("value"),function(g){r.push(g)}),o=a.seats=Va(r,e.hostModel.get("percentPrecision"))}var l=n.prototype.getDataParams.call(this,t);return l.percent=o[t]||0,l.$vars.push("percent"),l},i.prototype._defaultLabelLine=function(t){Ba(t,"labelLine",["show"]);var e=t.labelLine,a=t.emphasis.labelLine;e.show=e.show&&t.label.show,a.show=a.show&&t.emphasis.label.show},i.type="series.pie",i.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,endAngle:"auto",padAngle:0,minAngle:0,minShowLabelAngle:0,selectedOffset:10,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:0,show:!0,overflow:"truncate",position:"outer",alignTo:"none",edgeDistance:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,minTurnAngle:90,maxSurfaceAngle:90,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1,borderJoin:"round"},showEmptyCircle:!0,emptyCircleStyle:{color:"lightgray",opacity:1},labelLayout:{hideOverlap:!0},emphasis:{scale:!0,scaleSize:5},avoidLabelOverlap:!0,animationType:"expansion",animationDuration:1e3,animationTypeUpdate:"transition",animationEasingUpdate:"cubicInOut",animationDurationUpdate:500,animationEasing:"cubicInOut"},i}(Ya);function vt(n){return{seriesType:n,reset:function(i,t){var e=i.getData();e.filterSelf(function(a){var o=e.mapDimension("value"),r=e.get(o,a);return!(Je(r)&&!isNaN(r)&&r<0)})}}}function ct(n){n.registerChartView(it),n.registerSeriesModel(lt),Xa("pie",n.registerAction),n.registerLayout(Qe(Qa,"pie")),n.registerProcessor(et("pie")),n.registerProcessor(vt("pie"))}export{st as L,ft as a,et as d,ra as g,ct as i,ta as s};
//# sourceMappingURL=install-_krYdrE6.js.map
