import{d as l,j as s,b8 as P,bQ as u,bR as g,c as p,a as c,f as i,z as d,e as _,n as m,t as h,D as b,E as k,b as v,x as z,k as A,G as y,A as R,l as T,H as C}from"./index-Cwqm8wBn.js";const w=["title"],M=l({__name:"DateTimeTZ",props:{date:{required:!0,type:Number},showTimezone:{required:!1,type:Boolean,default:!1},dateOnly:{required:!1,type:Boolean,default:!1}},setup(r){const t=r,a=s(()=>t.dateOnly?P(t.date):t.showTimezone?u(t.date):g(t.date)),o=s(()=>{const e=u(t.date),n=u(t.date,"UTC");return e===n?n:`${e}
${n}`});return(e,n)=>(c(),p("span",{title:i(o)},d(i(a)),9,w))}}),D={class:"d-inline-block"},q=l({__name:"ProfitSymbol",props:{profit:{type:Number,required:!0}},setup(r){const t=r,a=s(()=>t.profit>0);return(o,e)=>(c(),p("div",D,[_("div",{class:m(i(a)?"triangle-up":"triangle-down")},null,2)]))}}),B=h(q,[["__scopeId","data-v-605c1d6a"]]),S=["title"],N={class:"flex justify-center items-center grow"},x=["title"],O=l({__name:"ProfitPill",props:{profitRatio:{},profitAbs:{},stakeCurrency:{},profitDesc:{}},setup(r){const t=r,a=s(()=>t.profitRatio!==void 0&&t.profitRatio>0||t.profitRatio===void 0&&t.profitAbs!==void 0&&t.profitAbs>0),o=s(()=>t.profitRatio!==void 0&&t.profitAbs!==void 0?`(${b(t.profitAbs,3)})`:t.profitAbs!==void 0?t.stakeCurrency!==void 0?`${k(t.profitAbs,t.stakeCurrency,3)}`:`${b(t.profitAbs,3)}`:"");return(e,n)=>{const f=B;return c(),p("div",{class:m(["flex justify-between items-center profit-pill ps-2 pe-1",{"profit-pill-profit":i(a)}]),title:e.profitDesc},[v(f,{profit:(e.profitRatio||e.profitAbs)??0},null,8,["profit"]),_("div",N,[z(d(e.profitRatio!==void 0?("formatPercent"in e?e.formatPercent:i(y))(e.profitRatio,2):"")+" ",1),i(o)?(c(),p("span",{key:0,class:m(["ms-1",e.profitRatio?"small":""]),title:e.stakeCurrency},d(i(o)),11,x)):A("",!0)])],10,S)}}}),V=h(O,[["__scopeId","data-v-98c41295"]]),Q=l({__name:"TradeProfit",props:{trade:{required:!0,type:Object},mode:{required:!1,default:"default",type:String}},setup(r){const t=r,a={default:"Current profit",total:"Total profit",realized:"Realized profit"},o=s(()=>{switch(t.mode){case"default":return t.trade.profit_ratio;case"total":return t.trade.total_profit_ratio;case"realized":return t.trade.realized_profit_ratio;default:return}}),e=s(()=>{switch(t.mode){case"default":return t.trade.profit_abs;case"total":return t.trade.total_profit_abs;case"realized":return t.trade.realized_profit;default:return}}),n=s(()=>{let f=`${a[t.mode]}: ${o.value?y(o.value):""} (${e.value})`;return f+=`
Open since: ${R(t.trade.open_timestamp)}`,f});return(f,G)=>{const $=V;return c(),T($,{"profit-ratio":i(o),"profit-abs":i(e),"profit-desc":i(n),"stake-currency":r.trade.quote_currency||"USDT"},null,8,["profit-ratio","profit-abs","profit-desc","stake-currency"])}}}),j={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function I(r,t){return c(),p("svg",j,t[0]||(t[0]=[_("path",{fill:"currentColor",d:"M11 9h2V7h-2m1 13c-4.41 0-8-3.59-8-8s3.59-8 8-8s8 3.59 8 8s-3.59 8-8 8m0-18A10 10 0 0 0 2 12a10 10 0 0 0 10 10a10 10 0 0 0 10-10A10 10 0 0 0 12 2m-1 15h2v-6h-2z"},null,-1)]))}const U=C({name:"mdi-information-outline",render:I}),E=["title"],W=l({__name:"InfoBox",props:{hint:{type:String,required:!0}},setup(r){return(t,a)=>{const o=U;return c(),p("div",{title:r.hint},[v(o)],8,E)}}});export{M as _,Q as a,V as b,W as c};
//# sourceMappingURL=InfoBox.vue_vue_type_script_setup_true_lang-DKaN2Tbm.js.map
