import{d as v,K as $,j as g,c as Z,a as x,e as T,l as k,k as w,f as d,n as q,b as W,a6 as K,t as L,a$ as H,r as J,b6 as Q,o as X,b7 as Y,U as tt,D as V,b8 as et,A as ot}from"./index-Cwqm8wBn.js";import{u as O,E as S,i as A,a as I,b as D,c as B,d as R}from"./installCanvasRenderer-SA1tPojE.js";import{e as z,o as M,c as N,i as F,b as at,d as E,f as st,k as rt}from"./chartZoom-DB0tK3Do.js";function nt(l,a){const n=Math.min(...l),m=(Math.max(...l)-n)*1.01/a,u=[...Array(a).keys()].map(s=>[Math.round((n+s*m)*1e3)/1e3,0]);for(let s=0;s<l.length;s++){const e=Math.min(Math.floor((l[s]-n)/m),a-1);isNaN(e)||u[e][1]++}return u}const it={class:"flex flex-col h-full relative"},lt={class:"grow mb-2"},C="Trade count",ct=v({__name:"ProfitDistributionChart",props:{trades:{required:!0,type:Array},showTitle:{default:!0,type:Boolean}},setup(l){O([M,A,I,N,D,B,R]);const a=l,n=$(),b=[{text:"10",value:10},{text:"15",value:15},{text:"20",value:20},{text:"25",value:25},{text:"50",value:50}],m=g(()=>{const s=a.trades.map(e=>e.profit_ratio);return nt(s,n.profitDistributionBins)}),u=g(()=>({title:{text:"Profit distribution",left:"center",show:a.showTitle},backgroundColor:"rgba(0, 0, 0, 0)",dataset:{source:m.value},tooltip:{trigger:"axis",axisPointer:{type:"line",label:{backgroundColor:"#6a7985"}}},legend:{data:[C],right:"5%",selectedMode:!1},xAxis:{type:"category",name:"Profit %",nameLocation:"middle",nameGap:25},yAxis:[{type:"value",name:C,splitLine:{show:!1},nameRotate:90,nameLocation:"middle",nameGap:35,position:"left"}],grid:{...z,bottom:50},series:[{type:"bar",name:C,animation:!0,encode:{x:"x0",y:"y0"}}]}));return(s,e)=>{const p=K;return x(),Z("div",it,[T("div",lt,[l.trades?(x(),k(d(S),{key:0,option:d(u),autoresize:"",theme:d(n).chartTheme},null,8,["option","theme"])):w("",!0)]),T("div",{class:q(["z-2 absolute fixed-top flex items-center gap-10 ms-2",{"mx-auto":l.showTitle}]),"label-for":"input-bins",size:"sm"},[e[1]||(e[1]=T("label",{for:"input-bins"},"Bins",-1)),W(p,{id:"input-bins",modelValue:d(n).profitDistributionBins,"onUpdate:modelValue":e[0]||(e[0]=_=>d(n).profitDistributionBins=_),size:"small","option-label":"text","option-value":"value",class:"mt-1",options:b},null,8,["modelValue"])],2)])}}}),_t=L(ct,[["__scopeId","data-v-17464fd3"]]),P="Profit",pt=v({__name:"CumProfitChart",props:{trades:{},openTrades:{default:()=>[]},showTitle:{type:Boolean,default:!0},profitColumn:{default:"profit_abs"}},setup(l){O([M,F,A,I,N,at,D,B,R]);const a=l,n=$(),b=H(),m=J(),u=g(()=>a.openTrades.reduce((t,i)=>t+(i.total_profit_abs??i[a.profitColumn]??0),0)),s=g(()=>{const t={},i=a.trades.slice().sort((c,h)=>c.close_timestamp>h.close_timestamp?1:-1);let r=0,y=!0;for(let c=0,h=i.length;c<h;c+=1){const o=i[c];y&&(y=!1,t[o.open_timestamp]?t[o.open_timestamp][o.botId]=r:t[o.open_timestamp]={profit:r,[o.botId]:r}),o.close_timestamp&&o[a.profitColumn]&&(r+=o[a.profitColumn],t[o.close_timestamp]?(t[o.close_timestamp].profit+=o[a.profitColumn],t[o.close_timestamp][o.botId]?t[o.close_timestamp][o.botId]+=o[a.profitColumn]:t[o.close_timestamp][o.botId]=r):t[o.close_timestamp]={profit:r,[o.botId]:r})}const f=Object.entries(t).map(([c,h])=>({date:parseInt(c,10),profit:h.profit}));if(a.openTrades.length>0){let c=0,h=0;if(f.length>0){const G=f[f.length-1];c=G.profit??0,h=G.date??0}else h=a.openTrades[0].open_timestamp;const o=(c??0)+u.value;f.push({date:h,currentProfit:c});const U=Date.now()+24*60*60*1e3;f.push({date:U,currentProfit:o})}return f});function e(t=!1){const{colorProfit:i,colorLoss:r}=b;return{dataset:{dimensions:["date","profit","currentProfit"],source:s.value},series:[{type:"line",name:"currentProfit",animation:t,lineStyle:{color:u.value>0?i:r,type:"dotted"},itemStyle:{color:u.value>0?i:r},encode:{x:"date",y:"currentProfit"}},{type:"line",name:P,animation:t,step:"end",lineStyle:{color:n.chartTheme==="dark"?"#c2c2c2":"black"},itemStyle:{color:n.chartTheme==="dark"?"#c2c2c2":"black"},encode:{x:"date",y:"profit"}}]}}function p(t=!1){var r;const i=e(t);(r=m.value)==null||r.setOption(i,{replaceMerge:["series","dataset"]})}const _=Q(()=>a.trades,()=>{const t={title:{text:"Cumulative Profit",left:"center",show:a.showTitle},backgroundColor:"rgba(0, 0, 0, 0)",tooltip:{trigger:"axis",formatter:r=>{const y=r[0].data.profit,f=r[0].data.currentProfit,c=f?`Projected profit (incl. unrealized): ${V(f,3)}`:`Profit: ${V(y,3)}`;return`${et(r[1].data.date)}<br />${r[1].marker}${c}`},axisPointer:{type:"line",label:{backgroundColor:"#6a7985"}}},legend:{data:[P],right:"5%",selectedMode:!1},useUTC:!1,xAxis:{type:"time"},yAxis:[{type:"value",name:P,splitLine:{show:!1},nameRotate:90,nameLocation:"middle",nameGap:40}],grid:{...z},dataZoom:[{type:"inside",start:0,end:100},{bottom:10,start:0,end:100,...E}]},i=e(!1);return t.series=i.series,t.dataset=i.dataset,t});return X(()=>{}),Y(()=>a.openTrades,()=>{p()},{throttle:60*1e3}),tt(()=>n.chartTheme,()=>{_.trigger()}),(t,i)=>t.trades?(x(),k(d(S),{key:0,ref_key:"chart",ref:m,option:d(_),theme:d(n).chartTheme,autoresize:""},null,8,["option","theme"])):w("",!0)}}),bt=L(pt,[["__scopeId","data-v-ddcc5ca5"]]),j="Profit %",mt="#9be0a8",dt=v({__name:"TradesLogChart",props:{trades:{required:!0,type:Array},showTitle:{default:!0,type:Boolean}},setup(l){O([M,F,A,I,N,D,B,R,st,rt]);const a=l,n=$(),b=H(),m=g(()=>{const s=[],e=a.trades.slice(0).sort((p,_)=>p.close_timestamp>_.close_timestamp?1:-1);for(let p=0,_=e.length;p<_;p+=1){const t=e[p],i=[p,(t.profit_ratio*100).toFixed(2),t.pair,t.botName,ot(t.close_timestamp),t.is_short===void 0||!t.is_short?"Long":"Short"];s.push(i)}return s}),u=g(()=>{const s=m.value.length>0?(1-50/m.value.length)*100:100;return{title:{text:"Trades log",left:"center",show:a.showTitle},backgroundColor:"rgba(0, 0, 0, 0)",dataset:{dimensions:["date","profit"],source:m.value},tooltip:{trigger:"axis",formatter:e=>{const p=e[0].data[3]?` | ${e[0].data[3]}`:"";return`${e[0].data[2]} | ${e[0].data[5]} ${p}<br>${e[0].data[4]}<br>Profit ${e[0].data[1]} %`},axisPointer:{type:"line",label:{backgroundColor:"#6a7985"}}},xAxis:{type:"category",show:!1},yAxis:[{type:"value",name:j,splitLine:{show:!1},nameRotate:90,nameLocation:"middle",nameGap:30}],grid:{...z,left:80},dataZoom:[{type:"inside",start:s,end:100},{bottom:10,start:s,end:100,...E}],visualMap:[{show:!0,seriesIndex:0,pieces:[{max:0,color:b.colorLoss},{min:0,color:b.colorProfit}]}],series:[{type:"bar",name:j,barCategoryGap:"0%",animation:!1,label:{show:!0,position:"top",rotate:90,offset:[7.5,7.5],formatter:"{@[1]} %",color:n.chartTheme==="dark"?"#c2c2c2":"#3c3c3c"},encode:{x:0,y:1},itemStyle:{color:mt}}]}});return(s,e)=>l.trades.length>0?(x(),k(d(S),{key:0,option:d(u),autoresize:"",theme:d(n).chartTheme},null,8,["option","theme"])):w("",!0)}}),gt=L(dt,[["__scopeId","data-v-1368a762"]]);export{gt as _,bt as a,_t as b};
//# sourceMappingURL=TradesLogChart-DuJ-8U1F.js.map
