import{_ as D}from"./CandleChartContainer-G8jE_PHU.js";import{s as W}from"./index-DfIy61Gi.js";import{_ as L}from"./TimeRangeSelect.vue_vue_type_script_setup_true_lang-DJKJVTiO.js";import{_ as U,a as N}from"./StrategySelect.vue_vue_type_script_setup_true_lang-BoCCaGKY.js";import{_ as O}from"./ExchangeSelect.vue_vue_type_script_setup_true_lang-CExpuEN2.js";import{Q as P,r as c,d as $,u as R,j as p,o as H,R as j,S as A,U as z,c as h,a as v,k as x,e as m,f as a,b as d,h as u,V as B,W as I,x as b,X as V,z as y,Y as Q,l as X}from"./index-Cwqm8wBn.js";import"./index-CONYmxgd.js";import"./index-Dt2Q_yjR.js";import"./check-olqpNIE9.js";import"./plus-box-outline-CDxaZbJP.js";import"./installCanvasRenderer-SA1tPojE.js";import"./chartZoom-DB0tK3Do.js";import"./index-Bpiivi0c.js";const Y=P("chartConfig",()=>{const f=c(""),e=c(!1),o=c("1h"),n=c("");return{strategy:f,useLiveData:e,selectedTimeframe:o,timerange:n}},{persist:{key:"ftUIChartSettings",pick:["useLiveData"]}}),q={class:"flex flex-col h-full"},F={key:0,class:"md:mx-3 mt-2 px-1"},G={class:"mb-2 border dark:border-surface-700 border-surface-300 rounded-md p-2 text-start"},J={class:"flex flex-row gap-5"},K={class:"grid grid-cols-3 md:grid-cols-5 mx-1 gap-1 md:gap-2"},Z={class:"text-start md:me-1 col-span-2"},ee={class:"flex flex-col text-start"},te={class:"md:mx-2 mt-2 pb-1 h-full"},fe=$({__name:"ChartsView",setup(f){const e=R(),o=Y(),n=p(()=>e.activeBot.isWebserverMode?o.selectedTimeframe||e.activeBot.strategy.timeframe||"":e.activeBot.timeframe),E=p(()=>{var i;if(e.activeBot.isWebserverMode){if(o.useLiveData)return Object.keys(((i=_.value)==null?void 0:i.markets)||{}).sort()||[];if(n.value&&n.value!==""){const t=n.value;return e.activeBot.pairlistWithTimeframe.filter(([l,g])=>g===t).map(([l])=>l)}return e.activeBot.pairlist}return e.activeBot.whitelist});H(()=>{e.activeBot.isWebserverMode?e.activeBot.getAvailablePairs({}):(!e.activeBot.whitelist||e.activeBot.whitelist.length===0)&&e.activeBot.getWhitelist()});function S(i,t){if(console.log("Refreshing OHLCV for pair:",i,n.value,"with columns:",t),e.activeBot.isWebserverMode&&n.value){const l={pair:i,timeframe:n.value,timerange:o.timerange,strategy:o.strategy,columns:t,live_mode:o.useLiveData};s.value.customExchange&&(l.exchange=s.value.selectedExchange.exchange,l.trading_mode=s.value.selectedExchange.trade_mode.trading_mode,l.margin_mode=s.value.selectedExchange.trade_mode.margin_mode),e.activeBot.getPairHistory(l)}else e.activeBot.getPairCandles({pair:i,timeframe:n.value,columns:t})}const s=c({customExchange:!1,selectedExchange:{exchange:e.activeBot.botState.exchange,trade_mode:{margin_mode:A.NONE,trading_mode:j.SPOT}}}),_=c(null);return z(()=>o.useLiveData,async()=>{if(e.activeBot.isWebserverMode&&o.useLiveData){const i={};s.value.customExchange&&(i.exchange=s.value.selectedExchange.exchange,i.trading_mode=s.value.selectedExchange.trade_mode.trading_mode,i.margin_mode=s.value.selectedExchange.trade_mode.margin_mode),_.value=await e.activeBot.getMarkets(i)}},{immediate:!0}),(i,t)=>{const l=I,g=O,C=U,k=N,w=L,M=W,T=D;return v(),h("div",q,[a(e).activeBot.isWebserverMode?(v(),h("div",F,[d(M,{header:"Settings",toggleable:""},{default:u(()=>[m("div",G,[m("div",J,[d(l,{modelValue:a(s).customExchange,"onUpdate:modelValue":t[0]||(t[0]=r=>a(s).customExchange=r),class:"mb-2"},{default:u(()=>t[6]||(t[6]=[b(" Custom Exchange ")])),_:1,__:[6]},8,["modelValue"]),B(m("span",null," Current Exchange: "+y(a(e).activeBot.botState.exchange)+" "+y(a(e).activeBot.botState.trading_mode),513),[[V,!a(s).customExchange]])]),d(Q,{name:"fade"},{default:u(()=>[B(d(g,{modelValue:a(s).selectedExchange,"onUpdate:modelValue":t[1]||(t[1]=r=>a(s).selectedExchange=r)},null,8,["modelValue"]),[[V,a(s).customExchange]])]),_:1})]),m("div",K,[m("div",Z,[t[8]||(t[8]=m("span",null,"Strategy",-1)),d(C,{modelValue:a(o).strategy,"onUpdate:modelValue":t[2]||(t[2]=r=>a(o).strategy=r),class:"mt-1 mb-1"},null,8,["modelValue"]),a(e).activeBot.botApiVersion>=2.42?(v(),X(l,{key:0,modelValue:a(o).useLiveData,"onUpdate:modelValue":t[3]||(t[3]=r=>a(o).useLiveData=r),class:"align-self-center",title:"Use live data from the exchange. Only use if you don't have data downloaded locally."},{default:u(()=>t[7]||(t[7]=[b(" Use Live Data ")])),_:1,__:[7]},8,["modelValue"])):x("",!0)]),m("div",ee,[t[9]||(t[9]=m("span",null,"Timeframe",-1)),d(k,{modelValue:a(o).selectedTimeframe,"onUpdate:modelValue":t[4]||(t[4]=r=>a(o).selectedTimeframe=r),class:"mt-1"},null,8,["modelValue"])]),d(w,{modelValue:a(o).timerange,"onUpdate:modelValue":t[5]||(t[5]=r=>a(o).timerange=r),class:"col-span-3 md:col-span-2"},null,8,["modelValue"])])]),_:1})])):x("",!0),m("div",te,[d(T,{"available-pairs":a(E),"historic-view":a(e).activeBot.isWebserverMode,timeframe:a(n),trades:a(e).activeBot.trades,timerange:a(e).activeBot.isWebserverMode?a(o).timerange:void 0,strategy:a(e).activeBot.isWebserverMode?a(o).strategy:void 0,"plot-config-modal":!1,onRefreshData:S},null,8,["available-pairs","historic-view","timeframe","trades","timerange","strategy"])])])}}});export{fe as default};
//# sourceMappingURL=ChartsView-B7ejBTsr.js.map
