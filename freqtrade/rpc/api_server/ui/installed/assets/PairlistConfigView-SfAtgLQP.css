/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){[data-v-47402f67],[data-v-47402f67]:before,[data-v-47402f67]:after,[data-v-47402f67]::backdrop{--tw-border-style:solid}}}.pairlist.dragging[data-v-47402f67]{border-style:var(--tw-border-style);border-width:1px;border-color:var(--color-white,#fff);background-color:var(--color-white,#fff)}.pairlist.dragging[data-v-47402f67]:where(.dark,.dark *){background-color:var(--color-black,#000)}.empty[data-v-47402f67]:after{content:"Drag pairlist here";text-transform:uppercase;align-self:center;font-size:1.1rem;line-height:0;position:absolute;top:50%}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}
