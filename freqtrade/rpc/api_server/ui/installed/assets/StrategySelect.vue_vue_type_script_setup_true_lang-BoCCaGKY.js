import{d as V,r as S,j as v,l as w,a as _,f as l,i as g,a6 as h,u as x,o as b,c as y,e as B,V as T,k,b as f,g as C,h as U,y as z,ar as L}from"./index-Cwqm8wBn.js";const q=V({__name:"TimeframeSelect",props:{value:{default:""},belowTimeframe:{default:""},size:{default:void 0}},emits:["input"],setup(r,{emit:c}){const u=r,m=c,e=S(""),s=[{value:"",text:"Use strategy default"},"1m","3m","5m","15m","30m","1h","2h","4h","6h","8h","12h","1d","3d","1w","2w","1M","1y"],i=v(()=>{if(!u.belowTimeframe)return s;const o=s.findIndex(n=>n===u.belowTimeframe);return[...s].splice(0,o)}),a=()=>{m("input",e.value)};return(o,n)=>{const d=h;return _(),w(d,{modelValue:l(e),"onUpdate:modelValue":n[0]||(n[0]=t=>g(e)?e.value=t:null),placeholder:"Use strategy default","option-label":t=>t.text||t,"option-value":t=>t.value??t,size:o.size,options:l(i),onChange:a},null,8,["modelValue","option-label","option-value","size","options"])}}}),D={class:"w-full flex"},M={class:"ms-1"},$=V({__name:"StrategySelect",props:{modelValue:{type:String,required:!0},showDetails:{default:!1,required:!1,type:Boolean}},emits:["update:modelValue"],setup(r,{emit:c}){const u=r,m=c,e=x(),s=v(()=>{var a;return(a=e.activeBot.strategy)==null?void 0:a.code}),i=v({get(){return u.modelValue},set(a){e.activeBot.getStrategy(a),m("update:modelValue",a)}});return b(()=>{e.activeBot.strategyList.length===0&&e.activeBot.getStrategyList()}),(a,o)=>{const n=h,d=z,t=C;return _(),y("div",null,[B("div",D,[f(n,{id:"strategy-select",modelValue:l(i),"onUpdate:modelValue":o[0]||(o[0]=p=>g(i)?i.value=p:null),filter:"",fluid:"",options:l(e).activeBot.strategyList},null,8,["modelValue","options"]),B("div",M,[f(t,{severity:"secondary",variant:"outlined",onClick:l(e).activeBot.getStrategyList},{icon:U(()=>[f(d)]),_:1},8,["onClick"])])]),r.showDetails&&l(e).activeBot.strategy?T((_(),y("textarea",{key:0,"onUpdate:modelValue":o[1]||(o[1]=p=>g(s)?s.value=p:null),class:"w-full h-full"},null,512)),[[L,l(s)]]):k("",!0)])}}});export{$ as _,q as a};
//# sourceMappingURL=StrategySelect.vue_vue_type_script_setup_true_lang-BoCCaGKY.js.map
