import{j as w,aC as dt,aj as ot,d5 as Qn,T as rt,aw as G,d6 as Jn,aR as Ds,d7 as Ts,aS as Bt,bm as Ls,n as E,bW as Ur,q as O,d8 as to,d9 as Is,o as Pt,da as Ae,cc as eo,am as ft,db as ro,dc as ks,dd as ao,bi as H,bq as St,_ as I,G as $,K as yt,S as we,de as Ua,bG as We,bH as He,v as ut,t as da,s as _t,D as Xt,bJ as tr,Z as ga,z as F,C as Me,F as Ft,ar as at,aN as Xa,J as er,bR as rr,by as st,cf as fe,a4 as ar,df as zt,cv as Ps,dg as Ya,dh as $a,aP as wt,bp as ya,a6 as nt,L as R,di as Ka,x as ht,y as pe,dj as ja,H as Qt,av as qa,aZ as tt,dk as Rs,dl as io,ad as no,bY as Ze,dm as Es,bP as ma,R as it,bT as ie,dn as Qa,bu as Ja,bX as Os,bs as Vs,dp as zs,bV as Bs,bU as Ns,B as de,W as Mt,V as ir,Q as Z,dq as Gs,dr as xa,ds as Sa,dt as Fs,g as Ws,h as ti,a3 as oo,du as Hs,dv as vr,aF as Ce,dw as Zs,aT as et,bB as Us,dx as ei,dy as ri,cd as Xs,a5 as Rt,dz as Xr,bC as ai,dA as Ys,dB as $s,X as Ks,bF as js,ce as qs,ab as ge,cg as ii,a1 as Et,u as kt,dC as nr,aU as so,A as U,dD as ni,dE as Wt,dF as lo,dG as cr,dH as fr,ak as Qs,dI as Js,b7 as Yr,b6 as pr,aO as tl,bb as uo,be as el,a$ as ho,P as ye,r as ba,dJ as _a,dK as Jt,dL as rl,M as al,dM as dr,a2 as ct,aB as Lt,a7 as k,dN as me,dO as il,aW as or,ao as vo,cV as co,c$ as nl,aH as ol,e as sl,dP as ll,dQ as ul,a_ as gt,dR as oi,dS as hl,dT as vl,dU as cl,aQ as ve,as as fo,aX as po,dV as fl,c5 as pl,dW as dl,bE as bt,dX as gl,dY as yl,U as ml,al as xl,dZ as go,c3 as Sl,d_ as bl,d$ as _l,e0 as yo,e1 as Al,e2 as si,e3 as mo,e4 as wl,e5 as Ml,e6 as xo,e7 as So,e8 as Aa,O as $r,ah as li,af as Cl,ai as Dl,e9 as Be,ea as Kr,bD as te,eb as Tl,ec as Ll,aG as Il,ed as Ue,ee as kl,ef as Pl,eg as Rl,cl as wa,aE as qt,eh as El,aD as Ol,bz as bo,bA as Vl,ei as zl,bv as Bl,ej as Nl,b2 as Gl,ek as Fl,aI as Wl,el as ui,em as Hl}from"./installCanvasRenderer-SA1tPojE.js";var Zl=function(){function a(e){this.coordSysDims=[],this.axisMap=ot(),this.categoryAxisMap=ot(),this.coordSysName=e}return a}();function Ul(a){var e=a.get("coordinateSystem"),t=new Zl(e),r=Xl[e];if(r)return r(a,t,t.axisMap,t.categoryAxisMap),t}var Xl={cartesian2d:function(a,e,t,r){var i=a.getReferringComponents("xAxis",dt).models[0],n=a.getReferringComponents("yAxis",dt).models[0];e.coordSysDims=["x","y"],t.set("x",i),t.set("y",n),Yt(i)&&(r.set("x",i),e.firstCategoryDimIndex=0),Yt(n)&&(r.set("y",n),e.firstCategoryDimIndex==null&&(e.firstCategoryDimIndex=1))},singleAxis:function(a,e,t,r){var i=a.getReferringComponents("singleAxis",dt).models[0];e.coordSysDims=["single"],t.set("single",i),Yt(i)&&(r.set("single",i),e.firstCategoryDimIndex=0)},polar:function(a,e,t,r){var i=a.getReferringComponents("polar",dt).models[0],n=i.findAxisModel("radiusAxis"),o=i.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],t.set("radius",n),t.set("angle",o),Yt(n)&&(r.set("radius",n),e.firstCategoryDimIndex=0),Yt(o)&&(r.set("angle",o),e.firstCategoryDimIndex==null&&(e.firstCategoryDimIndex=1))},geo:function(a,e,t,r){e.coordSysDims=["lng","lat"]},parallel:function(a,e,t,r){var i=a.ecModel,n=i.getComponent("parallel",a.get("parallelIndex")),o=e.coordSysDims=n.dimensions.slice();w(n.parallelAxisIndex,function(s,l){var u=i.getComponent("parallelAxis",s),h=o[l];t.set(h,u),Yt(u)&&(r.set(h,u),e.firstCategoryDimIndex==null&&(e.firstCategoryDimIndex=l))})}};function Yt(a){return a.get("type")==="category"}function Yl(a,e){var t=a.get("coordinateSystem"),r=Ls.get(t),i;return e&&e.coordSysDims&&(i=E(e.coordSysDims,function(n){var o={name:n},s=e.axisMap.get(n);if(s){var l=s.get("type");o.type=Ur(l)}return o})),i||(i=r&&(r.getDimensionsInfo?r.getDimensionsInfo():r.dimensions.slice())||["x","y"]),i}function $l(a,e,t){var r,i;return t&&w(a,function(n,o){var s=n.coordDim,l=t.categoryAxisMap.get(s);l&&(r==null&&(r=o),n.ordinalMeta=l.getOrdinalMeta(),e&&(n.createInvertedIndices=!0)),n.otherDims.itemName!=null&&(i=!0)}),!i&&r!=null&&(a[r].otherDims.itemName=0),r}function sr(a,e,t){t=t||{};var r=e.getSourceManager(),i,n=!1;a?(n=!0,i=Is(a)):(i=r.getSource(),n=i.sourceFormat===Qn);var o=Ul(e),s=Yl(e,o),l=t.useEncodeDefaulter,u=rt(l)?l:l?G(Jn,s,e):null,h={coordDimensions:s,generateCoord:t.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:u,canOmitUnusedDimensions:!n},v=Ds(i,h),c=$l(v.dimensions,t.createInvertedIndices,o),f=n?null:r.getSharedDataStore(v),p=Ts(e,{schema:v,store:f}),d=new Bt(v,e);d.setCalculationInfo(p);var g=c!=null&&Kl(i)?function(y,m,S,x){return x===c?S:this.defaultDimValueGetter(y,m,S,x)}:null;return d.hasItemOption=!1,d.initData(n?i:f,null,g),d}function Kl(a){if(a.sourceFormat===Qn){var e=jl(a.data||[]);return!O(to(e))}}function jl(a){for(var e=0;e<a.length&&a[e]==null;)e++;return a[e]}var ql=function(){function a(){}return a.prototype.getNeedCrossZero=function(){var e=this.option;return!e.scale},a.prototype.getCoordSysModel=function(){},a}(),xe=ft();function _o(a,e){var t=E(e,function(r){return a.scale.parse(r)});return a.type==="time"&&t.length>0&&(t.sort(),t.unshift(t[0]),t.push(t[t.length-1])),t}function Ql(a){var e=a.getLabelModel().get("customValues");if(e){var t=Ae(a),r=a.scale.getExtent(),i=_o(a,e),n=Pt(i,function(o){return o>=r[0]&&o<=r[1]});return{labels:E(n,function(o){var s={value:o};return{formattedLabel:t(s),rawLabel:a.scale.getLabel(s),tickValue:o}})}}return a.type==="category"?tu(a):ru(a)}function Jl(a,e){var t=a.getTickModel().get("customValues");if(t){var r=a.scale.getExtent(),i=_o(a,t);return{ticks:Pt(i,function(n){return n>=r[0]&&n<=r[1]})}}return a.type==="category"?eu(a,e):{ticks:E(a.scale.getTicks(),function(n){return n.value})}}function tu(a){var e=a.getLabelModel(),t=Ao(a,e);return!e.get("show")||a.scale.isBlank()?{labels:[],labelCategoryInterval:t.labelCategoryInterval}:t}function Ao(a,e){var t=wo(a,"labels"),r=ro(e),i=Mo(t,r);if(i)return i;var n,o;return rt(r)?n=To(a,r):(o=r==="auto"?au(a):r,n=Do(a,o)),Co(t,r,{labels:n,labelCategoryInterval:o})}function eu(a,e){var t=wo(a,"ticks"),r=ro(e),i=Mo(t,r);if(i)return i;var n,o;if((!e.get("show")||a.scale.isBlank())&&(n=[]),rt(r))n=To(a,r,!0);else if(r==="auto"){var s=Ao(a,a.getLabelModel());o=s.labelCategoryInterval,n=E(s.labels,function(l){return l.tickValue})}else o=r,n=Do(a,o,!0);return Co(t,r,{ticks:n,tickCategoryInterval:o})}function ru(a){var e=a.scale.getTicks(),t=Ae(a);return{labels:E(e,function(r,i){return{level:r.level,formattedLabel:t(r,i),rawLabel:a.scale.getLabel(r),tickValue:r.value}})}}function wo(a,e){return xe(a)[e]||(xe(a)[e]=[])}function Mo(a,e){for(var t=0;t<a.length;t++)if(a[t].key===e)return a[t].value}function Co(a,e,t){return a.push({key:e,value:t}),t}function au(a){var e=xe(a).autoInterval;return e??(xe(a).autoInterval=a.calculateCategoryInterval())}function iu(a){var e=nu(a),t=Ae(a),r=(e.axisRotate-e.labelRotate)/180*Math.PI,i=a.scale,n=i.getExtent(),o=i.count();if(n[1]-n[0]<1)return 0;var s=1;o>40&&(s=Math.max(1,Math.floor(o/40)));for(var l=n[0],u=a.dataToCoord(l+1)-a.dataToCoord(l),h=Math.abs(u*Math.cos(r)),v=Math.abs(u*Math.sin(r)),c=0,f=0;l<=n[1];l+=s){var p=0,d=0,g=eo(t({value:l}),e.font,"center","top");p=g.width*1.3,d=g.height*1.3,c=Math.max(c,p,7),f=Math.max(f,d,7)}var y=c/h,m=f/v;isNaN(y)&&(y=1/0),isNaN(m)&&(m=1/0);var S=Math.max(0,Math.floor(Math.min(y,m))),x=xe(a.model),b=a.getExtent(),_=x.lastAutoInterval,A=x.lastTickCount;return _!=null&&A!=null&&Math.abs(_-S)<=1&&Math.abs(A-o)<=1&&_>S&&x.axisExtent0===b[0]&&x.axisExtent1===b[1]?S=_:(x.lastTickCount=o,x.lastAutoInterval=S,x.axisExtent0=b[0],x.axisExtent1=b[1]),S}function nu(a){var e=a.getLabelModel();return{axisRotate:a.getRotate?a.getRotate():a.isHorizontal&&!a.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}function Do(a,e,t){var r=Ae(a),i=a.scale,n=i.getExtent(),o=a.getLabelModel(),s=[],l=Math.max((e||0)+1,1),u=n[0],h=i.count();u!==0&&l>1&&h/l>2&&(u=Math.round(Math.ceil(u/l)*l));var v=ks(a),c=o.get("showMinLabel")||v,f=o.get("showMaxLabel")||v;c&&u!==n[0]&&d(n[0]);for(var p=u;p<=n[1];p+=l)d(p);f&&p-l!==n[1]&&d(n[1]);function d(g){var y={value:g};s.push(t?g:{formattedLabel:r(y),rawLabel:i.getLabel(y),tickValue:g})}return s}function To(a,e,t){var r=a.scale,i=Ae(a),n=[];return w(r.getTicks(),function(o){var s=r.getLabel(o),l=o.value;e(o.value,s)&&n.push(t?l:{formattedLabel:i(o),rawLabel:s,tickValue:l})}),n}var hi=[0,1],Lo=function(){function a(e,t,r){this.onBand=!1,this.inverse=!1,this.dim=e,this.scale=t,this._extent=r||[0,0]}return a.prototype.contain=function(e){var t=this._extent,r=Math.min(t[0],t[1]),i=Math.max(t[0],t[1]);return e>=r&&e<=i},a.prototype.containData=function(e){return this.scale.contain(e)},a.prototype.getExtent=function(){return this._extent.slice()},a.prototype.getPixelPrecision=function(e){return ao(e||this.scale.getExtent(),this._extent)},a.prototype.setExtent=function(e,t){var r=this._extent;r[0]=e,r[1]=t},a.prototype.dataToCoord=function(e,t){var r=this._extent,i=this.scale;return e=i.normalize(e),this.onBand&&i.type==="ordinal"&&(r=r.slice(),vi(r,i.count())),H(e,hi,r,t)},a.prototype.coordToData=function(e,t){var r=this._extent,i=this.scale;this.onBand&&i.type==="ordinal"&&(r=r.slice(),vi(r,i.count()));var n=H(e,r,hi,t);return this.scale.scale(n)},a.prototype.pointToData=function(e,t){},a.prototype.getTicksCoords=function(e){e=e||{};var t=e.tickModel||this.getTickModel(),r=Jl(this,t),i=r.ticks,n=E(i,function(s){return{coord:this.dataToCoord(this.scale.type==="ordinal"?this.scale.getRawOrdinalNumber(s):s),tickValue:s}},this),o=t.get("alignWithLabel");return ou(this,n,o,e.clamp),n},a.prototype.getMinorTicksCoords=function(){if(this.scale.type==="ordinal")return[];var e=this.model.getModel("minorTick"),t=e.get("splitNumber");t>0&&t<100||(t=5);var r=this.scale.getMinorTicks(t),i=E(r,function(n){return E(n,function(o){return{coord:this.dataToCoord(o),tickValue:o}},this)},this);return i},a.prototype.getViewLabels=function(){return Ql(this).labels},a.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},a.prototype.getTickModel=function(){return this.model.getModel("axisTick")},a.prototype.getBandWidth=function(){var e=this._extent,t=this.scale.getExtent(),r=t[1]-t[0]+(this.onBand?1:0);r===0&&(r=1);var i=Math.abs(e[1]-e[0]);return Math.abs(i)/r},a.prototype.calculateCategoryInterval=function(){return iu(this)},a}();function vi(a,e){var t=a[1]-a[0],r=e,i=t/r/2;a[0]+=i,a[1]-=i}function ou(a,e,t,r){var i=e.length;if(!a.onBand||t||!i)return;var n=a.getExtent(),o,s;if(i===1)e[0].coord=n[0],o=e[1]={coord:n[1],tickValue:e[0].tickValue};else{var l=e[i-1].tickValue-e[0].tickValue,u=(e[i-1].coord-e[0].coord)/l;w(e,function(f){f.coord-=u/2});var h=a.scale.getExtent();s=1+h[1]-e[i-1].tickValue,o={coord:e[i-1].coord+u*s,tickValue:h[1]+1},e.push(o)}var v=n[0]>n[1];c(e[0].coord,n[0])&&(r?e[0].coord=n[0]:e.shift()),r&&c(n[0],e[0].coord)&&e.unshift({coord:n[0]}),c(n[1],o.coord)&&(r?o.coord=n[1]:e.pop()),r&&c(o.coord,n[1])&&e.push({coord:n[1]});function c(f,p){return f=St(f),p=St(p),v?f>p:f<p}}var su=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t){return sr(null,this,{useEncodeDefaulter:!0})},e.prototype.getLegendIcon=function(t){var r=new $,i=yt("line",0,t.itemHeight/2,t.itemWidth,0,t.lineStyle.stroke,!1);r.add(i),i.setStyle(t.lineStyle);var n=this.getData().getVisual("symbol"),o=this.getData().getVisual("symbolRotate"),s=n==="none"?"circle":n,l=t.itemHeight*.8,u=yt(s,(t.itemWidth-l)/2,(t.itemHeight-l)/2,l,l,t.itemStyle.fill);r.add(u),u.setStyle(t.itemStyle);var h=t.iconRotate==="inherit"?o:t.iconRotate||0;return u.rotation=h*Math.PI/180,u.setOrigin([t.itemWidth/2,t.itemHeight/2]),s.indexOf("empty")>-1&&(u.style.stroke=u.style.fill,u.style.fill="#fff",u.style.lineWidth=2),r},e.type="series.line",e.dependencies=["grid","polar"],e.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},e}(we);function Ma(a,e){var t=a.mapDimensionsAll("defaultedLabel"),r=t.length;if(r===1){var i=Ua(a,e,t[0]);return i!=null?i+"":null}else if(r){for(var n=[],o=0;o<t.length;o++)n.push(Ua(a,e,t[o]));return n.join(" ")}}function Io(a,e){var t=a.mapDimensionsAll("defaultedLabel");if(!O(e))return e+"";for(var r=[],i=0;i<t.length;i++){var n=a.getDimensionIndex(t[i]);n>=0&&r.push(e[n])}return r.join(" ")}var Ca=function(a){I(e,a);function e(t,r,i,n){var o=a.call(this)||this;return o.updateData(t,r,i,n),o}return e.prototype._createSymbol=function(t,r,i,n,o){this.removeAll();var s=yt(t,-1,-1,2,2,null,o);s.attr({z2:100,culling:!0,scaleX:n[0]/2,scaleY:n[1]/2}),s.drift=lu,this._symbolType=t,this.add(s)},e.prototype.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(null,t)},e.prototype.getSymbolType=function(){return this._symbolType},e.prototype.getSymbolPath=function(){return this.childAt(0)},e.prototype.highlight=function(){We(this.childAt(0))},e.prototype.downplay=function(){He(this.childAt(0))},e.prototype.setZ=function(t,r){var i=this.childAt(0);i.zlevel=t,i.z=r},e.prototype.setDraggable=function(t,r){var i=this.childAt(0);i.draggable=t,i.cursor=!r&&t?"move":i.cursor},e.prototype.updateData=function(t,r,i,n){this.silent=!1;var o=t.getItemVisual(r,"symbol")||"circle",s=t.hostModel,l=e.getSymbolSize(t,r),u=o!==this._symbolType,h=n&&n.disableAnimation;if(u){var v=t.getItemVisual(r,"symbolKeepAspect");this._createSymbol(o,t,r,l,v)}else{var c=this.childAt(0);c.silent=!1;var f={scaleX:l[0]/2,scaleY:l[1]/2};h?c.attr(f):ut(c,f,s,r),da(c)}if(this._updateCommon(t,r,l,i,n),u){var c=this.childAt(0);if(!h){var f={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:c.style.opacity}};c.scaleX=c.scaleY=0,c.style.opacity=0,_t(c,f,s,r)}}h&&this.childAt(0).stopAnimation("leave")},e.prototype._updateCommon=function(t,r,i,n,o){var s=this.childAt(0),l=t.hostModel,u,h,v,c,f,p,d,g,y;if(n&&(u=n.emphasisItemStyle,h=n.blurItemStyle,v=n.selectItemStyle,c=n.focus,f=n.blurScope,d=n.labelStatesModels,g=n.hoverScale,y=n.cursorStyle,p=n.emphasisDisabled),!n||t.hasItemOption){var m=n&&n.itemModel?n.itemModel:t.getItemModel(r),S=m.getModel("emphasis");u=S.getModel("itemStyle").getItemStyle(),v=m.getModel(["select","itemStyle"]).getItemStyle(),h=m.getModel(["blur","itemStyle"]).getItemStyle(),c=S.get("focus"),f=S.get("blurScope"),p=S.get("disabled"),d=Xt(m),g=S.getShallow("scale"),y=m.getShallow("cursor")}var x=t.getItemVisual(r,"symbolRotate");s.attr("rotation",(x||0)*Math.PI/180||0);var b=tr(t.getItemVisual(r,"symbolOffset"),i);b&&(s.x=b[0],s.y=b[1]),y&&s.attr("cursor",y);var _=t.getItemVisual(r,"style"),A=_.fill;if(s instanceof ga){var D=s.style;s.useStyle(F({image:D.image,x:D.x,y:D.y,width:D.width,height:D.height},_))}else s.__isEmptyBrush?s.useStyle(F({},_)):s.useStyle(_),s.style.decal=null,s.setColor(A,o&&o.symbolInnerColor),s.style.strokeNoScale=!0;var M=t.getItemVisual(r,"liftZ"),T=this._z2;M!=null?T==null&&(this._z2=s.z2,s.z2+=M):T!=null&&(s.z2=T,this._z2=null);var C=o&&o.useNameLabel;Me(s,d,{labelFetcher:l,labelDataIndex:r,defaultText:L,inheritColor:A,defaultOpacity:_.opacity});function L(V){return C?t.getName(V):Ma(t,V)}this._sizeX=i[0]/2,this._sizeY=i[1]/2;var P=s.ensureState("emphasis");P.style=u,s.ensureState("select").style=v,s.ensureState("blur").style=h;var B=g==null||g===!0?Math.max(1.1,3/this._sizeY):isFinite(g)&&g>0?+g:1;P.scaleX=this._sizeX*B,P.scaleY=this._sizeY*B,this.setSymbolScale(1),Ft(this,c,f,p)},e.prototype.setSymbolScale=function(t){this.scaleX=this.scaleY=t},e.prototype.fadeOut=function(t,r,i){var n=this.childAt(0),o=at(this).dataIndex,s=i&&i.animation;if(this.silent=n.silent=!0,i&&i.fadeLabel){var l=n.getTextContent();l&&Xa(l,{style:{opacity:0}},r,{dataIndex:o,removeOpt:s,cb:function(){n.removeTextContent()}})}else n.removeTextContent();Xa(n,{style:{opacity:0},scaleX:0,scaleY:0},r,{dataIndex:o,cb:t,removeOpt:s})},e.getSymbolSize=function(t,r){return er(t.getItemVisual(r,"symbolSize"))},e}($);function lu(a,e){this.parent.drift(a,e)}function gr(a,e,t,r){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(r.isIgnore&&r.isIgnore(t))&&!(r.clipShape&&!r.clipShape.contain(e[0],e[1]))&&a.getItemVisual(t,"symbol")!=="none"}function ci(a){return a!=null&&!st(a)&&(a={isIgnore:a}),a||{}}function fi(a){var e=a.hostModel,t=e.getModel("emphasis");return{emphasisItemStyle:t.getModel("itemStyle").getItemStyle(),blurItemStyle:e.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:e.getModel(["select","itemStyle"]).getItemStyle(),focus:t.get("focus"),blurScope:t.get("blurScope"),emphasisDisabled:t.get("disabled"),hoverScale:t.get("scale"),labelStatesModels:Xt(e),cursorStyle:e.get("cursor")}}var ko=function(){function a(e){this.group=new $,this._SymbolCtor=e||Ca}return a.prototype.updateData=function(e,t){this._progressiveEls=null,t=ci(t);var r=this.group,i=e.hostModel,n=this._data,o=this._SymbolCtor,s=t.disableAnimation,l=fi(e),u={disableAnimation:s},h=t.getSymbolPoint||function(v){return e.getItemLayout(v)};n||r.removeAll(),e.diff(n).add(function(v){var c=h(v);if(gr(e,c,v,t)){var f=new o(e,v,l,u);f.setPosition(c),e.setItemGraphicEl(v,f),r.add(f)}}).update(function(v,c){var f=n.getItemGraphicEl(c),p=h(v);if(!gr(e,p,v,t)){r.remove(f);return}var d=e.getItemVisual(v,"symbol")||"circle",g=f&&f.getSymbolType&&f.getSymbolType();if(!f||g&&g!==d)r.remove(f),f=new o(e,v,l,u),f.setPosition(p);else{f.updateData(e,v,l,u);var y={x:p[0],y:p[1]};s?f.attr(y):ut(f,y,i)}r.add(f),e.setItemGraphicEl(v,f)}).remove(function(v){var c=n.getItemGraphicEl(v);c&&c.fadeOut(function(){r.remove(c)},i)}).execute(),this._getSymbolPoint=h,this._data=e},a.prototype.updateLayout=function(){var e=this,t=this._data;t&&t.eachItemGraphicEl(function(r,i){var n=e._getSymbolPoint(i);r.setPosition(n),r.markRedraw()})},a.prototype.incrementalPrepareUpdate=function(e){this._seriesScope=fi(e),this._data=null,this.group.removeAll()},a.prototype.incrementalUpdate=function(e,t,r){this._progressiveEls=[],r=ci(r);function i(l){l.isGroup||(l.incremental=!0,l.ensureState("emphasis").hoverLayer=!0)}for(var n=e.start;n<e.end;n++){var o=t.getItemLayout(n);if(gr(t,o,n,r)){var s=new this._SymbolCtor(t,n,this._seriesScope);s.traverse(i),s.setPosition(o),this.group.add(s),t.setItemGraphicEl(n,s),this._progressiveEls.push(s)}}},a.prototype.eachRendered=function(e){rr(this._progressiveEls||this.group,e)},a.prototype.remove=function(e){var t=this.group,r=this._data;r&&e?r.eachItemGraphicEl(function(i){i.fadeOut(function(){t.remove(i)},r.hostModel)}):t.removeAll()},a}();function Po(a,e,t){var r=a.getBaseAxis(),i=a.getOtherAxis(r),n=uu(i,t),o=r.dim,s=i.dim,l=e.mapDimension(s),u=e.mapDimension(o),h=s==="x"||s==="radius"?1:0,v=E(a.dimensions,function(p){return e.mapDimension(p)}),c=!1,f=e.getCalculationInfo("stackResultDimension");return fe(e,v[0])&&(c=!0,v[0]=f),fe(e,v[1])&&(c=!0,v[1]=f),{dataDimsForPoint:v,valueStart:n,valueAxisDim:s,baseAxisDim:o,stacked:!!c,valueDim:l,baseDim:u,baseDataOffset:h,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function uu(a,e){var t=0,r=a.scale.getExtent();return e==="start"?t=r[0]:e==="end"?t=r[1]:ar(e)&&!isNaN(e)?t=e:r[0]>0?t=r[0]:r[1]<0&&(t=r[1]),t}function Ro(a,e,t,r){var i=NaN;a.stacked&&(i=t.get(t.getCalculationInfo("stackedOverDimension"),r)),isNaN(i)&&(i=a.valueStart);var n=a.baseDataOffset,o=[];return o[n]=t.get(a.baseDim,r),o[1-n]=i,e.dataToPoint(o)}function hu(a,e){var t=[];return e.diff(a).add(function(r){t.push({cmd:"+",idx:r})}).update(function(r,i){t.push({cmd:"=",idx:i,idx1:r})}).remove(function(r){t.push({cmd:"-",idx:r})}).execute(),t}function vu(a,e,t,r,i,n,o,s){for(var l=hu(a,e),u=[],h=[],v=[],c=[],f=[],p=[],d=[],g=Po(i,e,o),y=a.getLayout("points")||[],m=e.getLayout("points")||[],S=0;S<l.length;S++){var x=l[S],b=!0,_=void 0,A=void 0;switch(x.cmd){case"=":_=x.idx*2,A=x.idx1*2;var D=y[_],M=y[_+1],T=m[A],C=m[A+1];(isNaN(D)||isNaN(M))&&(D=T,M=C),u.push(D,M),h.push(T,C),v.push(t[_],t[_+1]),c.push(r[A],r[A+1]),d.push(e.getRawIndex(x.idx1));break;case"+":var L=x.idx,P=g.dataDimsForPoint,B=i.dataToPoint([e.get(P[0],L),e.get(P[1],L)]);A=L*2,u.push(B[0],B[1]),h.push(m[A],m[A+1]);var V=Ro(g,i,e,L);v.push(V[0],V[1]),c.push(r[A],r[A+1]),d.push(e.getRawIndex(L));break;case"-":b=!1}b&&(f.push(x),p.push(p.length))}p.sort(function(Y,hr){return d[Y]-d[hr]});for(var q=u.length,j=zt(q),z=zt(q),W=zt(q),Q=zt(q),N=[],S=0;S<p.length;S++){var lt=p[S],K=S*2,X=lt*2;j[K]=u[X],j[K+1]=u[X+1],z[K]=h[X],z[K+1]=h[X+1],W[K]=v[X],W[K+1]=v[X+1],Q[K]=c[X],Q[K+1]=c[X+1],N[S]=f[lt]}return{current:j,next:z,stackedOnCurrent:W,stackedOnNext:Q,status:N}}var Ct=Math.min,Dt=Math.max;function Nt(a,e){return isNaN(a)||isNaN(e)}function jr(a,e,t,r,i,n,o,s,l){for(var u,h,v,c,f,p,d=t,g=0;g<r;g++){var y=e[d*2],m=e[d*2+1];if(d>=i||d<0)break;if(Nt(y,m)){if(l){d+=n;continue}break}if(d===t)a[n>0?"moveTo":"lineTo"](y,m),v=y,c=m;else{var S=y-u,x=m-h;if(S*S+x*x<.5){d+=n;continue}if(o>0){for(var b=d+n,_=e[b*2],A=e[b*2+1];_===y&&A===m&&g<r;)g++,b+=n,d+=n,_=e[b*2],A=e[b*2+1],y=e[d*2],m=e[d*2+1],S=y-u,x=m-h;var D=g+1;if(l)for(;Nt(_,A)&&D<r;)D++,b+=n,_=e[b*2],A=e[b*2+1];var M=.5,T=0,C=0,L=void 0,P=void 0;if(D>=r||Nt(_,A))f=y,p=m;else{T=_-u,C=A-h;var B=y-u,V=_-y,q=m-h,j=A-m,z=void 0,W=void 0;if(s==="x"){z=Math.abs(B),W=Math.abs(V);var Q=T>0?1:-1;f=y-Q*z*o,p=m,L=y+Q*W*o,P=m}else if(s==="y"){z=Math.abs(q),W=Math.abs(j);var N=C>0?1:-1;f=y,p=m-N*z*o,L=y,P=m+N*W*o}else z=Math.sqrt(B*B+q*q),W=Math.sqrt(V*V+j*j),M=W/(W+z),f=y-T*o*(1-M),p=m-C*o*(1-M),L=y+T*o*M,P=m+C*o*M,L=Ct(L,Dt(_,y)),P=Ct(P,Dt(A,m)),L=Dt(L,Ct(_,y)),P=Dt(P,Ct(A,m)),T=L-y,C=P-m,f=y-T*z/W,p=m-C*z/W,f=Ct(f,Dt(u,y)),p=Ct(p,Dt(h,m)),f=Dt(f,Ct(u,y)),p=Dt(p,Ct(h,m)),T=y-f,C=m-p,L=y+T*W/z,P=m+C*W/z}a.bezierCurveTo(v,c,f,p,y,m),v=L,c=P}else a.lineTo(y,m)}u=y,h=m,d+=n}return g}var Eo=function(){function a(){this.smooth=0,this.smoothConstraint=!0}return a}(),cu=function(a){I(e,a);function e(t){var r=a.call(this,t)||this;return r.type="ec-polyline",r}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Eo},e.prototype.buildPath=function(t,r){var i=r.points,n=0,o=i.length/2;if(r.connectNulls){for(;o>0&&Nt(i[o*2-2],i[o*2-1]);o--);for(;n<o&&Nt(i[n*2],i[n*2+1]);n++);}for(;n<o;)n+=jr(t,i,n,o,o,1,r.smooth,r.smoothMonotone,r.connectNulls)+1},e.prototype.getPointOn=function(t,r){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var i=this.path,n=i.data,o=Ps.CMD,s,l,u=r==="x",h=[],v=0;v<n.length;){var c=n[v++],f=void 0,p=void 0,d=void 0,g=void 0,y=void 0,m=void 0,S=void 0;switch(c){case o.M:s=n[v++],l=n[v++];break;case o.L:if(f=n[v++],p=n[v++],S=u?(t-s)/(f-s):(t-l)/(p-l),S<=1&&S>=0){var x=u?(p-l)*S+l:(f-s)*S+s;return u?[t,x]:[x,t]}s=f,l=p;break;case o.C:f=n[v++],p=n[v++],d=n[v++],g=n[v++],y=n[v++],m=n[v++];var b=u?Ya(s,f,d,y,t,h):Ya(l,p,g,m,t,h);if(b>0)for(var _=0;_<b;_++){var A=h[_];if(A<=1&&A>=0){var x=u?$a(l,p,g,m,A):$a(s,f,d,y,A);return u?[t,x]:[x,t]}}s=y,l=m;break}}},e}(wt),fu=function(a){I(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e}(Eo),pu=function(a){I(e,a);function e(t){var r=a.call(this,t)||this;return r.type="ec-polygon",r}return e.prototype.getDefaultShape=function(){return new fu},e.prototype.buildPath=function(t,r){var i=r.points,n=r.stackedOnPoints,o=0,s=i.length/2,l=r.smoothMonotone;if(r.connectNulls){for(;s>0&&Nt(i[s*2-2],i[s*2-1]);s--);for(;o<s&&Nt(i[o*2],i[o*2+1]);o++);}for(;o<s;){var u=jr(t,i,o,s,s,1,r.smooth,l,r.connectNulls);jr(t,n,o+u-1,u,s,-1,r.stackedOnSmooth,l,r.connectNulls),o+=u+1,t.closePath()}},e}(wt);function Oo(a,e,t,r,i){var n=a.getArea(),o=n.x,s=n.y,l=n.width,u=n.height,h=t.get(["lineStyle","width"])||0;o-=h/2,s-=h/2,l+=h,u+=h,l=Math.ceil(l),o!==Math.floor(o)&&(o=Math.floor(o),l++);var v=new nt({shape:{x:o,y:s,width:l,height:u}});if(e){var c=a.getBaseAxis(),f=c.isHorizontal(),p=c.inverse;f?(p&&(v.shape.x+=l),v.shape.width=0):(p||(v.shape.y+=u),v.shape.height=0);var d=rt(i)?function(g){i(g,v)}:null;_t(v,{shape:{width:l,height:u,x:o,y:s}},t,null,r,d)}return v}function Vo(a,e,t){var r=a.getArea(),i=St(r.r0,1),n=St(r.r,1),o=new ya({shape:{cx:St(a.cx,1),cy:St(a.cy,1),r0:i,r:n,startAngle:r.startAngle,endAngle:r.endAngle,clockwise:r.clockwise}});if(e){var s=a.getBaseAxis().dim==="angle";s?o.shape.endAngle=r.startAngle:o.shape.r=i,_t(o,{shape:{endAngle:r.endAngle,r:n}},t)}return o}function zo(a,e,t,r,i){if(a){if(a.type==="polar")return Vo(a,e,t);if(a.type==="cartesian2d")return Oo(a,e,t,r,i)}else return null;return null}function ne(a,e){return a.type===e}function pi(a,e){if(a.length===e.length){for(var t=0;t<a.length;t++)if(a[t]!==e[t])return;return!0}}function di(a){for(var e=1/0,t=1/0,r=-1/0,i=-1/0,n=0;n<a.length;){var o=a[n++],s=a[n++];isNaN(o)||(e=Math.min(o,e),r=Math.max(o,r)),isNaN(s)||(t=Math.min(s,t),i=Math.max(s,i))}return[[e,t],[r,i]]}function gi(a,e){var t=di(a),r=t[0],i=t[1],n=di(e),o=n[0],s=n[1];return Math.max(Math.abs(r[0]-o[0]),Math.abs(r[1]-o[1]),Math.abs(i[0]-s[0]),Math.abs(i[1]-s[1]))}function yi(a){return ar(a)?a:a?.5:0}function du(a,e,t){if(!t.valueDim)return[];for(var r=e.count(),i=zt(r*2),n=0;n<r;n++){var o=Ro(t,a,e,n);i[n*2]=o[0],i[n*2+1]=o[1]}return i}function Tt(a,e,t,r,i){var n=t.getBaseAxis(),o=n.dim==="x"||n.dim==="radius"?0:1,s=[],l=0,u=[],h=[],v=[],c=[];if(i){for(l=0;l<a.length;l+=2){var f=e||a;!isNaN(f[l])&&!isNaN(f[l+1])&&c.push(a[l],a[l+1])}a=c}for(l=0;l<a.length-2;l+=2)switch(v[0]=a[l+2],v[1]=a[l+3],h[0]=a[l],h[1]=a[l+1],s.push(h[0],h[1]),r){case"end":u[o]=v[o],u[1-o]=h[1-o],s.push(u[0],u[1]);break;case"middle":var p=(h[o]+v[o])/2,d=[];u[o]=d[o]=p,u[1-o]=h[1-o],d[1-o]=v[1-o],s.push(u[0],u[1]),s.push(d[0],d[1]);break;default:u[o]=h[o],u[1-o]=v[1-o],s.push(u[0],u[1])}return s.push(a[l++],a[l++]),s}function gu(a,e){var t=[],r=a.length,i,n;function o(h,v,c){var f=h.coord,p=(c-f)/(v.coord-f),d=Es(p,[h.color,v.color]);return{coord:c,color:d}}for(var s=0;s<r;s++){var l=a[s],u=l.coord;if(u<0)i=l;else if(u>e){n?t.push(o(n,l,e)):i&&t.push(o(i,l,0),o(i,l,e));break}else i&&(t.push(o(i,l,0)),i=null),t.push(l),n=l}return t}function yu(a,e,t){var r=a.getVisual("visualMeta");if(!(!r||!r.length||!a.count())&&e.type==="cartesian2d"){for(var i,n,o=r.length-1;o>=0;o--){var s=a.getDimensionInfo(r[o].dimension);if(i=s&&s.coordDim,i==="x"||i==="y"){n=r[o];break}}if(n){var l=e.getAxis(i),u=E(n.stops,function(S){return{coord:l.toGlobalCoord(l.dataToCoord(S.value)),color:S.color}}),h=u.length,v=n.outerColors.slice();h&&u[0].coord>u[h-1].coord&&(u.reverse(),v.reverse());var c=gu(u,i==="x"?t.getWidth():t.getHeight()),f=c.length;if(!f&&h)return u[0].coord<0?v[1]?v[1]:u[h-1].color:v[0]?v[0]:u[0].color;var p=10,d=c[0].coord-p,g=c[f-1].coord+p,y=g-d;if(y<.001)return"transparent";w(c,function(S){S.offset=(S.coord-d)/y}),c.push({offset:f?c[f-1].offset:.5,color:v[1]||"transparent"}),c.unshift({offset:f?c[0].offset:.5,color:v[0]||"transparent"});var m=new no(0,0,0,0,c,!0);return m[i]=d,m[i+"2"]=g,m}}}function mu(a,e,t){var r=a.get("showAllSymbol"),i=r==="auto";if(!(r&&!i)){var n=t.getAxesByScale("ordinal")[0];if(n&&!(i&&xu(n,e))){var o=e.mapDimension(n.dim),s={};return w(n.getViewLabels(),function(l){var u=n.scale.getRawOrdinalNumber(l.tickValue);s[u]=1}),function(l){return!s.hasOwnProperty(e.get(o,l))}}}}function xu(a,e){var t=a.getExtent(),r=Math.abs(t[1]-t[0])/a.scale.count();isNaN(r)&&(r=0);for(var i=e.count(),n=Math.max(1,Math.round(i/5)),o=0;o<i;o+=n)if(Ca.getSymbolSize(e,o)[a.isHorizontal()?1:0]*1.5>r)return!1;return!0}function Su(a,e){return isNaN(a)||isNaN(e)}function bu(a){for(var e=a.length/2;e>0&&Su(a[e*2-2],a[e*2-1]);e--);return e-1}function mi(a,e){return[a[e*2],a[e*2+1]]}function _u(a,e,t){for(var r=a.length/2,i=t==="x"?0:1,n,o,s=0,l=-1,u=0;u<r;u++)if(o=a[u*2+i],!(isNaN(o)||isNaN(a[u*2+1-i]))){if(u===0){n=o;continue}if(n<=e&&o>=e||n>=e&&o<=e){l=u;break}s=u,n=o}return{range:[s,l],t:(e-n)/(o-n)}}function Bo(a){if(a.get(["endLabel","show"]))return!0;for(var e=0;e<Ze.length;e++)if(a.get([Ze[e],"endLabel","show"]))return!0;return!1}function yr(a,e,t,r){if(ne(e,"cartesian2d")){var i=r.getModel("endLabel"),n=i.get("valueAnimation"),o=r.getData(),s={lastFrameIndex:0},l=Bo(r)?function(f,p){a._endLabelOnDuring(f,p,o,s,n,i,e)}:null,u=e.getBaseAxis().isHorizontal(),h=Oo(e,t,r,function(){var f=a._endLabel;f&&t&&s.originalX!=null&&f.attr({x:s.originalX,y:s.originalY})},l);if(!r.get("clip",!0)){var v=h.shape,c=Math.max(v.width,v.height);u?(v.y-=c,v.height+=c*2):(v.x-=c,v.width+=c*2)}return l&&l(1,h),h}else return Vo(e,t,r)}function Au(a,e){var t=e.getBaseAxis(),r=t.isHorizontal(),i=t.inverse,n=r?i?"right":"left":"center",o=r?"middle":i?"top":"bottom";return{normal:{align:a.get("align")||n,verticalAlign:a.get("verticalAlign")||o}}}var wu=function(a){I(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.init=function(){var t=new $,r=new ko;this.group.add(r.group),this._symbolDraw=r,this._lineGroup=t,this._changePolyState=R(this._changePolyState,this)},e.prototype.render=function(t,r,i){var n=t.coordinateSystem,o=this.group,s=t.getData(),l=t.getModel("lineStyle"),u=t.getModel("areaStyle"),h=s.getLayout("points")||[],v=n.type==="polar",c=this._coordSys,f=this._symbolDraw,p=this._polyline,d=this._polygon,g=this._lineGroup,y=!r.ssr&&t.get("animation"),m=!u.isEmpty(),S=u.get("origin"),x=Po(n,s,S),b=m&&du(n,s,x),_=t.get("showSymbol"),A=t.get("connectNulls"),D=_&&!v&&mu(t,s,n),M=this._data;M&&M.eachItemGraphicEl(function(Y,hr){Y.__temp&&(o.remove(Y),M.setItemGraphicEl(hr,null))}),_||f.remove(),o.add(g);var T=v?!1:t.get("step"),C;n&&n.getArea&&t.get("clip",!0)&&(C=n.getArea(),C.width!=null?(C.x-=.1,C.y-=.1,C.width+=.2,C.height+=.2):C.r0&&(C.r0-=.5,C.r+=.5)),this._clipShapeForSymbol=C;var L=yu(s,n,i)||s.getVisual("style")[s.getVisual("drawType")];if(!(p&&c.type===n.type&&T===this._step))_&&f.updateData(s,{isIgnore:D,clipShape:C,disableAnimation:!0,getSymbolPoint:function(Y){return[h[Y*2],h[Y*2+1]]}}),y&&this._initSymbolLabelAnimation(s,n,C),T&&(b&&(b=Tt(b,h,n,T,A)),h=Tt(h,null,n,T,A)),p=this._newPolyline(h),m?d=this._newPolygon(h,b):d&&(g.remove(d),d=this._polygon=null),v||this._initOrUpdateEndLabel(t,n,Ka(L)),g.setClipPath(yr(this,n,!0,t));else{m&&!d?d=this._newPolygon(h,b):d&&!m&&(g.remove(d),d=this._polygon=null),v||this._initOrUpdateEndLabel(t,n,Ka(L));var P=g.getClipPath();if(P){var B=yr(this,n,!1,t);_t(P,{shape:B.shape},t)}else g.setClipPath(yr(this,n,!0,t));_&&f.updateData(s,{isIgnore:D,clipShape:C,disableAnimation:!0,getSymbolPoint:function(Y){return[h[Y*2],h[Y*2+1]]}}),(!pi(this._stackedOnPoints,b)||!pi(this._points,h))&&(y?this._doUpdateAnimation(s,b,n,i,T,S,A):(T&&(b&&(b=Tt(b,h,n,T,A)),h=Tt(h,null,n,T,A)),p.setShape({points:h}),d&&d.setShape({points:h,stackedOnPoints:b})))}var V=t.getModel("emphasis"),q=V.get("focus"),j=V.get("blurScope"),z=V.get("disabled");if(p.useStyle(ht(l.getLineStyle(),{fill:"none",stroke:L,lineJoin:"bevel"})),pe(p,t,"lineStyle"),p.style.lineWidth>0&&t.get(["emphasis","lineStyle","width"])==="bolder"){var W=p.getState("emphasis").style;W.lineWidth=+p.style.lineWidth+1}at(p).seriesIndex=t.seriesIndex,Ft(p,q,j,z);var Q=yi(t.get("smooth")),N=t.get("smoothMonotone");if(p.setShape({smooth:Q,smoothMonotone:N,connectNulls:A}),d){var lt=s.getCalculationInfo("stackedOnSeries"),K=0;d.useStyle(ht(u.getAreaStyle(),{fill:L,opacity:.7,lineJoin:"bevel",decal:s.getVisual("style").decal})),lt&&(K=yi(lt.get("smooth"))),d.setShape({smooth:Q,stackedOnSmooth:K,smoothMonotone:N,connectNulls:A}),pe(d,t,"areaStyle"),at(d).seriesIndex=t.seriesIndex,Ft(d,q,j,z)}var X=this._changePolyState;s.eachItemGraphicEl(function(Y){Y&&(Y.onHoverStateChange=X)}),this._polyline.onHoverStateChange=X,this._data=s,this._coordSys=n,this._stackedOnPoints=b,this._points=h,this._step=T,this._valueOrigin=S,t.get("triggerLineEvent")&&(this.packEventData(t,p),d&&this.packEventData(t,d))},e.prototype.packEventData=function(t,r){at(r).eventData={componentType:"series",componentSubType:"line",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"line"}},e.prototype.highlight=function(t,r,i,n){var o=t.getData(),s=ja(o,n);if(this._changePolyState("emphasis"),!(s instanceof Array)&&s!=null&&s>=0){var l=o.getLayout("points"),u=o.getItemGraphicEl(s);if(!u){var h=l[s*2],v=l[s*2+1];if(isNaN(h)||isNaN(v)||this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(h,v))return;var c=t.get("zlevel")||0,f=t.get("z")||0;u=new Ca(o,s),u.x=h,u.y=v,u.setZ(c,f);var p=u.getSymbolPath().getTextContent();p&&(p.zlevel=c,p.z=f,p.z2=this._polyline.z2+1),u.__temp=!0,o.setItemGraphicEl(s,u),u.stopSymbolAnimation(!0),this.group.add(u)}u.highlight()}else Qt.prototype.highlight.call(this,t,r,i,n)},e.prototype.downplay=function(t,r,i,n){var o=t.getData(),s=ja(o,n);if(this._changePolyState("normal"),s!=null&&s>=0){var l=o.getItemGraphicEl(s);l&&(l.__temp?(o.setItemGraphicEl(s,null),this.group.remove(l)):l.downplay())}else Qt.prototype.downplay.call(this,t,r,i,n)},e.prototype._changePolyState=function(t){var r=this._polygon;qa(this._polyline,t),r&&qa(r,t)},e.prototype._newPolyline=function(t){var r=this._polyline;return r&&this._lineGroup.remove(r),r=new cu({shape:{points:t},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(r),this._polyline=r,r},e.prototype._newPolygon=function(t,r){var i=this._polygon;return i&&this._lineGroup.remove(i),i=new pu({shape:{points:t,stackedOnPoints:r},segmentIgnoreThreshold:2}),this._lineGroup.add(i),this._polygon=i,i},e.prototype._initSymbolLabelAnimation=function(t,r,i){var n,o,s=r.getBaseAxis(),l=s.inverse;r.type==="cartesian2d"?(n=s.isHorizontal(),o=!1):r.type==="polar"&&(n=s.dim==="angle",o=!0);var u=t.hostModel,h=u.get("animationDuration");rt(h)&&(h=h(null));var v=u.get("animationDelay")||0,c=rt(v)?v(null):v;t.eachItemGraphicEl(function(f,p){var d=f;if(d){var g=[f.x,f.y],y=void 0,m=void 0,S=void 0;if(i)if(o){var x=i,b=r.pointToCoord(g);n?(y=x.startAngle,m=x.endAngle,S=-b[1]/180*Math.PI):(y=x.r0,m=x.r,S=b[0])}else{var _=i;n?(y=_.x,m=_.x+_.width,S=f.x):(y=_.y+_.height,m=_.y,S=f.y)}var A=m===y?0:(S-y)/(m-y);l&&(A=1-A);var D=rt(v)?v(p):h*A+c,M=d.getSymbolPath(),T=M.getTextContent();d.attr({scaleX:0,scaleY:0}),d.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:D}),T&&T.animateFrom({style:{opacity:0}},{duration:300,delay:D}),M.disableLabelAnimation=!0}})},e.prototype._initOrUpdateEndLabel=function(t,r,i){var n=t.getModel("endLabel");if(Bo(t)){var o=t.getData(),s=this._polyline,l=o.getLayout("points");if(!l){s.removeTextContent(),this._endLabel=null;return}var u=this._endLabel;u||(u=this._endLabel=new tt({z2:200}),u.ignoreClip=!0,s.setTextContent(this._endLabel),s.disableLabelAnimation=!0);var h=bu(l);h>=0&&(Me(s,Xt(t,"endLabel"),{inheritColor:i,labelFetcher:t,labelDataIndex:h,defaultText:function(v,c,f){return f!=null?Io(o,f):Ma(o,v)},enableTextSetter:!0},Au(n,r)),s.textConfig.position=null)}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},e.prototype._endLabelOnDuring=function(t,r,i,n,o,s,l){var u=this._endLabel,h=this._polyline;if(u){t<1&&n.originalX==null&&(n.originalX=u.x,n.originalY=u.y);var v=i.getLayout("points"),c=i.hostModel,f=c.get("connectNulls"),p=s.get("precision"),d=s.get("distance")||0,g=l.getBaseAxis(),y=g.isHorizontal(),m=g.inverse,S=r.shape,x=m?y?S.x:S.y+S.height:y?S.x+S.width:S.y,b=(y?d:0)*(m?-1:1),_=(y?0:-d)*(m?-1:1),A=y?"x":"y",D=_u(v,x,A),M=D.range,T=M[1]-M[0],C=void 0;if(T>=1){if(T>1&&!f){var L=mi(v,M[0]);u.attr({x:L[0]+b,y:L[1]+_}),o&&(C=c.getRawValue(M[0]))}else{var L=h.getPointOn(x,A);L&&u.attr({x:L[0]+b,y:L[1]+_});var P=c.getRawValue(M[0]),B=c.getRawValue(M[1]);o&&(C=Rs(i,p,P,B,D.t))}n.lastFrameIndex=M[0]}else{var V=t===1||n.lastFrameIndex>0?M[0]:0,L=mi(v,V);o&&(C=c.getRawValue(V)),u.attr({x:L[0]+b,y:L[1]+_})}if(o){var q=io(u);typeof q.setLabelText=="function"&&q.setLabelText(C)}}},e.prototype._doUpdateAnimation=function(t,r,i,n,o,s,l){var u=this._polyline,h=this._polygon,v=t.hostModel,c=vu(this._data,t,this._stackedOnPoints,r,this._coordSys,i,this._valueOrigin),f=c.current,p=c.stackedOnCurrent,d=c.next,g=c.stackedOnNext;if(o&&(p=Tt(c.stackedOnCurrent,c.current,i,o,l),f=Tt(c.current,null,i,o,l),g=Tt(c.stackedOnNext,c.next,i,o,l),d=Tt(c.next,null,i,o,l)),gi(f,d)>3e3||h&&gi(p,g)>3e3){u.stopAnimation(),u.setShape({points:d}),h&&(h.stopAnimation(),h.setShape({points:d,stackedOnPoints:g}));return}u.shape.__points=c.current,u.shape.points=f;var y={shape:{points:d}};c.current!==f&&(y.shape.__points=c.next),u.stopAnimation(),ut(u,y,v),h&&(h.setShape({points:f,stackedOnPoints:p}),h.stopAnimation(),ut(h,{shape:{stackedOnPoints:g}},v),u.shape.points!==h.shape.points&&(h.shape.points=u.shape.points));for(var m=[],S=c.status,x=0;x<S.length;x++){var b=S[x].cmd;if(b==="="){var _=t.getItemGraphicEl(S[x].idx1);_&&m.push({el:_,ptIdx:x})}}u.animators&&u.animators.length&&u.animators[0].during(function(){h&&h.dirtyShape();for(var A=u.shape.__points,D=0;D<m.length;D++){var M=m[D].el,T=m[D].ptIdx*2;M.x=A[T],M.y=A[T+1],M.markRedraw()}})},e.prototype.remove=function(t){var r=this.group,i=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),i&&i.eachItemGraphicEl(function(n,o){n.__temp&&(r.remove(n),i.setItemGraphicEl(o,null))}),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},e.type="line",e}(Qt);function Da(a,e){return{seriesType:a,plan:ma(),reset:function(t){var r=t.getData(),i=t.coordinateSystem,n=t.pipelineContext,o=e||n.large;if(i){var s=E(i.dimensions,function(f){return r.mapDimension(f)}).slice(0,2),l=s.length,u=r.getCalculationInfo("stackResultDimension");fe(r,s[0])&&(s[0]=u),fe(r,s[1])&&(s[1]=u);var h=r.getStore(),v=r.getDimensionIndex(s[0]),c=r.getDimensionIndex(s[1]);return l&&{progress:function(f,p){for(var d=f.end-f.start,g=o&&zt(d*l),y=[],m=[],S=f.start,x=0;S<f.end;S++){var b=void 0;if(l===1){var _=h.get(v,S);b=i.dataToPoint(_,null,m)}else y[0]=h.get(v,S),y[1]=h.get(c,S),b=i.dataToPoint(y,null,m);o?(g[x++]=b[0],g[x++]=b[1]):p.setItemLayout(S,b.slice())}o&&p.setLayout("points",g)}}}}}}var Mu={average:function(a){for(var e=0,t=0,r=0;r<a.length;r++)isNaN(a[r])||(e+=a[r],t++);return t===0?NaN:e/t},sum:function(a){for(var e=0,t=0;t<a.length;t++)e+=a[t]||0;return e},max:function(a){for(var e=-1/0,t=0;t<a.length;t++)a[t]>e&&(e=a[t]);return isFinite(e)?e:NaN},min:function(a){for(var e=1/0,t=0;t<a.length;t++)a[t]<e&&(e=a[t]);return isFinite(e)?e:NaN},nearest:function(a){return a[0]}},Cu=function(a){return Math.round(a.length/2)};function No(a){return{seriesType:a,reset:function(e,t,r){var i=e.getData(),n=e.get("sampling"),o=e.coordinateSystem,s=i.count();if(s>10&&o.type==="cartesian2d"&&n){var l=o.getBaseAxis(),u=o.getOtherAxis(l),h=l.getExtent(),v=r.getDevicePixelRatio(),c=Math.abs(h[1]-h[0])*(v||1),f=Math.round(s/c);if(isFinite(f)&&f>1){n==="lttb"?e.setData(i.lttbDownSample(i.mapDimension(u.dim),1/f)):n==="minmax"&&e.setData(i.minmaxDownSample(i.mapDimension(u.dim),1/f));var p=void 0;it(n)?p=Mu[n]:rt(n)&&(p=n),p&&e.setData(i.downSample(i.mapDimension(u.dim),1/f,p,Cu))}}}}}function If(a){a.registerChartView(wu),a.registerSeriesModel(su),a.registerLayout(Da("line",!0)),a.registerVisual({seriesType:"line",reset:function(e){var t=e.getData(),r=e.getModel("lineStyle").getLineStyle();r&&!r.stroke&&(r.stroke=t.getVisual("style").fill),t.setVisual("legendLineStyle",r)}}),a.registerProcessor(a.PRIORITY.PROCESSOR.STATISTIC,No("line"))}var qr=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,r){return sr(null,this,{useEncodeDefaulter:!0})},e.prototype.getMarkerPosition=function(t,r,i){var n=this.coordinateSystem;if(n&&n.clampData){var o=n.clampData(t),s=n.dataToPoint(o);if(i)w(n.getAxes(),function(c,f){if(c.type==="category"&&r!=null){var p=c.getTicksCoords(),d=c.getTickModel().get("alignWithLabel"),g=o[f],y=r[f]==="x1"||r[f]==="y1";if(y&&!d&&(g+=1),p.length<2)return;if(p.length===2){s[f]=c.toGlobalCoord(c.getExtent()[y?1:0]);return}for(var m=void 0,S=void 0,x=1,b=0;b<p.length;b++){var _=p[b].coord,A=b===p.length-1?p[b-1].tickValue+x:p[b].tickValue;if(A===g){S=_;break}else if(A<g)m=_;else if(m!=null&&A>g){S=(_+m)/2;break}b===1&&(x=A-p[0].tickValue)}S==null&&(m?m&&(S=p[p.length-1].coord):S=p[0].coord),s[f]=c.toGlobalCoord(S)}});else{var l=this.getData(),u=l.getLayout("offset"),h=l.getLayout("size"),v=n.getBaseAxis().isHorizontal()?0:1;s[v]+=u+h/2}return s}return[NaN,NaN]},e.type="series.__base_bar__",e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"},e}(we);we.registerClass(qr);var Du=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(){return sr(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},e.prototype.getProgressive=function(){return this.get("large")?this.get("progressive"):!1},e.prototype.getProgressiveThreshold=function(){var t=this.get("progressiveThreshold"),r=this.get("largeThreshold");return r>t&&(t=r),t},e.prototype.brushSelector=function(t,r,i){return i.rect(r.getItemLayout(t))},e.type="series.bar",e.dependencies=["grid","polar"],e.defaultOption=ie(qr.defaultOption,{clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1}),e}(qr),Tu=function(){function a(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return a}(),xi=function(a){I(e,a);function e(t){var r=a.call(this,t)||this;return r.type="sausage",r}return e.prototype.getDefaultShape=function(){return new Tu},e.prototype.buildPath=function(t,r){var i=r.cx,n=r.cy,o=Math.max(r.r0||0,0),s=Math.max(r.r,0),l=(s-o)*.5,u=o+l,h=r.startAngle,v=r.endAngle,c=r.clockwise,f=Math.PI*2,p=c?v-h<f:h-v<f;p||(h=v-(c?f:-f));var d=Math.cos(h),g=Math.sin(h),y=Math.cos(v),m=Math.sin(v);p?(t.moveTo(d*o+i,g*o+n),t.arc(d*u+i,g*u+n,l,-Math.PI+h,h,!c)):t.moveTo(d*s+i,g*s+n),t.arc(i,n,s,h,v,!c),t.arc(y*u+i,m*u+n,l,v-Math.PI*2,v-Math.PI,!c),o!==0&&t.arc(i,n,o,v,h,c)},e}(wt);function Lu(a,e){e=e||{};var t=e.isRoundCap;return function(r,i,n){var o=i.position;if(!o||o instanceof Array)return Qa(r,i,n);var s=a(o),l=i.distance!=null?i.distance:5,u=this.shape,h=u.cx,v=u.cy,c=u.r,f=u.r0,p=(c+f)/2,d=u.startAngle,g=u.endAngle,y=(d+g)/2,m=t?Math.abs(c-f)/2:0,S=Math.cos,x=Math.sin,b=h+c*S(d),_=v+c*x(d),A="left",D="top";switch(s){case"startArc":b=h+(f-l)*S(y),_=v+(f-l)*x(y),A="center",D="top";break;case"insideStartArc":b=h+(f+l)*S(y),_=v+(f+l)*x(y),A="center",D="bottom";break;case"startAngle":b=h+p*S(d)+De(d,l+m,!1),_=v+p*x(d)+Te(d,l+m,!1),A="right",D="middle";break;case"insideStartAngle":b=h+p*S(d)+De(d,-l+m,!1),_=v+p*x(d)+Te(d,-l+m,!1),A="left",D="middle";break;case"middle":b=h+p*S(y),_=v+p*x(y),A="center",D="middle";break;case"endArc":b=h+(c+l)*S(y),_=v+(c+l)*x(y),A="center",D="bottom";break;case"insideEndArc":b=h+(c-l)*S(y),_=v+(c-l)*x(y),A="center",D="top";break;case"endAngle":b=h+p*S(g)+De(g,l+m,!0),_=v+p*x(g)+Te(g,l+m,!0),A="left",D="middle";break;case"insideEndAngle":b=h+p*S(g)+De(g,-l+m,!0),_=v+p*x(g)+Te(g,-l+m,!0),A="right",D="middle";break;default:return Qa(r,i,n)}return r=r||{},r.x=b,r.y=_,r.align=A,r.verticalAlign=D,r}}function Iu(a,e,t,r){if(ar(r)){a.setTextConfig({rotation:r});return}else if(O(e)){a.setTextConfig({rotation:0});return}var i=a.shape,n=i.clockwise?i.startAngle:i.endAngle,o=i.clockwise?i.endAngle:i.startAngle,s=(n+o)/2,l,u=t(e);switch(u){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":l=s;break;case"startAngle":case"insideStartAngle":l=n;break;case"endAngle":case"insideEndAngle":l=o;break;default:a.setTextConfig({rotation:0});return}var h=Math.PI*1.5-l;u==="middle"&&h>Math.PI/2&&h<Math.PI*1.5&&(h-=Math.PI),a.setTextConfig({rotation:h})}function De(a,e,t){return e*Math.sin(a)*(t?-1:1)}function Te(a,e,t){return e*Math.cos(a)*(t?1:-1)}var mr=Math.max,xr=Math.min;function ku(a,e){var t=a.getArea&&a.getArea();if(ne(a,"cartesian2d")){var r=a.getBaseAxis();if(r.type!=="category"||!r.onBand){var i=e.getLayout("bandWidth");r.isHorizontal()?(t.x-=i,t.width+=i*2):(t.y-=i,t.height+=i*2)}}return t}var Pu=function(a){I(e,a);function e(){var t=a.call(this)||this;return t.type=e.type,t._isFirstFrame=!0,t}return e.prototype.render=function(t,r,i,n){this._model=t,this._removeOnRenderedListener(i),this._updateDrawMode(t);var o=t.get("coordinateSystem");(o==="cartesian2d"||o==="polar")&&(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(t,r,i):this._renderNormal(t,r,i,n))},e.prototype.incrementalPrepareRender=function(t){this._clear(),this._updateDrawMode(t),this._updateLargeClip(t)},e.prototype.incrementalRender=function(t,r){this._progressiveEls=[],this._incrementalRenderLarge(t,r)},e.prototype.eachRendered=function(t){rr(this._progressiveEls||this.group,t)},e.prototype._updateDrawMode=function(t){var r=t.pipelineContext.large;(this._isLargeDraw==null||r!==this._isLargeDraw)&&(this._isLargeDraw=r,this._clear())},e.prototype._renderNormal=function(t,r,i,n){var o=this.group,s=t.getData(),l=this._data,u=t.coordinateSystem,h=u.getBaseAxis(),v;u.type==="cartesian2d"?v=h.isHorizontal():u.type==="polar"&&(v=h.dim==="angle");var c=t.isAnimationEnabled()?t:null,f=Ru(t,u);f&&this._enableRealtimeSort(f,s,i);var p=t.get("clip",!0)||f,d=ku(u,s);o.removeClipPath();var g=t.get("roundCap",!0),y=t.get("showBackground",!0),m=t.getModel("backgroundStyle"),S=m.get("borderRadius")||0,x=[],b=this._backgroundEls,_=n&&n.isInitSort,A=n&&n.type==="changeAxisOrder";function D(C){var L=Le[u.type](s,C),P=Gu(u,v,L);return P.useStyle(m.getItemStyle()),u.type==="cartesian2d"?P.setShape("r",S):P.setShape("cornerRadius",S),x[C]=P,P}s.diff(l).add(function(C){var L=s.getItemModel(C),P=Le[u.type](s,C,L);if(y&&D(C),!(!s.hasValue(C)||!wi[u.type](P))){var B=!1;p&&(B=Si[u.type](d,P));var V=bi[u.type](t,s,C,P,v,c,h.model,!1,g);f&&(V.forceLabelAnimation=!0),Mi(V,s,C,L,P,t,v,u.type==="polar"),_?V.attr({shape:P}):f?_i(f,c,V,P,C,v,!1,!1):_t(V,{shape:P},t,C),s.setItemGraphicEl(C,V),o.add(V),V.ignore=B}}).update(function(C,L){var P=s.getItemModel(C),B=Le[u.type](s,C,P);if(y){var V=void 0;b.length===0?V=D(L):(V=b[L],V.useStyle(m.getItemStyle()),u.type==="cartesian2d"?V.setShape("r",S):V.setShape("cornerRadius",S),x[C]=V);var q=Le[u.type](s,C),j=Fo(v,q,u);ut(V,{shape:j},c,C)}var z=l.getItemGraphicEl(L);if(!s.hasValue(C)||!wi[u.type](B)){o.remove(z);return}var W=!1;if(p&&(W=Si[u.type](d,B),W&&o.remove(z)),z?da(z):z=bi[u.type](t,s,C,B,v,c,h.model,!!z,g),f&&(z.forceLabelAnimation=!0),A){var Q=z.getTextContent();if(Q){var N=io(Q);N.prevValue!=null&&(N.prevValue=N.value)}}else Mi(z,s,C,P,B,t,v,u.type==="polar");_?z.attr({shape:B}):f?_i(f,c,z,B,C,v,!0,A):ut(z,{shape:B},t,C,null),s.setItemGraphicEl(C,z),z.ignore=W,o.add(z)}).remove(function(C){var L=l.getItemGraphicEl(C);L&&Ja(L,t,C)}).execute();var M=this._backgroundGroup||(this._backgroundGroup=new $);M.removeAll();for(var T=0;T<x.length;++T)M.add(x[T]);o.add(M),this._backgroundEls=x,this._data=s},e.prototype._renderLarge=function(t,r,i){this._clear(),Di(t,this.group),this._updateLargeClip(t)},e.prototype._incrementalRenderLarge=function(t,r){this._removeBackground(),Di(r,this.group,this._progressiveEls,!0)},e.prototype._updateLargeClip=function(t){var r=t.get("clip",!0)&&zo(t.coordinateSystem,!1,t),i=this.group;r?i.setClipPath(r):i.removeClipPath()},e.prototype._enableRealtimeSort=function(t,r,i){var n=this;if(r.count()){var o=t.baseAxis;if(this._isFirstFrame)this._dispatchInitSort(r,t,i),this._isFirstFrame=!1;else{var s=function(l){var u=r.getItemGraphicEl(l),h=u&&u.shape;return h&&Math.abs(o.isHorizontal()?h.height:h.width)||0};this._onRendered=function(){n._updateSortWithinSameData(r,s,o,i)},i.getZr().on("rendered",this._onRendered)}}},e.prototype._dataSort=function(t,r,i){var n=[];return t.each(t.mapDimension(r.dim),function(o,s){var l=i(s);l=l??NaN,n.push({dataIndex:s,mappedValue:l,ordinalNumber:o})}),n.sort(function(o,s){return s.mappedValue-o.mappedValue}),{ordinalNumbers:E(n,function(o){return o.ordinalNumber})}},e.prototype._isOrderChangedWithinSameData=function(t,r,i){for(var n=i.scale,o=t.mapDimension(i.dim),s=Number.MAX_VALUE,l=0,u=n.getOrdinalMeta().categories.length;l<u;++l){var h=t.rawIndexOf(o,n.getRawOrdinalNumber(l)),v=h<0?Number.MIN_VALUE:r(t.indexOfRawIndex(h));if(v>s)return!0;s=v}return!1},e.prototype._isOrderDifferentInView=function(t,r){for(var i=r.scale,n=i.getExtent(),o=Math.max(0,n[0]),s=Math.min(n[1],i.getOrdinalMeta().categories.length-1);o<=s;++o)if(t.ordinalNumbers[o]!==i.getRawOrdinalNumber(o))return!0},e.prototype._updateSortWithinSameData=function(t,r,i,n){if(this._isOrderChangedWithinSameData(t,r,i)){var o=this._dataSort(t,i,r);this._isOrderDifferentInView(o,i)&&(this._removeOnRenderedListener(n),n.dispatchAction({type:"changeAxisOrder",componentType:i.dim+"Axis",axisId:i.index,sortInfo:o}))}},e.prototype._dispatchInitSort=function(t,r,i){var n=r.baseAxis,o=this._dataSort(t,n,function(s){return t.get(t.mapDimension(r.otherAxis.dim),s)});i.dispatchAction({type:"changeAxisOrder",componentType:n.dim+"Axis",isInitSort:!0,axisId:n.index,sortInfo:o})},e.prototype.remove=function(t,r){this._clear(this._model),this._removeOnRenderedListener(r)},e.prototype.dispose=function(t,r){this._removeOnRenderedListener(r)},e.prototype._removeOnRenderedListener=function(t){this._onRendered&&(t.getZr().off("rendered",this._onRendered),this._onRendered=null)},e.prototype._clear=function(t){var r=this.group,i=this._data;t&&t.isAnimationEnabled()&&i&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],i.eachItemGraphicEl(function(n){Ja(n,t,at(n).dataIndex)})):r.removeAll(),this._data=null,this._isFirstFrame=!0},e.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},e.type="bar",e}(Qt),Si={cartesian2d:function(a,e){var t=e.width<0?-1:1,r=e.height<0?-1:1;t<0&&(e.x+=e.width,e.width=-e.width),r<0&&(e.y+=e.height,e.height=-e.height);var i=a.x+a.width,n=a.y+a.height,o=mr(e.x,a.x),s=xr(e.x+e.width,i),l=mr(e.y,a.y),u=xr(e.y+e.height,n),h=s<o,v=u<l;return e.x=h&&o>i?s:o,e.y=v&&l>n?u:l,e.width=h?0:s-o,e.height=v?0:u-l,t<0&&(e.x+=e.width,e.width=-e.width),r<0&&(e.y+=e.height,e.height=-e.height),h||v},polar:function(a,e){var t=e.r0<=e.r?1:-1;if(t<0){var r=e.r;e.r=e.r0,e.r0=r}var i=xr(e.r,a.r),n=mr(e.r0,a.r0);e.r=i,e.r0=n;var o=i-n<0;if(t<0){var r=e.r;e.r=e.r0,e.r0=r}return o}},bi={cartesian2d:function(a,e,t,r,i,n,o,s,l){var u=new nt({shape:F({},r),z2:1});if(u.__dataIndex=t,u.name="item",n){var h=u.shape,v=i?"height":"width";h[v]=0}return u},polar:function(a,e,t,r,i,n,o,s,l){var u=!i&&l?xi:ya,h=new u({shape:r,z2:1});h.name="item";var v=Go(i);if(h.calculateTextPosition=Lu(v,{isRoundCap:u===xi}),n){var c=h.shape,f=i?"r":"endAngle",p={};c[f]=i?r.r0:r.startAngle,p[f]=r[f],(s?ut:_t)(h,{shape:p},n)}return h}};function Ru(a,e){var t=a.get("realtimeSort",!0),r=e.getBaseAxis();if(t&&r.type==="category"&&e.type==="cartesian2d")return{baseAxis:r,otherAxis:e.getOtherAxis(r)}}function _i(a,e,t,r,i,n,o,s){var l,u;n?(u={x:r.x,width:r.width},l={y:r.y,height:r.height}):(u={y:r.y,height:r.height},l={x:r.x,width:r.width}),s||(o?ut:_t)(t,{shape:l},e,i,null);var h=e?a.baseAxis.model:null;(o?ut:_t)(t,{shape:u},h,i)}function Ai(a,e){for(var t=0;t<e.length;t++)if(!isFinite(a[e[t]]))return!0;return!1}var Eu=["x","y","width","height"],Ou=["cx","cy","r","startAngle","endAngle"],wi={cartesian2d:function(a){return!Ai(a,Eu)},polar:function(a){return!Ai(a,Ou)}},Le={cartesian2d:function(a,e,t){var r=a.getItemLayout(e),i=t?zu(t,r):0,n=r.width>0?1:-1,o=r.height>0?1:-1;return{x:r.x+n*i/2,y:r.y+o*i/2,width:r.width-n*i,height:r.height-o*i}},polar:function(a,e,t){var r=a.getItemLayout(e);return{cx:r.cx,cy:r.cy,r0:r.r0,r:r.r,startAngle:r.startAngle,endAngle:r.endAngle,clockwise:r.clockwise}}};function Vu(a){return a.startAngle!=null&&a.endAngle!=null&&a.startAngle===a.endAngle}function Go(a){return function(e){var t=e?"Arc":"Angle";return function(r){switch(r){case"start":case"insideStart":case"end":case"insideEnd":return r+t;default:return r}}}(a)}function Mi(a,e,t,r,i,n,o,s){var l=e.getItemVisual(t,"style");if(s){if(!n.get("roundCap")){var h=a.shape,v=Os(r.getModel("itemStyle"),h,!0);F(h,v),a.setShape(h)}}else{var u=r.get(["itemStyle","borderRadius"])||0;a.setShape("r",u)}a.useStyle(l);var c=r.getShallow("cursor");c&&a.attr("cursor",c);var f=s?o?i.r>=i.r0?"endArc":"startArc":i.endAngle>=i.startAngle?"endAngle":"startAngle":o?i.height>=0?"bottom":"top":i.width>=0?"right":"left",p=Xt(r);Me(a,p,{labelFetcher:n,labelDataIndex:t,defaultText:Ma(n.getData(),t),inheritColor:l.fill,defaultOpacity:l.opacity,defaultOutsidePosition:f});var d=a.getTextContent();if(s&&d){var g=r.get(["label","position"]);a.textConfig.inside=g==="middle"?!0:null,Iu(a,g==="outside"?f:g,Go(o),r.get(["label","rotate"]))}Vs(d,p,n.getRawValue(t),function(m){return Io(e,m)});var y=r.getModel(["emphasis"]);Ft(a,y.get("focus"),y.get("blurScope"),y.get("disabled")),pe(a,r),Vu(i)&&(a.style.fill="none",a.style.stroke="none",w(a.states,function(m){m.style&&(m.style.fill=m.style.stroke="none")}))}function zu(a,e){var t=a.get(["itemStyle","borderColor"]);if(!t||t==="none")return 0;var r=a.get(["itemStyle","borderWidth"])||0,i=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),n=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(r,i,n)}var Bu=function(){function a(){}return a}(),Ci=function(a){I(e,a);function e(t){var r=a.call(this,t)||this;return r.type="largeBar",r}return e.prototype.getDefaultShape=function(){return new Bu},e.prototype.buildPath=function(t,r){for(var i=r.points,n=this.baseDimIdx,o=1-this.baseDimIdx,s=[],l=[],u=this.barWidth,h=0;h<i.length;h+=3)l[n]=u,l[o]=i[h+2],s[n]=i[h+n],s[o]=i[h+o],t.rect(s[0],s[1],l[0],l[1])},e}(wt);function Di(a,e,t,r){var i=a.getData(),n=i.getLayout("valueAxisHorizontal")?1:0,o=i.getLayout("largeDataIndices"),s=i.getLayout("size"),l=a.getModel("backgroundStyle"),u=i.getLayout("largeBackgroundPoints");if(u){var h=new Ci({shape:{points:u},incremental:!!r,silent:!0,z2:0});h.baseDimIdx=n,h.largeDataIndices=o,h.barWidth=s,h.useStyle(l.getItemStyle()),e.add(h),t&&t.push(h)}var v=new Ci({shape:{points:i.getLayout("largePoints")},incremental:!!r,ignoreCoarsePointer:!0,z2:1});v.baseDimIdx=n,v.largeDataIndices=o,v.barWidth=s,e.add(v),v.useStyle(i.getVisual("style")),v.style.stroke=null,at(v).seriesIndex=a.seriesIndex,a.get("silent")||(v.on("mousedown",Ti),v.on("mousemove",Ti)),t&&t.push(v)}var Ti=zs(function(a){var e=this,t=Nu(e,a.offsetX,a.offsetY);at(e).dataIndex=t>=0?t:null},30,!1);function Nu(a,e,t){for(var r=a.baseDimIdx,i=1-r,n=a.shape.points,o=a.largeDataIndices,s=[],l=[],u=a.barWidth,h=0,v=n.length/3;h<v;h++){var c=h*3;if(l[r]=u,l[i]=n[c+2],s[r]=n[c+r],s[i]=n[c+i],l[i]<0&&(s[i]+=l[i],l[i]=-l[i]),e>=s[0]&&e<=s[0]+l[0]&&t>=s[1]&&t<=s[1]+l[1])return o[h]}return-1}function Fo(a,e,t){if(ne(t,"cartesian2d")){var r=e,i=t.getArea();return{x:a?r.x:i.x,y:a?i.y:r.y,width:a?r.width:i.width,height:a?i.height:r.height}}else{var i=t.getArea(),n=e;return{cx:i.cx,cy:i.cy,r0:a?i.r0:n.r0,r:a?i.r:n.r,startAngle:a?n.startAngle:0,endAngle:a?n.endAngle:Math.PI*2}}}function Gu(a,e,t){var r=a.type==="polar"?ya:nt;return new r({shape:Fo(e,t,a),silent:!0,z2:0})}function kf(a){a.registerChartView(Pu),a.registerSeriesModel(Du),a.registerLayout(a.PRIORITY.VISUAL.LAYOUT,G(Ns,"bar")),a.registerLayout(a.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,Bs("bar")),a.registerProcessor(a.PRIORITY.PROCESSOR.STATISTIC,No("bar")),a.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},function(e,t){var r=e.componentType||"series";t.eachComponent({mainType:r,query:e},function(i){e.sortInfo&&i.axis.setCategorySortInfo(e.sortInfo)})})}var Fu=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t,r){return sr(null,this,{useEncodeDefaulter:!0})},e.prototype.getProgressive=function(){var t=this.option.progressive;return t??(this.option.large?5e3:this.get("progressive"))},e.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return t??(this.option.large?1e4:this.get("progressiveThreshold"))},e.prototype.brushSelector=function(t,r,i){return i.point(r.getItemLayout(t))},e.prototype.getZLevelKey=function(){return this.getData().count()>this.getProgressiveThreshold()?this.id:""},e.type="series.scatter",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,symbolSize:10,large:!1,largeThreshold:2e3,itemStyle:{opacity:.8},emphasis:{scale:!0},clip:!0,select:{itemStyle:{borderColor:"#212121"}},universalTransition:{divideShape:"clone"}},e}(we),Wo=4,Wu=function(){function a(){}return a}(),Hu=function(a){I(e,a);function e(t){var r=a.call(this,t)||this;return r._off=0,r.hoverDataIdx=-1,r}return e.prototype.getDefaultShape=function(){return new Wu},e.prototype.reset=function(){this.notClear=!1,this._off=0},e.prototype.buildPath=function(t,r){var i=r.points,n=r.size,o=this.symbolProxy,s=o.shape,l=t.getContext?t.getContext():t,u=l&&n[0]<Wo,h=this.softClipShape,v;if(u){this._ctx=l;return}for(this._ctx=null,v=this._off;v<i.length;){var c=i[v++],f=i[v++];isNaN(c)||isNaN(f)||h&&!h.contain(c,f)||(s.x=c-n[0]/2,s.y=f-n[1]/2,s.width=n[0],s.height=n[1],o.buildPath(t,s,!0))}this.incremental&&(this._off=v,this.notClear=!0)},e.prototype.afterBrush=function(){var t=this.shape,r=t.points,i=t.size,n=this._ctx,o=this.softClipShape,s;if(n){for(s=this._off;s<r.length;){var l=r[s++],u=r[s++];isNaN(l)||isNaN(u)||o&&!o.contain(l,u)||n.fillRect(l-i[0]/2,u-i[1]/2,i[0],i[1])}this.incremental&&(this._off=s,this.notClear=!0)}},e.prototype.findDataIndex=function(t,r){for(var i=this.shape,n=i.points,o=i.size,s=Math.max(o[0],4),l=Math.max(o[1],4),u=n.length/2-1;u>=0;u--){var h=u*2,v=n[h]-s/2,c=n[h+1]-l/2;if(t>=v&&r>=c&&t<=v+s&&r<=c+l)return u}return-1},e.prototype.contain=function(t,r){var i=this.transformCoordToLocal(t,r),n=this.getBoundingRect();if(t=i[0],r=i[1],n.contain(t,r)){var o=this.hoverDataIdx=this.findDataIndex(t,r);return o>=0}return this.hoverDataIdx=-1,!1},e.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var r=this.shape,i=r.points,n=r.size,o=n[0],s=n[1],l=1/0,u=1/0,h=-1/0,v=-1/0,c=0;c<i.length;){var f=i[c++],p=i[c++];l=Math.min(f,l),h=Math.max(f,h),u=Math.min(p,u),v=Math.max(p,v)}t=this._rect=new de(l-o/2,u-s/2,h-l+o,v-u+s)}return t},e}(wt),Zu=function(){function a(){this.group=new $}return a.prototype.updateData=function(e,t){this._clear();var r=this._create();r.setShape({points:e.getLayout("points")}),this._setCommon(r,e,t)},a.prototype.updateLayout=function(e){var t=e.getLayout("points");this.group.eachChild(function(r){if(r.startIndex!=null){var i=(r.endIndex-r.startIndex)*2,n=r.startIndex*4*2;t=new Float32Array(t.buffer,n,i)}r.setShape("points",t),r.reset()})},a.prototype.incrementalPrepareUpdate=function(e){this._clear()},a.prototype.incrementalUpdate=function(e,t,r){var i=this._newAdded[0],n=t.getLayout("points"),o=i&&i.shape.points;if(o&&o.length<2e4){var s=o.length,l=new Float32Array(s+n.length);l.set(o),l.set(n,s),i.endIndex=e.end,i.setShape({points:l})}else{this._newAdded=[];var u=this._create();u.startIndex=e.start,u.endIndex=e.end,u.incremental=!0,u.setShape({points:n}),this._setCommon(u,t,r)}},a.prototype.eachRendered=function(e){this._newAdded[0]&&e(this._newAdded[0])},a.prototype._create=function(){var e=new Hu({cursor:"default"});return e.ignoreCoarsePointer=!0,this.group.add(e),this._newAdded.push(e),e},a.prototype._setCommon=function(e,t,r){var i=t.hostModel;r=r||{};var n=t.getVisual("symbolSize");e.setShape("size",n instanceof Array?n:[n,n]),e.softClipShape=r.clipShape||null,e.symbolProxy=yt(t.getVisual("symbol"),0,0,0,0),e.setColor=e.symbolProxy.setColor;var o=e.shape.size[0]<Wo;e.useStyle(i.getModel("itemStyle").getItemStyle(o?["color","shadowBlur","shadowColor"]:["color"]));var s=t.getVisual("style"),l=s&&s.fill;l&&e.setColor(l);var u=at(e);u.seriesIndex=i.seriesIndex,e.on("mousemove",function(h){u.dataIndex=null;var v=e.hoverDataIdx;v>=0&&(u.dataIndex=v+(e.startIndex||0))})},a.prototype.remove=function(){this._clear()},a.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},a}(),Uu=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n=t.getData(),o=this._updateSymbolDraw(n,t);o.updateData(n,{clipShape:this._getClipShape(t)}),this._finished=!0},e.prototype.incrementalPrepareRender=function(t,r,i){var n=t.getData(),o=this._updateSymbolDraw(n,t);o.incrementalPrepareUpdate(n),this._finished=!1},e.prototype.incrementalRender=function(t,r,i){this._symbolDraw.incrementalUpdate(t,r.getData(),{clipShape:this._getClipShape(r)}),this._finished=t.end===r.getData().count()},e.prototype.updateTransform=function(t,r,i){var n=t.getData();if(this.group.dirty(),!this._finished||n.count()>1e4)return{update:!0};var o=Da("").reset(t,r,i);o.progress&&o.progress({start:0,end:n.count(),count:n.count()},n),this._symbolDraw.updateLayout(n)},e.prototype.eachRendered=function(t){this._symbolDraw&&this._symbolDraw.eachRendered(t)},e.prototype._getClipShape=function(t){if(t.get("clip",!0)){var r=t.coordinateSystem;return r&&r.getArea&&r.getArea(.1)}},e.prototype._updateSymbolDraw=function(t,r){var i=this._symbolDraw,n=r.pipelineContext,o=n.large;return(!i||o!==this._isLargeDraw)&&(i&&i.remove(),i=this._symbolDraw=o?new Zu:new ko,this._isLargeDraw=o,this.group.removeAll()),this.group.add(i.group),i},e.prototype.remove=function(t,r){this._symbolDraw&&this._symbolDraw.remove(!0),this._symbolDraw=null},e.prototype.dispose=function(){},e.type="scatter",e}(Qt),Xu=function(a){I(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.type="grid",e.dependencies=["xAxis","yAxis"],e.layoutMode="box",e.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},e}(Mt),Qr=function(a){I(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",dt).models[0]},e.type="cartesian2dAxis",e}(Mt);ir(Qr,ql);var Ho={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,showMinLine:!0,showMaxLine:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},Yu=Z({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},Ho),Ta=Z({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},Ho),$u=Z({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},Ta),Ku=ht({logBase:10},Ta);const ju={category:Yu,value:Ta,time:$u,log:Ku};var qu={value:1,category:1,time:1,log:1};function Li(a,e,t,r){w(qu,function(i,n){var o=Z(Z({},ju[n],!0),r,!0),s=function(l){I(u,l);function u(){var h=l!==null&&l.apply(this,arguments)||this;return h.type=e+"Axis."+n,h}return u.prototype.mergeDefaultAndTheme=function(h,v){var c=Gs(this),f=c?xa(h):{},p=v.getTheme();Z(h,p.get(n+"Axis")),Z(h,this.getDefaultOption()),h.type=Ii(h),c&&Sa(h,f,c)},u.prototype.optionUpdated=function(){var h=this.option;h.type==="category"&&(this.__ordinalMeta=Fs.createByAxisModel(this))},u.prototype.getCategories=function(h){var v=this.option;if(v.type==="category")return h?v.data:this.__ordinalMeta.categories},u.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},u.type=e+"Axis."+n,u.defaultOption=o,u}(t);a.registerComponentModel(s)}),a.registerSubTypeDefaulter(e+"Axis",Ii)}function Ii(a){return a.type||(a.data?"category":"value")}var Qu=function(){function a(e){this.type="cartesian",this._dimList=[],this._axes={},this.name=e||""}return a.prototype.getAxis=function(e){return this._axes[e]},a.prototype.getAxes=function(){return E(this._dimList,function(e){return this._axes[e]},this)},a.prototype.getAxesByScale=function(e){return e=e.toLowerCase(),Pt(this.getAxes(),function(t){return t.scale.type===e})},a.prototype.addAxis=function(e){var t=e.dim;this._axes[t]=e,this._dimList.push(t)},a}(),Jr=["x","y"];function ki(a){return a.type==="interval"||a.type==="time"}var Ju=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="cartesian2d",t.dimensions=Jr,t}return e.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var t=this.getAxis("x").scale,r=this.getAxis("y").scale;if(!(!ki(t)||!ki(r))){var i=t.getExtent(),n=r.getExtent(),o=this.dataToPoint([i[0],n[0]]),s=this.dataToPoint([i[1],n[1]]),l=i[1]-i[0],u=n[1]-n[0];if(!(!l||!u)){var h=(s[0]-o[0])/l,v=(s[1]-o[1])/u,c=o[0]-i[0]*h,f=o[1]-n[0]*v,p=this._transform=[h,0,0,v,c,f];this._invTransform=Ws([],p)}}},e.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},e.prototype.containPoint=function(t){var r=this.getAxis("x"),i=this.getAxis("y");return r.contain(r.toLocalCoord(t[0]))&&i.contain(i.toLocalCoord(t[1]))},e.prototype.containData=function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},e.prototype.containZone=function(t,r){var i=this.dataToPoint(t),n=this.dataToPoint(r),o=this.getArea(),s=new de(i[0],i[1],n[0]-i[0],n[1]-i[1]);return o.intersect(s)},e.prototype.dataToPoint=function(t,r,i){i=i||[];var n=t[0],o=t[1];if(this._transform&&n!=null&&isFinite(n)&&o!=null&&isFinite(o))return ti(i,t,this._transform);var s=this.getAxis("x"),l=this.getAxis("y");return i[0]=s.toGlobalCoord(s.dataToCoord(n,r)),i[1]=l.toGlobalCoord(l.dataToCoord(o,r)),i},e.prototype.clampData=function(t,r){var i=this.getAxis("x").scale,n=this.getAxis("y").scale,o=i.getExtent(),s=n.getExtent(),l=i.parse(t[0]),u=n.parse(t[1]);return r=r||[],r[0]=Math.min(Math.max(Math.min(o[0],o[1]),l),Math.max(o[0],o[1])),r[1]=Math.min(Math.max(Math.min(s[0],s[1]),u),Math.max(s[0],s[1])),r},e.prototype.pointToData=function(t,r){var i=[];if(this._invTransform)return ti(i,t,this._invTransform);var n=this.getAxis("x"),o=this.getAxis("y");return i[0]=n.coordToData(n.toLocalCoord(t[0]),r),i[1]=o.coordToData(o.toLocalCoord(t[1]),r),i},e.prototype.getOtherAxis=function(t){return this.getAxis(t.dim==="x"?"y":"x")},e.prototype.getArea=function(t){t=t||0;var r=this.getAxis("x").getGlobalExtent(),i=this.getAxis("y").getGlobalExtent(),n=Math.min(r[0],r[1])-t,o=Math.min(i[0],i[1])-t,s=Math.max(r[0],r[1])-n+t,l=Math.max(i[0],i[1])-o+t;return new de(n,o,s,l)},e}(Qu),th=function(a){I(e,a);function e(t,r,i,n,o){var s=a.call(this,t,r,i)||this;return s.index=0,s.type=n||"value",s.position=o||"bottom",s}return e.prototype.isHorizontal=function(){var t=this.position;return t==="top"||t==="bottom"},e.prototype.getGlobalExtent=function(t){var r=this.getExtent();return r[0]=this.toGlobalCoord(r[0]),r[1]=this.toGlobalCoord(r[1]),t&&r[0]>r[1]&&r.reverse(),r},e.prototype.pointToData=function(t,r){return this.coordToData(this.toLocalCoord(t[this.dim==="x"?0:1]),r)},e.prototype.setCategorySortInfo=function(t){if(this.type!=="category")return!1;this.model.option.categorySortInfo=t,this.scale.setSortInfo(t)},e}(Lo),Sr=Math.log;function eh(a,e,t){var r=oo.prototype,i=r.getTicks.call(t),n=r.getTicks.call(t,!0),o=i.length-1,s=r.getInterval.call(t),l=Hs(a,e),u=l.extent,h=l.fixMin,v=l.fixMax;if(a.type==="log"){var c=Sr(a.base);u=[Sr(u[0])/c,Sr(u[1])/c]}a.setExtent(u[0],u[1]),a.calcNiceExtent({splitNumber:o,fixMin:h,fixMax:v});var f=r.getExtent.call(a);h&&(u[0]=f[0]),v&&(u[1]=f[1]);var p=r.getInterval.call(a),d=u[0],g=u[1];if(h&&v)p=(g-d)/o;else if(h)for(g=u[0]+p*o;g<u[1]&&isFinite(g)&&isFinite(u[1]);)p=vr(p),g=u[0]+p*o;else if(v)for(d=u[1]-p*o;d>u[0]&&isFinite(d)&&isFinite(u[0]);)p=vr(p),d=u[1]-p*o;else{var y=a.getTicks().length-1;y>o&&(p=vr(p));var m=p*o;g=Math.ceil(u[1]/p)*p,d=St(g-m),d<0&&u[0]>=0?(d=0,g=St(m)):g>0&&u[1]<=0&&(g=0,d=-St(m))}var S=(i[0].value-n[0].value)/s,x=(i[o].value-n[o].value)/s;r.setExtent.call(a,d+p*S,g+p*x),r.setInterval.call(a,p),(S||x)&&r.setNiceExtent.call(a,d+p,g-p)}var rh=function(){function a(e,t,r){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=Jr,this._initCartesian(e,t,r),this.model=e}return a.prototype.getRect=function(){return this._rect},a.prototype.update=function(e,t){var r=this._axesMap;this._updateScale(e,this.model);function i(o){var s,l=Rt(o),u=l.length;if(u){for(var h=[],v=u-1;v>=0;v--){var c=+l[v],f=o[c],p=f.model,d=f.scale;Xr(d)&&p.get("alignTicks")&&p.get("interval")==null?h.push(f):(ai(d,p),Xr(d)&&(s=f))}h.length&&(s||(s=h.pop(),ai(s.scale,s.model)),w(h,function(g){eh(g.scale,g.model,s.scale)}))}}i(r.x),i(r.y);var n={};w(r.x,function(o){Pi(r,"y",o,n)}),w(r.y,function(o){Pi(r,"x",o,n)}),this.resize(this.model,t)},a.prototype.resize=function(e,t,r){var i=e.getBoxLayoutParams(),n=!r&&e.get("containLabel"),o=Ce(i,{width:t.getWidth(),height:t.getHeight()});this._rect=o;var s=this._axesList;l(),n&&(w(s,function(u){if(!u.model.get(["axisLabel","inside"])){var h=Zs(u);if(h){var v=u.isHorizontal()?"height":"width",c=u.model.get(["axisLabel","margin"]);o[v]-=h[v]+c,u.position==="top"?o.y+=h.height+c:u.position==="left"&&(o.x+=h.width+c)}}}),l()),w(this._coordsList,function(u){u.calcAffineTransform()});function l(){w(s,function(u){var h=u.isHorizontal(),v=h?[0,o.width]:[0,o.height],c=u.inverse?1:0;u.setExtent(v[c],v[1-c]),ah(u,h?o.x:o.y)})}},a.prototype.getAxis=function(e,t){var r=this._axesMap[e];if(r!=null)return r[t||0]},a.prototype.getAxes=function(){return this._axesList.slice()},a.prototype.getCartesian=function(e,t){if(e!=null&&t!=null){var r="x"+e+"y"+t;return this._coordsMap[r]}st(e)&&(t=e.yAxisIndex,e=e.xAxisIndex);for(var i=0,n=this._coordsList;i<n.length;i++)if(n[i].getAxis("x").index===e||n[i].getAxis("y").index===t)return n[i]},a.prototype.getCartesians=function(){return this._coordsList.slice()},a.prototype.convertToPixel=function(e,t,r){var i=this._findConvertTarget(t);return i.cartesian?i.cartesian.dataToPoint(r):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(r)):null},a.prototype.convertFromPixel=function(e,t,r){var i=this._findConvertTarget(t);return i.cartesian?i.cartesian.pointToData(r):i.axis?i.axis.coordToData(i.axis.toLocalCoord(r)):null},a.prototype._findConvertTarget=function(e){var t=e.seriesModel,r=e.xAxisModel||t&&t.getReferringComponents("xAxis",dt).models[0],i=e.yAxisModel||t&&t.getReferringComponents("yAxis",dt).models[0],n=e.gridModel,o=this._coordsList,s,l;if(t)s=t.coordinateSystem,et(o,s)<0&&(s=null);else if(r&&i)s=this.getCartesian(r.componentIndex,i.componentIndex);else if(r)l=this.getAxis("x",r.componentIndex);else if(i)l=this.getAxis("y",i.componentIndex);else if(n){var u=n.coordinateSystem;u===this&&(s=this._coordsList[0])}return{cartesian:s,axis:l}},a.prototype.containPoint=function(e){var t=this._coordsList[0];if(t)return t.containPoint(e)},a.prototype._initCartesian=function(e,t,r){var i=this,n=this,o={left:!1,right:!1,top:!1,bottom:!1},s={x:{},y:{}},l={x:0,y:0};if(t.eachComponent("xAxis",u("x"),this),t.eachComponent("yAxis",u("y"),this),!l.x||!l.y){this._axesMap={},this._axesList=[];return}this._axesMap=s,w(s.x,function(h,v){w(s.y,function(c,f){var p="x"+v+"y"+f,d=new Ju(p);d.master=i,d.model=e,i._coordsMap[p]=d,i._coordsList.push(d),d.addAxis(h),d.addAxis(c)})});function u(h){return function(v,c){if(br(v,e)){var f=v.get("position");h==="x"?f!=="top"&&f!=="bottom"&&(f=o.bottom?"top":"bottom"):f!=="left"&&f!=="right"&&(f=o.left?"right":"left"),o[f]=!0;var p=new th(h,Us(v),[0,0],v.get("type"),f),d=p.type==="category";p.onBand=d&&v.get("boundaryGap"),p.inverse=v.get("inverse"),v.axis=p,p.model=v,p.grid=n,p.index=c,n._axesList.push(p),s[h][c]=p,l[h]++}}}},a.prototype._updateScale=function(e,t){w(this._axesList,function(i){if(i.scale.setExtent(1/0,-1/0),i.type==="category"){var n=i.model.get("categorySortInfo");i.scale.setSortInfo(n)}}),e.eachSeries(function(i){if(ei(i)){var n=ri(i),o=n.xAxisModel,s=n.yAxisModel;if(!br(o,t)||!br(s,t))return;var l=this.getCartesian(o.componentIndex,s.componentIndex),u=i.getData(),h=l.getAxis("x"),v=l.getAxis("y");r(u,h),r(u,v)}},this);function r(i,n){w(Xs(i,n.dim),function(o){n.scale.unionExtentFromData(i,o)})}},a.prototype.getTooltipAxes=function(e){var t=[],r=[];return w(this.getCartesians(),function(i){var n=e!=null&&e!=="auto"?i.getAxis(e):i.getBaseAxis(),o=i.getOtherAxis(n);et(t,n)<0&&t.push(n),et(r,o)<0&&r.push(o)}),{baseAxes:t,otherAxes:r}},a.create=function(e,t){var r=[];return e.eachComponent("grid",function(i,n){var o=new a(i,e,t);o.name="grid_"+n,o.resize(i,t,!0),i.coordinateSystem=o,r.push(o)}),e.eachSeries(function(i){if(ei(i)){var n=ri(i),o=n.xAxisModel,s=n.yAxisModel,l=o.getCoordSysModel(),u=l.coordinateSystem;i.coordinateSystem=u.getCartesian(o.componentIndex,s.componentIndex)}}),r},a.dimensions=Jr,a}();function br(a,e){return a.getCoordSysModel()===e}function Pi(a,e,t,r){t.getAxesOnZeroOf=function(){return n?[n]:[]};var i=a[e],n,o=t.model,s=o.get(["axisLine","onZero"]),l=o.get(["axisLine","onZeroAxisIndex"]);if(!s)return;if(l!=null)Ri(i[l])&&(n=i[l]);else for(var u in i)if(i.hasOwnProperty(u)&&Ri(i[u])&&!r[h(i[u])]){n=i[u];break}n&&(r[h(n)]=!0);function h(v){return v.dim+"_"+v.index}}function Ri(a){return a&&a.type!=="category"&&a.type!=="time"&&Ys(a)}function ah(a,e){var t=a.getExtent(),r=t[0]+t[1];a.toGlobalCoord=a.dim==="x"?function(i){return i+e}:function(i){return r-i+e},a.toLocalCoord=a.dim==="x"?function(i){return i-e}:function(i){return r-i+e}}var ta=ft();function ih(a,e,t,r){var i=t.axis;if(!i.scale.isBlank()){var n=t.getModel("splitArea"),o=n.getModel("areaStyle"),s=o.get("color"),l=r.coordinateSystem.getRect(),u=i.getTicksCoords({tickModel:n,clamp:!0});if(u.length){var h=s.length,v=ta(a).splitAreaColors,c=ot(),f=0;if(v)for(var p=0;p<u.length;p++){var d=v.get(u[p].tickValue);if(d!=null){f=(d+(h-1)*p)%h;break}}var g=i.toGlobalCoord(u[0].coord),y=o.getAreaStyle();s=O(s)?s:[s];for(var p=1;p<u.length;p++){var m=i.toGlobalCoord(u[p].coord),S=void 0,x=void 0,b=void 0,_=void 0;i.isHorizontal()?(S=g,x=l.y,b=m-S,_=l.height,g=S+b):(S=l.x,x=g,b=l.width,_=m-x,g=x+_);var A=u[p-1].tickValue;A!=null&&c.set(A,f),e.add(new nt({anid:A!=null?"area_"+A:null,shape:{x:S,y:x,width:b,height:_},style:ht({fill:s[f]},y),autoBatch:!0,silent:!0})),f=(f+1)%h}ta(a).splitAreaColors=c}}}function nh(a){ta(a).splitAreaColors=null}var oh=["axisLine","axisTickLabel","axisName"],sh=["splitArea","splitLine","minorSplitLine"],Zo=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="CartesianAxisPointer",t}return e.prototype.render=function(t,r,i,n){this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new $,this.group.add(this._axisGroup),!!t.get("show")){var s=t.getCoordSysModel(),l=$s(s,t),u=new Ks(t,F({handleAutoShown:function(v){for(var c=s.coordinateSystem.getCartesians(),f=0;f<c.length;f++)if(Xr(c[f].getOtherAxis(t.axis).scale))return!0;return!1}},l));w(oh,u.add,u),this._axisGroup.add(u.getGroup()),w(sh,function(v){t.get([v,"show"])&&lh[v](this,this._axisGroup,t,s)},this);var h=n&&n.type==="changeAxisOrder"&&n.isInitSort;h||js(o,this._axisGroup,t),a.prototype.render.call(this,t,r,i,n)}},e.prototype.remove=function(){nh(this)},e.type="cartesianAxis",e}(qs),lh={splitLine:function(a,e,t,r){var i=t.axis;if(!i.scale.isBlank()){var n=t.getModel("splitLine"),o=n.getModel("lineStyle"),s=o.get("color"),l=n.get("showMinLine")!==!1,u=n.get("showMaxLine")!==!1;s=O(s)?s:[s];for(var h=r.coordinateSystem.getRect(),v=i.isHorizontal(),c=0,f=i.getTicksCoords({tickModel:n}),p=[],d=[],g=o.getLineStyle(),y=0;y<f.length;y++){var m=i.toGlobalCoord(f[y].coord);if(!(y===0&&!l||y===f.length-1&&!u)){var S=f[y].tickValue;v?(p[0]=m,p[1]=h.y,d[0]=m,d[1]=h.y+h.height):(p[0]=h.x,p[1]=m,d[0]=h.x+h.width,d[1]=m);var x=c++%s.length,b=new ge({anid:S!=null?"line_"+S:null,autoBatch:!0,shape:{x1:p[0],y1:p[1],x2:d[0],y2:d[1]},style:ht({stroke:s[x]},g),silent:!0});ii(b.shape,g.lineWidth),e.add(b)}}}},minorSplitLine:function(a,e,t,r){var i=t.axis,n=t.getModel("minorSplitLine"),o=n.getModel("lineStyle"),s=r.coordinateSystem.getRect(),l=i.isHorizontal(),u=i.getMinorTicksCoords();if(u.length)for(var h=[],v=[],c=o.getLineStyle(),f=0;f<u.length;f++)for(var p=0;p<u[f].length;p++){var d=i.toGlobalCoord(u[f][p].coord);l?(h[0]=d,h[1]=s.y,v[0]=d,v[1]=s.y+s.height):(h[0]=s.x,h[1]=d,v[0]=s.x+s.width,v[1]=d);var g=new ge({anid:"minor_line_"+u[f][p].tickValue,autoBatch:!0,shape:{x1:h[0],y1:h[1],x2:v[0],y2:v[1]},style:c,silent:!0});ii(g.shape,c.lineWidth),e.add(g)}},splitArea:function(a,e,t,r){ih(a,e,t,r)}},Uo=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="xAxis",e}(Zo),uh=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=Uo.type,t}return e.type="yAxis",e}(Zo),hh=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="grid",t}return e.prototype.render=function(t,r){this.group.removeAll(),t.get("show")&&this.group.add(new nt({shape:t.coordinateSystem.getRect(),style:ht({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))},e.type="grid",e}(Et),Ei={offset:0};function Xo(a){a.registerComponentView(hh),a.registerComponentModel(Xu),a.registerCoordinateSystem("cartesian2d",rh),Li(a,"x",Qr,Ei),Li(a,"y",Qr,Ei),a.registerComponentView(Uo),a.registerComponentView(uh),a.registerPreprocessor(function(e){e.xAxis&&e.yAxis&&!e.grid&&(e.grid={})})}function Pf(a){kt(Xo),a.registerSeriesModel(Fu),a.registerChartView(Uu),a.registerLayout(Da("scatter"))}var Oi="\0_ec_interaction_mutex";function vh(a,e,t){var r=La(a);r[e]=t}function ch(a,e,t){var r=La(a),i=r[e];i===t&&(r[e]=null)}function Vi(a,e){return!!La(a)[e]}function La(a){return a[Oi]||(a[Oi]={})}nr({type:"takeGlobalCursor",event:"globalCursorTaken",update:"update"},so);var fh=function(a){I(e,a);function e(t){var r=a.call(this)||this;r._zr=t;var i=R(r._mousedownHandler,r),n=R(r._mousemoveHandler,r),o=R(r._mouseupHandler,r),s=R(r._mousewheelHandler,r),l=R(r._pinchHandler,r);return r.enable=function(u,h){this.disable(),this._opt=ht(U(h)||{},{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),u==null&&(u=!0),(u===!0||u==="move"||u==="pan")&&(t.on("mousedown",i),t.on("mousemove",n),t.on("mouseup",o)),(u===!0||u==="scale"||u==="zoom")&&(t.on("mousewheel",s),t.on("pinch",l))},r.disable=function(){t.off("mousedown",i),t.off("mousemove",n),t.off("mouseup",o),t.off("mousewheel",s),t.off("pinch",l)},r}return e.prototype.isDragging=function(){return this._dragging},e.prototype.isPinching=function(){return this._pinching},e.prototype.setPointerChecker=function(t){this.pointerChecker=t},e.prototype.dispose=function(){this.disable()},e.prototype._mousedownHandler=function(t){if(!ni(t)){for(var r=t.target;r;){if(r.draggable)return;r=r.__hostTarget||r.parent}var i=t.offsetX,n=t.offsetY;this.pointerChecker&&this.pointerChecker(t,i,n)&&(this._x=i,this._y=n,this._dragging=!0)}},e.prototype._mousemoveHandler=function(t){if(!(!this._dragging||!Ne("moveOnMouseMove",t,this._opt)||t.gestureEvent==="pinch"||Vi(this._zr,"globalPan"))){var r=t.offsetX,i=t.offsetY,n=this._x,o=this._y,s=r-n,l=i-o;this._x=r,this._y=i,this._opt.preventDefaultMouseMove&&Wt(t.event),Yo(this,"pan","moveOnMouseMove",t,{dx:s,dy:l,oldX:n,oldY:o,newX:r,newY:i,isAvailableBehavior:null})}},e.prototype._mouseupHandler=function(t){ni(t)||(this._dragging=!1)},e.prototype._mousewheelHandler=function(t){var r=Ne("zoomOnMouseWheel",t,this._opt),i=Ne("moveOnMouseWheel",t,this._opt),n=t.wheelDelta,o=Math.abs(n),s=t.offsetX,l=t.offsetY;if(!(n===0||!r&&!i)){if(r){var u=o>3?1.4:o>1?1.2:1.1,h=n>0?u:1/u;_r(this,"zoom","zoomOnMouseWheel",t,{scale:h,originX:s,originY:l,isAvailableBehavior:null})}if(i){var v=Math.abs(n),c=(n>0?1:-1)*(v>3?.4:v>1?.15:.05);_r(this,"scrollMove","moveOnMouseWheel",t,{scrollDelta:c,originX:s,originY:l,isAvailableBehavior:null})}}},e.prototype._pinchHandler=function(t){if(!Vi(this._zr,"globalPan")){var r=t.pinchScale>1?1.1:1/1.1;_r(this,"zoom",null,t,{scale:r,originX:t.pinchX,originY:t.pinchY,isAvailableBehavior:null})}},e}(lo);function _r(a,e,t,r,i){a.pointerChecker&&a.pointerChecker(r,i.originX,i.originY)&&(Wt(r.event),Yo(a,e,t,r,i))}function Yo(a,e,t,r,i){i.isAvailableBehavior=R(Ne,null,t,r),a.trigger(e,i)}function Ne(a,e,t){var r=t[a];return!a||r&&(!it(r)||e.event[r+"Key"])}var ph={axisPointer:1,tooltip:1,brush:1};function dh(a,e,t){var r=e.getComponentByElement(a.topTarget),i=r&&r.coordinateSystem;return r&&r!==t&&!ph.hasOwnProperty(r.mainType)&&i&&i.model!==t}var Se=w,gh=st,Xe=-1,J=function(){function a(e){var t=e.mappingMethod,r=e.type,i=this.option=U(e);this.type=r,this.mappingMethod=t,this._normalizeData=xh[t];var n=a.visualHandlers[r];this.applyVisual=n.applyVisual,this.getColorMapper=n.getColorMapper,this._normalizedToVisual=n._normalizedToVisual[t],t==="piecewise"?(Ar(i),yh(i)):t==="category"?i.categories?mh(i):Ar(i,!0):(Qs(t!=="linear"||i.dataExtent),Ar(i))}return a.prototype.mapValueToVisual=function(e){var t=this._normalizeData(e);return this._normalizedToVisual(t,e)},a.prototype.getNormalizer=function(){return R(this._normalizeData,this)},a.listVisualTypes=function(){return Rt(a.visualHandlers)},a.isValidType=function(e){return a.visualHandlers.hasOwnProperty(e)},a.eachVisual=function(e,t,r){st(e)?w(e,t,r):t.call(r,e)},a.mapVisual=function(e,t,r){var i,n=O(e)?[]:st(e)?{}:(i=!0,null);return a.eachVisual(e,function(o,s){var l=t.call(r,o,s);i?n=l:n[s]=l}),n},a.retrieveVisuals=function(e){var t={},r;return e&&Se(a.visualHandlers,function(i,n){e.hasOwnProperty(n)&&(t[n]=e[n],r=!0)}),r?t:null},a.prepareVisualTypes=function(e){if(O(e))e=e.slice();else if(gh(e)){var t=[];Se(e,function(r,i){t.push(i)}),e=t}else return[];return e.sort(function(r,i){return i==="color"&&r!=="color"&&r.indexOf("color")===0?1:-1}),e},a.dependsOn=function(e,t){return t==="color"?!!(e&&e.indexOf(t)===0):e===t},a.findPieceIndex=function(e,t,r){for(var i,n=1/0,o=0,s=t.length;o<s;o++){var l=t[o].value;if(l!=null){if(l===e||it(l)&&l===e+"")return o;r&&c(l,o)}}for(var o=0,s=t.length;o<s;o++){var u=t[o],h=u.interval,v=u.close;if(h){if(h[0]===-1/0){if(ke(v[1],e,h[1]))return o}else if(h[1]===1/0){if(ke(v[0],h[0],e))return o}else if(ke(v[0],h[0],e)&&ke(v[1],e,h[1]))return o;r&&c(h[0],o),r&&c(h[1],o)}}if(r)return e===1/0?t.length-1:e===-1/0?0:i;function c(f,p){var d=Math.abs(f-e);d<n&&(n=d,i=p)}},a.visualHandlers={color:{applyVisual:se("color"),getColorMapper:function(){var e=this.option;return R(e.mappingMethod==="category"?function(t,r){return!r&&(t=this._normalizeData(t)),ce.call(this,t)}:function(t,r,i){var n=!!i;return!r&&(t=this._normalizeData(t)),i=fr(t,e.parsedVisual,i),n?i:cr(i,"rgba")},this)},_normalizedToVisual:{linear:function(e){return cr(fr(e,this.option.parsedVisual),"rgba")},category:ce,piecewise:function(e,t){var r=ra.call(this,t);return r==null&&(r=cr(fr(e,this.option.parsedVisual),"rgba")),r},fixed:Ot}},colorHue:Ie(function(e,t){return pr(e,t)}),colorSaturation:Ie(function(e,t){return pr(e,null,t)}),colorLightness:Ie(function(e,t){return pr(e,null,null,t)}),colorAlpha:Ie(function(e,t){return Yr(e,t)}),decal:{applyVisual:se("decal"),_normalizedToVisual:{linear:null,category:ce,piecewise:null,fixed:null}},opacity:{applyVisual:se("opacity"),_normalizedToVisual:ea([0,1])},liftZ:{applyVisual:se("liftZ"),_normalizedToVisual:{linear:Ot,category:Ot,piecewise:Ot,fixed:Ot}},symbol:{applyVisual:function(e,t,r){var i=this.mapValueToVisual(e);r("symbol",i)},_normalizedToVisual:{linear:zi,category:ce,piecewise:function(e,t){var r=ra.call(this,t);return r==null&&(r=zi.call(this,e)),r},fixed:Ot}},symbolSize:{applyVisual:se("symbolSize"),_normalizedToVisual:ea([0,1])}},a}();function yh(a){var e=a.pieceList;a.hasSpecialVisual=!1,w(e,function(t,r){t.originIndex=r,t.visual!=null&&(a.hasSpecialVisual=!0)})}function mh(a){var e=a.categories,t=a.categoryMap={},r=a.visual;if(Se(e,function(o,s){t[o]=s}),!O(r)){var i=[];st(r)?Se(r,function(o,s){var l=t[s];i[l??Xe]=o}):i[Xe]=r,r=$o(a,i)}for(var n=e.length-1;n>=0;n--)r[n]==null&&(delete t[e[n]],e.pop())}function Ar(a,e){var t=a.visual,r=[];st(t)?Se(t,function(n){r.push(n)}):t!=null&&r.push(t);var i={color:1,symbol:1};!e&&r.length===1&&!i.hasOwnProperty(a.type)&&(r[1]=r[0]),$o(a,r)}function Ie(a){return{applyVisual:function(e,t,r){var i=this.mapValueToVisual(e);r("color",a(t("color"),i))},_normalizedToVisual:ea([0,1])}}function zi(a){var e=this.option.visual;return e[Math.round(H(a,[0,1],[0,e.length-1],!0))]||{}}function se(a){return function(e,t,r){r(a,this.mapValueToVisual(e))}}function ce(a){var e=this.option.visual;return e[this.option.loop&&a!==Xe?a%e.length:a]}function Ot(){return this.option.visual[0]}function ea(a){return{linear:function(e){return H(e,a,this.option.visual,!0)},category:ce,piecewise:function(e,t){var r=ra.call(this,t);return r==null&&(r=H(e,a,this.option.visual,!0)),r},fixed:Ot}}function ra(a){var e=this.option,t=e.pieceList;if(e.hasSpecialVisual){var r=J.findPieceIndex(a,t),i=t[r];if(i&&i.visual)return i.visual[this.type]}}function $o(a,e){return a.visual=e,a.type==="color"&&(a.parsedVisual=E(e,function(t){var r=Js(t);return r||[0,0,0,1]})),e}var xh={linear:function(a){return H(a,this.option.dataExtent,[0,1],!0)},piecewise:function(a){var e=this.option.pieceList,t=J.findPieceIndex(a,e,!0);if(t!=null)return H(t,[0,e.length-1],[0,1],!0)},category:function(a){var e=this.option.categories?this.option.categoryMap[a]:a;return e??Xe},fixed:so};function ke(a,e,t){return a?e<=t:e<t}var Bi=ge.prototype,wr=tl.prototype,Ko=function(){function a(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return a}();(function(a){I(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e})(Ko);function Mr(a){return isNaN(+a.cpx1)||isNaN(+a.cpy1)}var Sh=function(a){I(e,a);function e(t){var r=a.call(this,t)||this;return r.type="ec-line",r}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Ko},e.prototype.buildPath=function(t,r){Mr(r)?Bi.buildPath.call(this,t,r):wr.buildPath.call(this,t,r)},e.prototype.pointAt=function(t){return Mr(this.shape)?Bi.pointAt.call(this,t):wr.pointAt.call(this,t)},e.prototype.tangentAt=function(t){var r=this.shape,i=Mr(r)?[r.x2-r.x1,r.y2-r.y1]:wr.tangentAt.call(this,t);return uo(i,i)},e}(wt),Cr=["fromSymbol","toSymbol"];function Ni(a){return"_"+a+"Type"}function Gi(a,e,t){var r=e.getItemVisual(t,a);if(!r||r==="none")return r;var i=e.getItemVisual(t,a+"Size"),n=e.getItemVisual(t,a+"Rotate"),o=e.getItemVisual(t,a+"Offset"),s=e.getItemVisual(t,a+"KeepAspect"),l=er(i),u=tr(o||0,l);return r+l+u+(n||"")+(s||"")}function Fi(a,e,t){var r=e.getItemVisual(t,a);if(!(!r||r==="none")){var i=e.getItemVisual(t,a+"Size"),n=e.getItemVisual(t,a+"Rotate"),o=e.getItemVisual(t,a+"Offset"),s=e.getItemVisual(t,a+"KeepAspect"),l=er(i),u=tr(o||0,l),h=yt(r,-l[0]/2+u[0],-l[1]/2+u[1],l[0],l[1],null,s);return h.__specifiedRotation=n==null||isNaN(n)?void 0:+n*Math.PI/180||0,h.name=a,h}}function bh(a){var e=new Sh({name:"line",subPixelOptimize:!0});return aa(e.shape,a),e}function aa(a,e){a.x1=e[0][0],a.y1=e[0][1],a.x2=e[1][0],a.y2=e[1][1],a.percent=1;var t=e[2];t?(a.cpx1=t[0],a.cpy1=t[1]):(a.cpx1=NaN,a.cpy1=NaN)}var _h=function(a){I(e,a);function e(t,r,i){var n=a.call(this)||this;return n._createLine(t,r,i),n}return e.prototype._createLine=function(t,r,i){var n=t.hostModel,o=t.getItemLayout(r),s=bh(o);s.shape.percent=0,_t(s,{shape:{percent:1}},n,r),this.add(s),w(Cr,function(l){var u=Fi(l,t,r);this.add(u),this[Ni(l)]=Gi(l,t,r)},this),this._updateCommonStl(t,r,i)},e.prototype.updateData=function(t,r,i){var n=t.hostModel,o=this.childOfName("line"),s=t.getItemLayout(r),l={shape:{}};aa(l.shape,s),ut(o,l,n,r),w(Cr,function(u){var h=Gi(u,t,r),v=Ni(u);if(this[v]!==h){this.remove(this.childOfName(u));var c=Fi(u,t,r);this.add(c)}this[v]=h},this),this._updateCommonStl(t,r,i)},e.prototype.getLinePath=function(){return this.childAt(0)},e.prototype._updateCommonStl=function(t,r,i){var n=t.hostModel,o=this.childOfName("line"),s=i&&i.emphasisLineStyle,l=i&&i.blurLineStyle,u=i&&i.selectLineStyle,h=i&&i.labelStatesModels,v=i&&i.emphasisDisabled,c=i&&i.focus,f=i&&i.blurScope;if(!i||t.hasItemOption){var p=t.getItemModel(r),d=p.getModel("emphasis");s=d.getModel("lineStyle").getLineStyle(),l=p.getModel(["blur","lineStyle"]).getLineStyle(),u=p.getModel(["select","lineStyle"]).getLineStyle(),v=d.get("disabled"),c=d.get("focus"),f=d.get("blurScope"),h=Xt(p)}var g=t.getItemVisual(r,"style"),y=g.stroke;o.useStyle(g),o.style.fill=null,o.style.strokeNoScale=!0,o.ensureState("emphasis").style=s,o.ensureState("blur").style=l,o.ensureState("select").style=u,w(Cr,function(_){var A=this.childOfName(_);if(A){A.setColor(y),A.style.opacity=g.opacity;for(var D=0;D<Ze.length;D++){var M=Ze[D],T=o.getState(M);if(T){var C=T.style||{},L=A.ensureState(M),P=L.style||(L.style={});C.stroke!=null&&(P[A.__isEmptyBrush?"stroke":"fill"]=C.stroke),C.opacity!=null&&(P.opacity=C.opacity)}}A.markRedraw()}},this);var m=n.getRawValue(r);Me(this,h,{labelDataIndex:r,labelFetcher:{getFormattedLabel:function(_,A){return n.getFormattedLabel(_,A,t.dataType)}},inheritColor:y||"#000",defaultOpacity:g.opacity,defaultText:(m==null?t.getName(r):isFinite(m)?St(m):m)+""});var S=this.getTextContent();if(S){var x=h.normal;S.__align=S.style.align,S.__verticalAlign=S.style.verticalAlign,S.__position=x.get("position")||"middle";var b=x.get("distance");O(b)||(b=[b,b]),S.__labelDistance=b}this.setTextConfig({position:null,local:!0,inside:!1}),Ft(this,c,f,v)},e.prototype.highlight=function(){We(this)},e.prototype.downplay=function(){He(this)},e.prototype.updateLayout=function(t,r){this.setLinePoints(t.getItemLayout(r))},e.prototype.setLinePoints=function(t){var r=this.childOfName("line");aa(r.shape,t),r.dirty()},e.prototype.beforeUpdate=function(){var t=this,r=t.childOfName("fromSymbol"),i=t.childOfName("toSymbol"),n=t.getTextContent();if(!r&&!i&&(!n||n.ignore))return;for(var o=1,s=this.parent;s;)s.scaleX&&(o/=s.scaleX),s=s.parent;var l=t.childOfName("line");if(!this.__dirty&&!l.__dirty)return;var u=l.shape.percent,h=l.pointAt(0),v=l.pointAt(u),c=el([],v,h);uo(c,c);function f(T,C){var L=T.__specifiedRotation;if(L==null){var P=l.tangentAt(C);T.attr("rotation",(C===1?-1:1)*Math.PI/2-Math.atan2(P[1],P[0]))}else T.attr("rotation",L)}if(r&&(r.setPosition(h),f(r,0),r.scaleX=r.scaleY=o*u,r.markRedraw()),i&&(i.setPosition(v),f(i,1),i.scaleX=i.scaleY=o*u,i.markRedraw()),n&&!n.ignore){n.x=n.y=0,n.originX=n.originY=0;var p=void 0,d=void 0,g=n.__labelDistance,y=g[0]*o,m=g[1]*o,S=u/2,x=l.tangentAt(S),b=[x[1],-x[0]],_=l.pointAt(S);b[1]>0&&(b[0]=-b[0],b[1]=-b[1]);var A=x[0]<0?-1:1;if(n.__position!=="start"&&n.__position!=="end"){var D=-Math.atan2(x[1],x[0]);v[0]<h[0]&&(D=Math.PI+D),n.rotation=D}var M=void 0;switch(n.__position){case"insideStartTop":case"insideMiddleTop":case"insideEndTop":case"middle":M=-m,d="bottom";break;case"insideStartBottom":case"insideMiddleBottom":case"insideEndBottom":M=m,d="top";break;default:M=0,d="middle"}switch(n.__position){case"end":n.x=c[0]*y+v[0],n.y=c[1]*m+v[1],p=c[0]>.8?"left":c[0]<-.8?"right":"center",d=c[1]>.8?"top":c[1]<-.8?"bottom":"middle";break;case"start":n.x=-c[0]*y+h[0],n.y=-c[1]*m+h[1],p=c[0]>.8?"right":c[0]<-.8?"left":"center",d=c[1]>.8?"bottom":c[1]<-.8?"top":"middle";break;case"insideStartTop":case"insideStart":case"insideStartBottom":n.x=y*A+h[0],n.y=h[1]+M,p=x[0]<0?"right":"left",n.originX=-y*A,n.originY=-M;break;case"insideMiddleTop":case"insideMiddle":case"insideMiddleBottom":case"middle":n.x=_[0],n.y=_[1]+M,p="center",n.originY=-M;break;case"insideEndTop":case"insideEnd":case"insideEndBottom":n.x=-y*A+v[0],n.y=v[1]+M,p=x[0]>=0?"right":"left",n.originX=y*A,n.originY=-M;break}n.scaleX=n.scaleY=o,n.setStyle({verticalAlign:n.__verticalAlign||d,align:n.__align||p})}},e}($),Ah=function(){function a(e){this.group=new $,this._LineCtor=e||_h}return a.prototype.updateData=function(e){var t=this;this._progressiveEls=null;var r=this,i=r.group,n=r._lineData;r._lineData=e,n||i.removeAll();var o=Wi(e);e.diff(n).add(function(s){t._doAdd(e,s,o)}).update(function(s,l){t._doUpdate(n,e,l,s,o)}).remove(function(s){i.remove(n.getItemGraphicEl(s))}).execute()},a.prototype.updateLayout=function(){var e=this._lineData;e&&e.eachItemGraphicEl(function(t,r){t.updateLayout(e,r)},this)},a.prototype.incrementalPrepareUpdate=function(e){this._seriesScope=Wi(e),this._lineData=null,this.group.removeAll()},a.prototype.incrementalUpdate=function(e,t){this._progressiveEls=[];function r(s){!s.isGroup&&!wh(s)&&(s.incremental=!0,s.ensureState("emphasis").hoverLayer=!0)}for(var i=e.start;i<e.end;i++){var n=t.getItemLayout(i);if(Dr(n)){var o=new this._LineCtor(t,i,this._seriesScope);o.traverse(r),this.group.add(o),t.setItemGraphicEl(i,o),this._progressiveEls.push(o)}}},a.prototype.remove=function(){this.group.removeAll()},a.prototype.eachRendered=function(e){rr(this._progressiveEls||this.group,e)},a.prototype._doAdd=function(e,t,r){var i=e.getItemLayout(t);if(Dr(i)){var n=new this._LineCtor(e,t,r);e.setItemGraphicEl(t,n),this.group.add(n)}},a.prototype._doUpdate=function(e,t,r,i,n){var o=e.getItemGraphicEl(r);if(!Dr(t.getItemLayout(i))){this.group.remove(o);return}o?o.updateData(t,i,n):o=new this._LineCtor(t,i,n),t.setItemGraphicEl(i,o),this.group.add(o)},a}();function wh(a){return a.animators&&a.animators.length>0}function Wi(a){var e=a.hostModel,t=e.getModel("emphasis");return{lineStyle:e.getModel("lineStyle").getLineStyle(),emphasisLineStyle:t.getModel(["lineStyle"]).getLineStyle(),blurLineStyle:e.getModel(["blur","lineStyle"]).getLineStyle(),selectLineStyle:e.getModel(["select","lineStyle"]).getLineStyle(),emphasisDisabled:t.get("disabled"),blurScope:t.get("blurScope"),focus:t.get("focus"),labelStatesModels:Xt(e)}}function Hi(a){return isNaN(a[0])||isNaN(a[1])}function Dr(a){return a&&!Hi(a[0])&&!Hi(a[1])}function oe(a,e,t,r,i,n){a=a||0;var o=t[1]-t[0];if(i!=null&&(i=$t(i,[0,o])),n!=null&&(n=Math.max(n,i??0)),r==="all"){var s=Math.abs(e[1]-e[0]);s=$t(s,[0,o]),i=n=$t(s,[i,n]),r=0}e[0]=$t(e[0],t),e[1]=$t(e[1],t);var l=Tr(e,r);e[r]+=a;var u=i||0,h=t.slice();l.sign<0?h[0]+=u:h[1]-=u,e[r]=$t(e[r],h);var v;return v=Tr(e,r),i!=null&&(v.sign!==l.sign||v.span<i)&&(e[1-r]=e[r]+l.sign*i),v=Tr(e,r),n!=null&&v.span>n&&(e[1-r]=e[r]+v.sign*n),e}function Tr(a,e){var t=a[e]-a[1-e];return{span:Math.abs(t),sign:t>0?-1:t<0?1:e?-1:1}}function $t(a,e){return Math.min(e[1]!=null?e[1]:1/0,Math.max(e[0]!=null?e[0]:-1/0,a))}var Ht=!0,be=Math.min,ee=Math.max,Mh=Math.pow,Ch=1e4,Dh=6,Th=6,Zi="globalPan",Lh={w:[0,0],e:[0,1],n:[1,0],s:[1,1]},Ih={w:"ew",e:"ew",n:"ns",s:"ns",ne:"nesw",sw:"nesw",nw:"nwse",se:"nwse"},Ui={brushStyle:{lineWidth:2,stroke:"rgba(210,219,238,0.3)",fill:"#D2DBEE"},transformable:!0,brushMode:"single",removeOnClick:!1},kh=0,Ph=function(a){I(e,a);function e(t){var r=a.call(this)||this;return r._track=[],r._covers=[],r._handlers={},r._zr=t,r.group=new $,r._uid="brushController_"+kh++,w(Nh,function(i,n){this._handlers[n]=R(i,this)},r),r}return e.prototype.enableBrush=function(t){return this._brushType&&this._doDisableBrush(),t.brushType&&this._doEnableBrush(t),this},e.prototype._doEnableBrush=function(t){var r=this._zr;this._enableGlobalPan||vh(r,Zi,this._uid),w(this._handlers,function(i,n){r.on(n,i)}),this._brushType=t.brushType,this._brushOption=Z(U(Ui),t,!0)},e.prototype._doDisableBrush=function(){var t=this._zr;ch(t,Zi,this._uid),w(this._handlers,function(r,i){t.off(i,r)}),this._brushType=this._brushOption=null},e.prototype.setPanels=function(t){if(t&&t.length){var r=this._panels={};w(t,function(i){r[i.panelId]=U(i)})}else this._panels=null;return this},e.prototype.mount=function(t){t=t||{},this._enableGlobalPan=t.enableGlobalPan;var r=this.group;return this._zr.add(r),r.attr({x:t.x||0,y:t.y||0,rotation:t.rotation||0,scaleX:t.scaleX||1,scaleY:t.scaleY||1}),this._transform=r.getLocalTransform(),this},e.prototype.updateCovers=function(t){t=E(t,function(c){return Z(U(Ui),c,!0)});var r="\0-brush-index-",i=this._covers,n=this._covers=[],o=this,s=this._creatingCover;return new ho(i,t,u,l).add(h).update(h).remove(v).execute(),this;function l(c,f){return(c.id!=null?c.id:r+f)+"-"+c.brushType}function u(c,f){return l(c.__brushOption,f)}function h(c,f){var p=t[c];if(f!=null&&i[f]===s)n[c]=i[f];else{var d=n[c]=f!=null?(i[f].__brushOption=p,i[f]):qo(o,jo(o,p));Ia(o,d)}}function v(c){i[c]!==s&&o.group.remove(i[c])}},e.prototype.unmount=function(){return this.enableBrush(!1),ia(this),this._zr.remove(this.group),this},e.prototype.dispose=function(){this.unmount(),this.off()},e}(lo);function jo(a,e){var t=lr[e.brushType].createCover(a,e);return t.__brushOption=e,Jo(t,e),a.group.add(t),t}function qo(a,e){var t=ka(e);return t.endCreating&&(t.endCreating(a,e),Jo(e,e.__brushOption)),e}function Qo(a,e){var t=e.__brushOption;ka(e).updateCoverShape(a,e,t.range,t)}function Jo(a,e){var t=e.z;t==null&&(t=Ch),a.traverse(function(r){r.z=t,r.z2=t})}function Ia(a,e){ka(e).updateCommon(a,e),Qo(a,e)}function ka(a){return lr[a.__brushOption.brushType]}function Pa(a,e,t){var r=a._panels;if(!r)return Ht;var i,n=a._transform;return w(r,function(o){o.isTargetByCursor(e,t,n)&&(i=o)}),i}function ts(a,e){var t=a._panels;if(!t)return Ht;var r=e.__brushOption.panelId;return r!=null?t[r]:Ht}function ia(a){var e=a._covers,t=e.length;return w(e,function(r){a.group.remove(r)},a),e.length=0,!!t}function Zt(a,e){var t=E(a._covers,function(r){var i=r.__brushOption,n=U(i.range);return{brushType:i.brushType,panelId:i.panelId,range:n}});a.trigger("brush",{areas:t,isEnd:!!e.isEnd,removeOnClick:!!e.removeOnClick})}function Rh(a){var e=a._track;if(!e.length)return!1;var t=e[e.length-1],r=e[0],i=t[0]-r[0],n=t[1]-r[1],o=Mh(i*i+n*n,.5);return o>Dh}function es(a){var e=a.length-1;return e<0&&(e=0),[a[0],a[e]]}function rs(a,e,t,r){var i=new $;return i.add(new nt({name:"main",style:Ra(t),silent:!0,draggable:!0,cursor:"move",drift:G(Xi,a,e,i,["n","s","w","e"]),ondragend:G(Zt,e,{isEnd:!0})})),w(r,function(n){i.add(new nt({name:n.join(""),style:{opacity:0},draggable:!0,silent:!0,invisible:!0,drift:G(Xi,a,e,i,n),ondragend:G(Zt,e,{isEnd:!0})}))}),i}function as(a,e,t,r){var i=r.brushStyle.lineWidth||0,n=ee(i,Th),o=t[0][0],s=t[1][0],l=o-i/2,u=s-i/2,h=t[0][1],v=t[1][1],c=h-n+i/2,f=v-n+i/2,p=h-o,d=v-s,g=p+i,y=d+i;At(a,e,"main",o,s,p,d),r.transformable&&(At(a,e,"w",l,u,n,y),At(a,e,"e",c,u,n,y),At(a,e,"n",l,u,g,n),At(a,e,"s",l,f,g,n),At(a,e,"nw",l,u,n,n),At(a,e,"ne",c,u,n,n),At(a,e,"sw",l,f,n,n),At(a,e,"se",c,f,n,n))}function na(a,e){var t=e.__brushOption,r=t.transformable,i=e.childAt(0);i.useStyle(Ra(t)),i.attr({silent:!r,cursor:r?"move":"default"}),w([["w"],["e"],["n"],["s"],["s","e"],["s","w"],["n","e"],["n","w"]],function(n){var o=e.childOfName(n.join("")),s=n.length===1?oa(a,n[0]):Oh(a,n);o&&o.attr({silent:!r,invisible:!r,cursor:r?Ih[s]+"-resize":null})})}function At(a,e,t,r,i,n,o){var s=e.childOfName(t);s&&s.setShape(zh(Ea(a,e,[[r,i],[r+n,i+o]])))}function Ra(a){return ht({strokeNoScale:!0},a.brushStyle)}function is(a,e,t,r){var i=[be(a,t),be(e,r)],n=[ee(a,t),ee(e,r)];return[[i[0],n[0]],[i[1],n[1]]]}function Eh(a){return Jt(a.group)}function oa(a,e){var t={w:"left",e:"right",n:"top",s:"bottom"},r={left:"w",right:"e",top:"n",bottom:"s"},i=_a(t[e],Eh(a));return r[i]}function Oh(a,e){var t=[oa(a,e[0]),oa(a,e[1])];return(t[0]==="e"||t[0]==="w")&&t.reverse(),t.join("")}function Xi(a,e,t,r,i,n){var o=t.__brushOption,s=a.toRectRange(o.range),l=ns(e,i,n);w(r,function(u){var h=Lh[u];s[h[0]][h[1]]+=l[h[0]]}),o.range=a.fromRectRange(is(s[0][0],s[1][0],s[0][1],s[1][1])),Ia(e,t),Zt(e,{isEnd:!1})}function Vh(a,e,t,r){var i=e.__brushOption.range,n=ns(a,t,r);w(i,function(o){o[0]+=n[0],o[1]+=n[1]}),Ia(a,e),Zt(a,{isEnd:!1})}function ns(a,e,t){var r=a.group,i=r.transformCoordToLocal(e,t),n=r.transformCoordToLocal(0,0);return[i[0]-n[0],i[1]-n[1]]}function Ea(a,e,t){var r=ts(a,e);return r&&r!==Ht?r.clipPath(t,a._transform):U(t)}function zh(a){var e=be(a[0][0],a[1][0]),t=be(a[0][1],a[1][1]),r=ee(a[0][0],a[1][0]),i=ee(a[0][1],a[1][1]);return{x:e,y:t,width:r-e,height:i-t}}function Bh(a,e,t){if(!(!a._brushType||Gh(a,e.offsetX,e.offsetY))){var r=a._zr,i=a._covers,n=Pa(a,e,t);if(!a._dragging)for(var o=0;o<i.length;o++){var s=i[o].__brushOption;if(n&&(n===Ht||s.panelId===n.panelId)&&lr[s.brushType].contain(i[o],t[0],t[1]))return}n&&r.setCursorStyle("crosshair")}}function sa(a){var e=a.event;e.preventDefault&&e.preventDefault()}function la(a,e,t){return a.childOfName("main").contain(e,t)}function os(a,e,t,r){var i=a._creatingCover,n=a._creatingPanel,o=a._brushOption,s;if(a._track.push(t.slice()),Rh(a)||i){if(n&&!i){o.brushMode==="single"&&ia(a);var l=U(o);l.brushType=Yi(l.brushType,n),l.panelId=n===Ht?null:n.panelId,i=a._creatingCover=jo(a,l),a._covers.push(i)}if(i){var u=lr[Yi(a._brushType,n)],h=i.__brushOption;h.range=u.getCreatingRange(Ea(a,i,a._track)),r&&(qo(a,i),u.updateCommon(a,i)),Qo(a,i),s={isEnd:r}}}else r&&o.brushMode==="single"&&o.removeOnClick&&Pa(a,e,t)&&ia(a)&&(s={isEnd:r,removeOnClick:!0});return s}function Yi(a,e){return a==="auto"?e.defaultBrushType:a}var Nh={mousedown:function(a){if(this._dragging)$i(this,a);else if(!a.target||!a.target.draggable){sa(a);var e=this.group.transformCoordToLocal(a.offsetX,a.offsetY);this._creatingCover=null;var t=this._creatingPanel=Pa(this,a,e);t&&(this._dragging=!0,this._track=[e.slice()])}},mousemove:function(a){var e=a.offsetX,t=a.offsetY,r=this.group.transformCoordToLocal(e,t);if(Bh(this,a,r),this._dragging){sa(a);var i=os(this,a,r,!1);i&&Zt(this,i)}},mouseup:function(a){$i(this,a)}};function $i(a,e){if(a._dragging){sa(e);var t=e.offsetX,r=e.offsetY,i=a.group.transformCoordToLocal(t,r),n=os(a,e,i,!0);a._dragging=!1,a._track=[],a._creatingCover=null,n&&Zt(a,n)}}function Gh(a,e,t){var r=a._zr;return e<0||e>r.getWidth()||t<0||t>r.getHeight()}var lr={lineX:Ki(0),lineY:Ki(1),rect:{createCover:function(a,e){function t(r){return r}return rs({toRectRange:t,fromRectRange:t},a,e,[["w"],["e"],["n"],["s"],["s","e"],["s","w"],["n","e"],["n","w"]])},getCreatingRange:function(a){var e=es(a);return is(e[1][0],e[1][1],e[0][0],e[0][1])},updateCoverShape:function(a,e,t,r){as(a,e,t,r)},updateCommon:na,contain:la},polygon:{createCover:function(a,e){var t=new $;return t.add(new ba({name:"main",style:Ra(e),silent:!0})),t},getCreatingRange:function(a){return a},endCreating:function(a,e){e.remove(e.childAt(0)),e.add(new ye({name:"main",draggable:!0,drift:G(Vh,a,e),ondragend:G(Zt,a,{isEnd:!0})}))},updateCoverShape:function(a,e,t,r){e.childAt(0).setShape({points:Ea(a,e,t)})},updateCommon:na,contain:la}};function Ki(a){return{createCover:function(e,t){return rs({toRectRange:function(r){var i=[r,[0,100]];return a&&i.reverse(),i},fromRectRange:function(r){return r[a]}},e,t,[[["w"],["e"]],[["n"],["s"]]][a])},getCreatingRange:function(e){var t=es(e),r=be(t[0][a],t[1][a]),i=ee(t[0][a],t[1][a]);return[r,i]},updateCoverShape:function(e,t,r,i){var n,o=ts(e,t);if(o!==Ht&&o.getLinearBrushOtherExtent)n=o.getLinearBrushOtherExtent(a);else{var s=e._zr;n=[0,[s.getWidth(),s.getHeight()][1-a]]}var l=[r,n];a&&l.reverse(),as(e,t,l,i)},updateCommon:na,contain:la}}function Fh(a){return a=Oa(a),function(e){return rl(e,a)}}function Wh(a,e){return a=Oa(a),function(t){var r=e??t,i=r?a.width:a.height,n=r?a.x:a.y;return[n,n+(i||0)]}}function Hh(a,e,t){var r=Oa(a);return function(i,n){return r.contain(n[0],n[1])&&!dh(i,e,t)}}function Oa(a){return de.create(a)}var Zh=function(){function a(){}return a.prototype._hasEncodeRule=function(e){var t=this.getEncode();return t&&t.get(e)!=null},a.prototype.getInitialData=function(e,t){var r,i=t.getComponent("xAxis",this.get("xAxisIndex")),n=t.getComponent("yAxis",this.get("yAxisIndex")),o=i.get("type"),s=n.get("type"),l;o==="category"?(e.layout="horizontal",r=i.getOrdinalMeta(),l=!this._hasEncodeRule("x")):s==="category"?(e.layout="vertical",r=n.getOrdinalMeta(),l=!this._hasEncodeRule("y")):e.layout=e.layout||"horizontal";var u=["x","y"],h=e.layout==="horizontal"?0:1,v=this._baseAxisDim=u[h],c=u[1-h],f=[i,n],p=f[h].get("type"),d=f[1-h].get("type"),g=e.data;if(g&&l){var y=[];w(g,function(x,b){var _;O(x)?(_=x.slice(),x.unshift(b)):O(x.value)?(_=F({},x),_.value=_.value.slice(),x.value.unshift(b)):_=x,y.push(_)}),e.data=y}var m=this.defaultValueDimensions,S=[{name:v,type:Ur(p),ordinalMeta:r,otherDims:{tooltip:!1,itemName:0},dimsDef:["base"]},{name:c,type:Ur(d),dimsDef:m.slice()}];return al(this,{coordDimensions:S,dimensionsCount:m.length+1,encodeDefaulter:G(Jn,S,this)})},a.prototype.getBaseAxis=function(){var e=this._baseAxisDim;return this.ecModel.getComponent(e+"Axis",this.get(e+"AxisIndex")).axis},a}(),Uh=["itemStyle","borderColor"],Xh=["itemStyle","borderColor0"],Yh=["itemStyle","borderColorDoji"],$h=["itemStyle","color"],Kh=["itemStyle","color0"];function Va(a,e){return e.get(a>0?$h:Kh)}function za(a,e){return e.get(a===0?Yh:a>0?Uh:Xh)}var jh={seriesType:"candlestick",plan:ma(),performRawSeries:!0,reset:function(a,e){if(!e.isSeriesFiltered(a)){var t=a.pipelineContext.large;return!t&&{progress:function(r,i){for(var n;(n=r.next())!=null;){var o=i.getItemModel(n),s=i.getItemLayout(n).sign,l=o.getItemStyle();l.fill=Va(s,o),l.stroke=za(s,o)||l.fill;var u=i.ensureUniqueItemVisual(n,"style");F(u,l)}}}}}},qh=["color","borderColor"],Qh=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){this.group.removeClipPath(),this._progressiveEls=null,this._updateDrawMode(t),this._isLargeDraw?this._renderLarge(t):this._renderNormal(t)},e.prototype.incrementalPrepareRender=function(t,r,i){this._clear(),this._updateDrawMode(t)},e.prototype.incrementalRender=function(t,r,i,n){this._progressiveEls=[],this._isLargeDraw?this._incrementalRenderLarge(t,r):this._incrementalRenderNormal(t,r)},e.prototype.eachRendered=function(t){rr(this._progressiveEls||this.group,t)},e.prototype._updateDrawMode=function(t){var r=t.pipelineContext.large;(this._isLargeDraw==null||r!==this._isLargeDraw)&&(this._isLargeDraw=r,this._clear())},e.prototype._renderNormal=function(t){var r=t.getData(),i=this._data,n=this.group,o=r.getLayout("isSimpleBox"),s=t.get("clip",!0),l=t.coordinateSystem,u=l.getArea&&l.getArea();this._data||n.removeAll(),r.diff(i).add(function(h){if(r.hasValue(h)){var v=r.getItemLayout(h);if(s&&ji(u,v))return;var c=Lr(v,h,!0);_t(c,{shape:{points:v.ends}},t,h),Ir(c,r,h,o),n.add(c),r.setItemGraphicEl(h,c)}}).update(function(h,v){var c=i.getItemGraphicEl(v);if(!r.hasValue(h)){n.remove(c);return}var f=r.getItemLayout(h);if(s&&ji(u,f)){n.remove(c);return}c?(ut(c,{shape:{points:f.ends}},t,h),da(c)):c=Lr(f),Ir(c,r,h,o),n.add(c),r.setItemGraphicEl(h,c)}).remove(function(h){var v=i.getItemGraphicEl(h);v&&n.remove(v)}).execute(),this._data=r},e.prototype._renderLarge=function(t){this._clear(),qi(t,this.group);var r=t.get("clip",!0)?zo(t.coordinateSystem,!1,t):null;r?this.group.setClipPath(r):this.group.removeClipPath()},e.prototype._incrementalRenderNormal=function(t,r){for(var i=r.getData(),n=i.getLayout("isSimpleBox"),o;(o=t.next())!=null;){var s=i.getItemLayout(o),l=Lr(s);Ir(l,i,o,n),l.incremental=!0,this.group.add(l),this._progressiveEls.push(l)}},e.prototype._incrementalRenderLarge=function(t,r){qi(r,this.group,this._progressiveEls,!0)},e.prototype.remove=function(t){this._clear()},e.prototype._clear=function(){this.group.removeAll(),this._data=null},e.type="candlestick",e}(Qt),Jh=function(){function a(){}return a}(),tv=function(a){I(e,a);function e(t){var r=a.call(this,t)||this;return r.type="normalCandlestickBox",r}return e.prototype.getDefaultShape=function(){return new Jh},e.prototype.buildPath=function(t,r){var i=r.points;this.__simpleBox?(t.moveTo(i[4][0],i[4][1]),t.lineTo(i[6][0],i[6][1])):(t.moveTo(i[0][0],i[0][1]),t.lineTo(i[1][0],i[1][1]),t.lineTo(i[2][0],i[2][1]),t.lineTo(i[3][0],i[3][1]),t.closePath(),t.moveTo(i[4][0],i[4][1]),t.lineTo(i[5][0],i[5][1]),t.moveTo(i[6][0],i[6][1]),t.lineTo(i[7][0],i[7][1]))},e}(wt);function Lr(a,e,t){var r=a.ends;return new tv({shape:{points:t?ev(r,a):r},z2:100})}function ji(a,e){for(var t=!0,r=0;r<e.ends.length;r++)if(a.contain(e.ends[r][0],e.ends[r][1])){t=!1;break}return t}function Ir(a,e,t,r){var i=e.getItemModel(t);a.useStyle(e.getItemVisual(t,"style")),a.style.strokeNoScale=!0,a.__simpleBox=r,pe(a,i);var n=e.getItemLayout(t).sign;w(a.states,function(s,l){var u=i.getModel(l),h=Va(n,u),v=za(n,u)||h,c=s.style||(s.style={});h&&(c.fill=h),v&&(c.stroke=v)});var o=i.getModel("emphasis");Ft(a,o.get("focus"),o.get("blurScope"),o.get("disabled"))}function ev(a,e){return E(a,function(t){return t=t.slice(),t[1]=e.initBaseline,t})}var rv=function(){function a(){}return a}(),kr=function(a){I(e,a);function e(t){var r=a.call(this,t)||this;return r.type="largeCandlestickBox",r}return e.prototype.getDefaultShape=function(){return new rv},e.prototype.buildPath=function(t,r){for(var i=r.points,n=0;n<i.length;)if(this.__sign===i[n++]){var o=i[n++];t.moveTo(o,i[n++]),t.lineTo(o,i[n++])}else n+=3},e}(wt);function qi(a,e,t,r){var i=a.getData(),n=i.getLayout("largePoints"),o=new kr({shape:{points:n},__sign:1,ignoreCoarsePointer:!0});e.add(o);var s=new kr({shape:{points:n},__sign:-1,ignoreCoarsePointer:!0});e.add(s);var l=new kr({shape:{points:n},__sign:0,ignoreCoarsePointer:!0});e.add(l),Pr(1,o,a),Pr(-1,s,a),Pr(0,l,a),r&&(o.incremental=!0,s.incremental=!0),t&&t.push(o,s)}function Pr(a,e,t,r){var i=za(a,t)||Va(a,t),n=t.getModel("itemStyle").getItemStyle(qh);e.useStyle(n),e.style.fill=null,e.style.stroke=i}var ss=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.defaultValueDimensions=[{name:"open",defaultTooltip:!0},{name:"close",defaultTooltip:!0},{name:"lowest",defaultTooltip:!0},{name:"highest",defaultTooltip:!0}],t}return e.prototype.getShadowDim=function(){return"open"},e.prototype.brushSelector=function(t,r,i){var n=r.getItemLayout(t);return n&&i.rect(n.brushRect)},e.type="series.candlestick",e.dependencies=["xAxis","yAxis","grid"],e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,layout:null,clip:!0,itemStyle:{color:"#eb5454",color0:"#47b262",borderColor:"#eb5454",borderColor0:"#47b262",borderColorDoji:null,borderWidth:1},emphasis:{itemStyle:{borderWidth:2}},barMaxWidth:null,barMinWidth:null,barWidth:null,large:!0,largeThreshold:600,progressive:3e3,progressiveThreshold:1e4,progressiveChunkMode:"mod",animationEasing:"linear",animationDuration:300},e}(we);ir(ss,Zh,!0);function av(a){!a||!O(a.series)||w(a.series,function(e){st(e)&&e.type==="k"&&(e.type="candlestick")})}var iv={seriesType:"candlestick",plan:ma(),reset:function(a){var e=a.coordinateSystem,t=a.getData(),r=nv(a,t),i=0,n=1,o=["x","y"],s=t.getDimensionIndex(t.mapDimension(o[i])),l=E(t.mapDimensionsAll(o[n]),t.getDimensionIndex,t),u=l[0],h=l[1],v=l[2],c=l[3];if(t.setLayout({candleWidth:r,isSimpleBox:r<=1.3}),s<0||l.length<4)return;return{progress:a.pipelineContext.large?p:f};function f(d,g){for(var y,m=g.getStore();(y=d.next())!=null;){var S=m.get(s,y),x=m.get(u,y),b=m.get(h,y),_=m.get(v,y),A=m.get(c,y),D=Math.min(x,b),M=Math.max(x,b),T=j(D,S),C=j(M,S),L=j(_,S),P=j(A,S),B=[];z(B,C,0),z(B,T,1),B.push(Q(P),Q(C),Q(L),Q(T));var V=g.getItemModel(y),q=!!V.get(["itemStyle","borderColorDoji"]);g.setItemLayout(y,{sign:Qi(m,y,x,b,h,q),initBaseline:x>b?C[n]:T[n],ends:B,brushRect:W(_,A,S)})}function j(N,lt){var K=[];return K[i]=lt,K[n]=N,isNaN(lt)||isNaN(N)?[NaN,NaN]:e.dataToPoint(K)}function z(N,lt,K){var X=lt.slice(),Y=lt.slice();X[i]=dr(X[i]+r/2,1,!1),Y[i]=dr(Y[i]-r/2,1,!0),K?N.push(X,Y):N.push(Y,X)}function W(N,lt,K){var X=j(N,K),Y=j(lt,K);return X[i]-=r/2,Y[i]-=r/2,{x:X[0],y:X[1],width:r,height:Y[1]-X[1]}}function Q(N){return N[i]=dr(N[i],1),N}}function p(d,g){for(var y=zt(d.count*4),m=0,S,x=[],b=[],_,A=g.getStore(),D=!!a.get(["itemStyle","borderColorDoji"]);(_=d.next())!=null;){var M=A.get(s,_),T=A.get(u,_),C=A.get(h,_),L=A.get(v,_),P=A.get(c,_);if(isNaN(M)||isNaN(L)||isNaN(P)){y[m++]=NaN,m+=3;continue}y[m++]=Qi(A,_,T,C,h,D),x[i]=M,x[n]=L,S=e.dataToPoint(x,null,b),y[m++]=S?S[0]:NaN,y[m++]=S?S[1]:NaN,x[n]=P,S=e.dataToPoint(x,null,b),y[m++]=S?S[1]:NaN}g.setLayout("largePoints",y)}}};function Qi(a,e,t,r,i,n){var o;return t>r?o=-1:t<r?o=1:o=n?0:e>0?a.get(i,e-1)<=r?1:-1:1,o}function nv(a,e){var t=a.getBaseAxis(),r,i=t.type==="category"?t.getBandWidth():(r=t.getExtent(),Math.abs(r[1]-r[0])/e.count()),n=ct(Lt(a.get("barMaxWidth"),i),i),o=ct(Lt(a.get("barMinWidth"),1),i),s=a.get("barWidth");return s!=null?ct(s,i):Math.max(Math.min(i/2,n),o)}function Rf(a){a.registerChartView(Qh),a.registerSeriesModel(ss),a.registerPreprocessor(av),a.registerVisual(jh),a.registerLayout(iv)}function ov(a,e,t,r){return a&&(a.legacy||a.legacy!==!1&&!t&&!r&&e!=="tspan"&&(e==="text"||k(a,"text")))}function sv(a,e,t){var r=a,i,n,o;if(e==="text")o=r;else{o={},k(r,"text")&&(o.text=r.text),k(r,"rich")&&(o.rich=r.rich),k(r,"textFill")&&(o.fill=r.textFill),k(r,"textStroke")&&(o.stroke=r.textStroke),k(r,"fontFamily")&&(o.fontFamily=r.fontFamily),k(r,"fontSize")&&(o.fontSize=r.fontSize),k(r,"fontStyle")&&(o.fontStyle=r.fontStyle),k(r,"fontWeight")&&(o.fontWeight=r.fontWeight),n={type:"text",style:o,silent:!0},i={};var s=k(r,"textPosition");t?i.position=s?r.textPosition:"inside":s&&(i.position=r.textPosition),k(r,"textPosition")&&(i.position=r.textPosition),k(r,"textOffset")&&(i.offset=r.textOffset),k(r,"textRotation")&&(i.rotation=r.textRotation),k(r,"textDistance")&&(i.distance=r.textDistance)}return Ji(o,a),w(o.rich,function(l){Ji(l,l)}),{textConfig:i,textContent:n}}function Ji(a,e){e&&(e.font=e.textFont||e.font,k(e,"textStrokeWidth")&&(a.lineWidth=e.textStrokeWidth),k(e,"textAlign")&&(a.align=e.textAlign),k(e,"textVerticalAlign")&&(a.verticalAlign=e.textVerticalAlign),k(e,"textLineHeight")&&(a.lineHeight=e.textLineHeight),k(e,"textWidth")&&(a.width=e.textWidth),k(e,"textHeight")&&(a.height=e.textHeight),k(e,"textBackgroundColor")&&(a.backgroundColor=e.textBackgroundColor),k(e,"textPadding")&&(a.padding=e.textPadding),k(e,"textBorderColor")&&(a.borderColor=e.textBorderColor),k(e,"textBorderWidth")&&(a.borderWidth=e.textBorderWidth),k(e,"textBorderRadius")&&(a.borderRadius=e.textBorderRadius),k(e,"textBoxShadowColor")&&(a.shadowColor=e.textBoxShadowColor),k(e,"textBoxShadowBlur")&&(a.shadowBlur=e.textBoxShadowBlur),k(e,"textBoxShadowOffsetX")&&(a.shadowOffsetX=e.textBoxShadowOffsetX),k(e,"textBoxShadowOffsetY")&&(a.shadowOffsetY=e.textBoxShadowOffsetY))}function Ef(a,e,t){var r=a;r.textPosition=r.textPosition||t.position||"inside",t.offset!=null&&(r.textOffset=t.offset),t.rotation!=null&&(r.textRotation=t.rotation),t.distance!=null&&(r.textDistance=t.distance);var i=r.textPosition.indexOf("inside")>=0,n=a.fill||"#000";tn(r,e);var o=r.textFill==null;return i?o&&(r.textFill=t.insideFill||"#fff",!r.textStroke&&t.insideStroke&&(r.textStroke=t.insideStroke),!r.textStroke&&(r.textStroke=n),r.textStrokeWidth==null&&(r.textStrokeWidth=2)):(o&&(r.textFill=a.fill||t.outsideFill||"#000"),!r.textStroke&&t.outsideStroke&&(r.textStroke=t.outsideStroke)),r.text=e.text,r.rich=e.rich,w(e.rich,function(s){tn(s,s)}),r}function tn(a,e){e&&(k(e,"fill")&&(a.textFill=e.fill),k(e,"stroke")&&(a.textStroke=e.fill),k(e,"lineWidth")&&(a.textStrokeWidth=e.lineWidth),k(e,"font")&&(a.font=e.font),k(e,"fontStyle")&&(a.fontStyle=e.fontStyle),k(e,"fontWeight")&&(a.fontWeight=e.fontWeight),k(e,"fontSize")&&(a.fontSize=e.fontSize),k(e,"fontFamily")&&(a.fontFamily=e.fontFamily),k(e,"align")&&(a.textAlign=e.align),k(e,"verticalAlign")&&(a.textVerticalAlign=e.verticalAlign),k(e,"lineHeight")&&(a.textLineHeight=e.lineHeight),k(e,"width")&&(a.textWidth=e.width),k(e,"height")&&(a.textHeight=e.height),k(e,"backgroundColor")&&(a.textBackgroundColor=e.backgroundColor),k(e,"padding")&&(a.textPadding=e.padding),k(e,"borderColor")&&(a.textBorderColor=e.borderColor),k(e,"borderWidth")&&(a.textBorderWidth=e.borderWidth),k(e,"borderRadius")&&(a.textBorderRadius=e.borderRadius),k(e,"shadowColor")&&(a.textBoxShadowColor=e.shadowColor),k(e,"shadowBlur")&&(a.textBoxShadowBlur=e.shadowBlur),k(e,"shadowOffsetX")&&(a.textBoxShadowOffsetX=e.shadowOffsetX),k(e,"shadowOffsetY")&&(a.textBoxShadowOffsetY=e.shadowOffsetY),k(e,"textShadowColor")&&(a.textShadowColor=e.textShadowColor),k(e,"textShadowBlur")&&(a.textShadowBlur=e.textShadowBlur),k(e,"textShadowOffsetX")&&(a.textShadowOffsetX=e.textShadowOffsetX),k(e,"textShadowOffsetY")&&(a.textShadowOffsetY=e.textShadowOffsetY))}var ls={position:["x","y"],scale:["scaleX","scaleY"],origin:["originX","originY"]},en=Rt(ls);ol(me,function(a,e){return a[e]=1,a},{});me.join(", ");var Ye=["","style","shape","extra"],re=ft();function Ba(a,e,t,r,i){var n=a+"Animation",o=co(a,r,i)||{},s=re(e).userDuring;return o.duration>0&&(o.during=s?R(fv,{el:e,userDuring:s}):null,o.setToFinal=!0,o.scope=a),F(o,t[n]),o}function Rr(a,e,t,r){r=r||{};var i=r.dataIndex,n=r.isInit,o=r.clearStyle,s=t.isAnimationEnabled(),l=re(a),u=e.style;l.userDuring=e.during;var h={},v={};if(dv(a,e,v),an("shape",e,v),an("extra",e,v),!n&&s&&(pv(a,e,h),rn("shape",a,e,h),rn("extra",a,e,h),gv(a,e,u,h)),v.style=u,uv(a,v,o),vv(a,e),s)if(n){var c={};w(Ye,function(p){var d=p?e[p]:e;d&&d.enterFrom&&(p&&(c[p]=c[p]||{}),F(p?c[p]:c,d.enterFrom))});var f=Ba("enter",a,e,t,i);f.duration>0&&a.animateFrom(c,f)}else hv(a,e,i||0,t,h);us(a,e),u?a.dirty():a.markRedraw()}function us(a,e){for(var t=re(a).leaveToProps,r=0;r<Ye.length;r++){var i=Ye[r],n=i?e[i]:e;n&&n.leaveTo&&(t||(t=re(a).leaveToProps={}),i&&(t[i]=t[i]||{}),F(i?t[i]:t,n.leaveTo))}}function lv(a,e,t,r){if(a){var i=a.parent,n=re(a).leaveToProps;if(n){var o=Ba("update",a,e,t,0);o.done=function(){i.remove(a)},a.animateTo(n,o)}else i.remove(a)}}function Gt(a){return a==="all"}function uv(a,e,t){var r=e.style;if(!a.isGroup&&r){if(t){a.useStyle({});for(var i=a.animators,n=0;n<i.length;n++){var o=i[n];o.targetName==="style"&&o.changeTarget(a.style)}}a.setStyle(r)}e&&(e.style=null,e&&a.attr(e),e.style=r)}function hv(a,e,t,r,i){if(i){var n=Ba("update",a,e,r,t);n.duration>0&&a.animateFrom(i,n)}}function vv(a,e){k(e,"silent")&&(a.silent=e.silent),k(e,"ignore")&&(a.ignore=e.ignore),a instanceof vo&&k(e,"invisible")&&(a.invisible=e.invisible),a instanceof wt&&k(e,"autoBatch")&&(a.autoBatch=e.autoBatch)}var mt={},cv={setTransform:function(a,e){return mt.el[a]=e,this},getTransform:function(a){return mt.el[a]},setShape:function(a,e){var t=mt.el,r=t.shape||(t.shape={});return r[a]=e,t.dirtyShape&&t.dirtyShape(),this},getShape:function(a){var e=mt.el.shape;if(e)return e[a]},setStyle:function(a,e){var t=mt.el,r=t.style;return r&&(r[a]=e,t.dirtyStyle&&t.dirtyStyle()),this},getStyle:function(a){var e=mt.el.style;if(e)return e[a]},setExtra:function(a,e){var t=mt.el.extra||(mt.el.extra={});return t[a]=e,this},getExtra:function(a){var e=mt.el.extra;if(e)return e[a]}};function fv(){var a=this,e=a.el;if(e){var t=re(e).userDuring,r=a.userDuring;if(t!==r){a.el=a.userDuring=null;return}mt.el=e,r(cv)}}function rn(a,e,t,r){var i=t[a];if(i){var n=e[a],o;if(n){var s=t.transition,l=i.transition;if(l)if(!o&&(o=r[a]={}),Gt(l))F(o,n);else for(var u=or(l),h=0;h<u.length;h++){var v=u[h],c=n[v];o[v]=c}else if(Gt(s)||et(s,a)>=0){!o&&(o=r[a]={});for(var f=Rt(n),h=0;h<f.length;h++){var v=f[h],c=n[v];yv(i[v],c)&&(o[v]=c)}}}}}function an(a,e,t){var r=e[a];if(r)for(var i=t[a]={},n=Rt(r),o=0;o<n.length;o++){var s=n[o];i[s]=il(r[s])}}function pv(a,e,t){for(var r=e.transition,i=Gt(r)?me:or(r||[]),n=0;n<i.length;n++){var o=i[n];if(!(o==="style"||o==="shape"||o==="extra")){var s=a[o];t[o]=s}}}function dv(a,e,t){for(var r=0;r<en.length;r++){var i=en[r],n=ls[i],o=e[i];o&&(t[n[0]]=o[0],t[n[1]]=o[1])}for(var r=0;r<me.length;r++){var s=me[r];e[s]!=null&&(t[s]=e[s])}}function gv(a,e,t,r){if(t){var i=a.style,n;if(i){var o=t.transition,s=e.transition;if(o&&!Gt(o)){var l=or(o);!n&&(n=r.style={});for(var u=0;u<l.length;u++){var h=l[u],v=i[h];n[h]=v}}else if(a.getAnimationStyleProps&&(Gt(s)||Gt(o)||et(s,"style")>=0)){var c=a.getAnimationStyleProps(),f=c?c.style:null;if(f){!n&&(n=r.style={});for(var p=Rt(t),u=0;u<p.length;u++){var h=p[u];if(f[h]){var v=i[h];n[h]=v}}}}}}}function yv(a,e){return nl(a)?a!==e:a!=null&&isFinite(a)}var hs=ft(),mv=["percent","easing","shape","style","extra"];function xv(a){a.stopAnimation("keyframe"),a.attr(hs(a))}function ua(a,e,t){if(!(!t.isAnimationEnabled()||!e)){if(O(e)){w(e,function(s){ua(a,s,t)});return}var r=e.keyframes,i=e.duration;if(t&&i==null){var n=co("enter",t,0);i=n&&n.duration}if(!(!r||!i)){var o=hs(a);w(Ye,function(s){if(!(s&&!a[s])){var l;r.sort(function(u,h){return u.percent-h.percent}),w(r,function(u){var h=a.animators,v=s?u[s]:u;if(v){var c=Rt(v);if(s||(c=Pt(c,function(d){return et(mv,d)<0})),!!c.length){l||(l=a.animate(s,e.loop,!0),l.scope="keyframe");for(var f=0;f<h.length;f++)h[f]!==l&&h[f].targetName===l.targetName&&h[f].stopTracks(c);s&&(o[s]=o[s]||{});var p=s?o[s]:o;w(c,function(d){p[d]=((s?a[s]:a)||{})[d]}),l.whenWithKeys(i*u.percent,v,c,u.easing)}}}),l&&l.delay(e.delay||0).duration(i).start(e.easing)}})}}}function Of(a){kt(Xo),kt(sl)}var Sv=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r,i){var n=xa(t);a.prototype.init.apply(this,arguments),nn(t,n)},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),nn(this.option,t)},e.prototype.getCellSize=function(){return this.option.cellSize},e.type="calendar",e.defaultOption={z:2,left:80,top:60,cellSize:20,orient:"horizontal",splitLine:{show:!0,lineStyle:{color:"#000",width:1,type:"solid"}},itemStyle:{color:"#fff",borderWidth:1,borderColor:"#ccc"},dayLabel:{show:!0,firstDay:0,position:"start",margin:"50%",color:"#000"},monthLabel:{show:!0,position:"start",margin:5,align:"center",formatter:null,color:"#000"},yearLabel:{show:!0,position:null,margin:30,formatter:null,color:"#ccc",fontFamily:"sans-serif",fontWeight:"bolder",fontSize:20}},e}(Mt);function nn(a,e){var t=a.cellSize,r;O(t)?r=t:r=a.cellSize=[t,t],r.length===1&&(r[1]=r[0]);var i=E([0,1],function(n){return ll(e,n)&&(r[n]="auto"),r[n]!=null&&r[n]!=="auto"});Sa(a,e,{type:"box",ignoreSize:i})}var bv=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i){var n=this.group;n.removeAll();var o=t.coordinateSystem,s=o.getRangeInfo(),l=o.getOrient(),u=r.getLocaleModel();this._renderDayRect(t,s,n),this._renderLines(t,s,l,n),this._renderYearText(t,s,l,n),this._renderMonthText(t,u,l,n),this._renderWeekText(t,u,s,l,n)},e.prototype._renderDayRect=function(t,r,i){for(var n=t.coordinateSystem,o=t.getModel("itemStyle").getItemStyle(),s=n.getCellWidth(),l=n.getCellHeight(),u=r.start.time;u<=r.end.time;u=n.getNextNDay(u,1).time){var h=n.dataToRect([u],!1).tl,v=new nt({shape:{x:h[0],y:h[1],width:s,height:l},cursor:"default",style:o});i.add(v)}},e.prototype._renderLines=function(t,r,i,n){var o=this,s=t.coordinateSystem,l=t.getModel(["splitLine","lineStyle"]).getLineStyle(),u=t.get(["splitLine","show"]),h=l.lineWidth;this._tlpoints=[],this._blpoints=[],this._firstDayOfMonth=[],this._firstDayPoints=[];for(var v=r.start,c=0;v.time<=r.end.time;c++){p(v.formatedDate),c===0&&(v=s.getDateInfo(r.start.y+"-"+r.start.m));var f=v.date;f.setMonth(f.getMonth()+1),v=s.getDateInfo(f)}p(s.getNextNDay(r.end.time,1).formatedDate);function p(d){o._firstDayOfMonth.push(s.getDateInfo(d)),o._firstDayPoints.push(s.dataToRect([d],!1).tl);var g=o._getLinePointsOfOneWeek(t,d,i);o._tlpoints.push(g[0]),o._blpoints.push(g[g.length-1]),u&&o._drawSplitline(g,l,n)}u&&this._drawSplitline(o._getEdgesPoints(o._tlpoints,h,i),l,n),u&&this._drawSplitline(o._getEdgesPoints(o._blpoints,h,i),l,n)},e.prototype._getEdgesPoints=function(t,r,i){var n=[t[0].slice(),t[t.length-1].slice()],o=i==="horizontal"?0:1;return n[0][o]=n[0][o]-r/2,n[1][o]=n[1][o]+r/2,n},e.prototype._drawSplitline=function(t,r,i){var n=new ba({z2:20,shape:{points:t},style:r});i.add(n)},e.prototype._getLinePointsOfOneWeek=function(t,r,i){for(var n=t.coordinateSystem,o=n.getDateInfo(r),s=[],l=0;l<7;l++){var u=n.getNextNDay(o.time,l),h=n.dataToRect([u.time],!1);s[2*u.day]=h.tl,s[2*u.day+1]=h[i==="horizontal"?"bl":"tr"]}return s},e.prototype._formatterLabel=function(t,r){return it(t)&&t?ul(t,r):rt(t)?t(r):r.nameMap},e.prototype._yearTextPositionControl=function(t,r,i,n,o){var s=r[0],l=r[1],u=["center","bottom"];n==="bottom"?(l+=o,u=["center","top"]):n==="left"?s-=o:n==="right"?(s+=o,u=["center","top"]):l-=o;var h=0;return(n==="left"||n==="right")&&(h=Math.PI/2),{rotation:h,x:s,y:l,style:{align:u[0],verticalAlign:u[1]}}},e.prototype._renderYearText=function(t,r,i,n){var o=t.getModel("yearLabel");if(o.get("show")){var s=o.get("margin"),l=o.get("position");l||(l=i!=="horizontal"?"top":"left");var u=[this._tlpoints[this._tlpoints.length-1],this._blpoints[0]],h=(u[0][0]+u[1][0])/2,v=(u[0][1]+u[1][1])/2,c=i==="horizontal"?0:1,f={top:[h,u[c][1]],bottom:[h,u[1-c][1]],left:[u[1-c][0],v],right:[u[c][0],v]},p=r.start.y;+r.end.y>+r.start.y&&(p=p+"-"+r.end.y);var d=o.get("formatter"),g={start:r.start.y,end:r.end.y,nameMap:p},y=this._formatterLabel(d,g),m=new tt({z2:30,style:gt(o,{text:y}),silent:o.get("silent")});m.attr(this._yearTextPositionControl(m,f[l],i,l,s)),n.add(m)}},e.prototype._monthTextPositionControl=function(t,r,i,n,o){var s="left",l="top",u=t[0],h=t[1];return i==="horizontal"?(h=h+o,r&&(s="center"),n==="start"&&(l="bottom")):(u=u+o,r&&(l="middle"),n==="start"&&(s="right")),{x:u,y:h,align:s,verticalAlign:l}},e.prototype._renderMonthText=function(t,r,i,n){var o=t.getModel("monthLabel");if(o.get("show")){var s=o.get("nameMap"),l=o.get("margin"),u=o.get("position"),h=o.get("align"),v=[this._tlpoints,this._blpoints];(!s||it(s))&&(s&&(r=oi(s)||r),s=r.get(["time","monthAbbr"])||[]);var c=u==="start"?0:1,f=i==="horizontal"?0:1;l=u==="start"?-l:l;for(var p=h==="center",d=o.get("silent"),g=0;g<v[c].length-1;g++){var y=v[c][g].slice(),m=this._firstDayOfMonth[g];if(p){var S=this._firstDayPoints[g];y[f]=(S[f]+v[0][g+1][f])/2}var x=o.get("formatter"),b=s[+m.m-1],_={yyyy:m.y,yy:(m.y+"").slice(2),MM:m.m,M:+m.m,nameMap:b},A=this._formatterLabel(x,_),D=new tt({z2:30,style:F(gt(o,{text:A}),this._monthTextPositionControl(y,p,i,u,l)),silent:d});n.add(D)}}},e.prototype._weekTextPositionControl=function(t,r,i,n,o){var s="center",l="middle",u=t[0],h=t[1],v=i==="start";return r==="horizontal"?(u=u+n+(v?1:-1)*o[0]/2,s=v?"right":"left"):(h=h+n+(v?1:-1)*o[1]/2,l=v?"bottom":"top"),{x:u,y:h,align:s,verticalAlign:l}},e.prototype._renderWeekText=function(t,r,i,n,o){var s=t.getModel("dayLabel");if(s.get("show")){var l=t.coordinateSystem,u=s.get("position"),h=s.get("nameMap"),v=s.get("margin"),c=l.getFirstDayOfWeek();if(!h||it(h)){h&&(r=oi(h)||r);var f=r.get(["time","dayOfWeekShort"]);h=f||E(r.get(["time","dayOfWeekAbbr"]),function(_){return _[0]})}var p=l.getNextNDay(i.end.time,7-i.lweek).time,d=[l.getCellWidth(),l.getCellHeight()];v=ct(v,Math.min(d[1],d[0])),u==="start"&&(p=l.getNextNDay(i.start.time,-(7+i.fweek)).time,v=-v);for(var g=s.get("silent"),y=0;y<7;y++){var m=l.getNextNDay(p,y),S=l.dataToRect([m.time],!1).center,x=y;x=Math.abs((y+c)%7);var b=new tt({z2:30,style:F(gt(s,{text:h[x]}),this._weekTextPositionControl(S,n,u,v,d)),silent:g});o.add(b)}}},e.type="calendar",e}(Et),Er=864e5,_v=function(){function a(e,t,r){this.type="calendar",this.dimensions=a.dimensions,this.getDimensionsInfo=a.getDimensionsInfo,this._model=e}return a.getDimensionsInfo=function(){return[{name:"time",type:"time"},"value"]},a.prototype.getRangeInfo=function(){return this._rangeInfo},a.prototype.getModel=function(){return this._model},a.prototype.getRect=function(){return this._rect},a.prototype.getCellWidth=function(){return this._sw},a.prototype.getCellHeight=function(){return this._sh},a.prototype.getOrient=function(){return this._orient},a.prototype.getFirstDayOfWeek=function(){return this._firstDayOfWeek},a.prototype.getDateInfo=function(e){e=hl(e);var t=e.getFullYear(),r=e.getMonth()+1,i=r<10?"0"+r:""+r,n=e.getDate(),o=n<10?"0"+n:""+n,s=e.getDay();return s=Math.abs((s+7-this.getFirstDayOfWeek())%7),{y:t+"",m:i,d:o,day:s,time:e.getTime(),formatedDate:t+"-"+i+"-"+o,date:e}},a.prototype.getNextNDay=function(e,t){return t=t||0,t===0?this.getDateInfo(e):(e=new Date(this.getDateInfo(e).time),e.setDate(e.getDate()+t),this.getDateInfo(e))},a.prototype.update=function(e,t){this._firstDayOfWeek=+this._model.getModel("dayLabel").get("firstDay"),this._orient=this._model.get("orient"),this._lineWidth=this._model.getModel("itemStyle").getItemStyle().lineWidth||0,this._rangeInfo=this._getRangeInfo(this._initRangeOption());var r=this._rangeInfo.weeks||1,i=["width","height"],n=this._model.getCellSize().slice(),o=this._model.getBoxLayoutParams(),s=this._orient==="horizontal"?[r,7]:[7,r];w([0,1],function(v){h(n,v)&&(o[i[v]]=n[v]*s[v])});var l={width:t.getWidth(),height:t.getHeight()},u=this._rect=Ce(o,l);w([0,1],function(v){h(n,v)||(n[v]=u[i[v]]/s[v])});function h(v,c){return v[c]!=null&&v[c]!=="auto"}this._sw=n[0],this._sh=n[1]},a.prototype.dataToPoint=function(e,t){O(e)&&(e=e[0]),t==null&&(t=!0);var r=this.getDateInfo(e),i=this._rangeInfo,n=r.formatedDate;if(t&&!(r.time>=i.start.time&&r.time<i.end.time+Er))return[NaN,NaN];var o=r.day,s=this._getRangeInfo([i.start.time,n]).nthWeek;return this._orient==="vertical"?[this._rect.x+o*this._sw+this._sw/2,this._rect.y+s*this._sh+this._sh/2]:[this._rect.x+s*this._sw+this._sw/2,this._rect.y+o*this._sh+this._sh/2]},a.prototype.pointToData=function(e){var t=this.pointToDate(e);return t&&t.time},a.prototype.dataToRect=function(e,t){var r=this.dataToPoint(e,t);return{contentShape:{x:r[0]-(this._sw-this._lineWidth)/2,y:r[1]-(this._sh-this._lineWidth)/2,width:this._sw-this._lineWidth,height:this._sh-this._lineWidth},center:r,tl:[r[0]-this._sw/2,r[1]-this._sh/2],tr:[r[0]+this._sw/2,r[1]-this._sh/2],br:[r[0]+this._sw/2,r[1]+this._sh/2],bl:[r[0]-this._sw/2,r[1]+this._sh/2]}},a.prototype.pointToDate=function(e){var t=Math.floor((e[0]-this._rect.x)/this._sw)+1,r=Math.floor((e[1]-this._rect.y)/this._sh)+1,i=this._rangeInfo.range;return this._orient==="vertical"?this._getDateByWeeksAndDay(r,t-1,i):this._getDateByWeeksAndDay(t,r-1,i)},a.prototype.convertToPixel=function(e,t,r){var i=on(t);return i===this?i.dataToPoint(r):null},a.prototype.convertFromPixel=function(e,t,r){var i=on(t);return i===this?i.pointToData(r):null},a.prototype.containPoint=function(e){return console.warn("Not implemented."),!1},a.prototype._initRangeOption=function(){var e=this._model.get("range"),t;if(O(e)&&e.length===1&&(e=e[0]),O(e))t=e;else{var r=e.toString();if(/^\d{4}$/.test(r)&&(t=[r+"-01-01",r+"-12-31"]),/^\d{4}[\/|-]\d{1,2}$/.test(r)){var i=this.getDateInfo(r),n=i.date;n.setMonth(n.getMonth()+1);var o=this.getNextNDay(n,-1);t=[i.formatedDate,o.formatedDate]}/^\d{4}[\/|-]\d{1,2}[\/|-]\d{1,2}$/.test(r)&&(t=[r,r])}if(!t)return e;var s=this._getRangeInfo(t);return s.start.time>s.end.time&&t.reverse(),t},a.prototype._getRangeInfo=function(e){var t=[this.getDateInfo(e[0]),this.getDateInfo(e[1])],r;t[0].time>t[1].time&&(r=!0,t.reverse());var i=Math.floor(t[1].time/Er)-Math.floor(t[0].time/Er)+1,n=new Date(t[0].time),o=n.getDate(),s=t[1].date.getDate();n.setDate(o+i-1);var l=n.getDate();if(l!==s)for(var u=n.getTime()-t[1].time>0?1:-1;(l=n.getDate())!==s&&(n.getTime()-t[1].time)*u>0;)i-=u,n.setDate(l-u);var h=Math.floor((i+t[0].day+6)/7),v=r?-h+1:h-1;return r&&t.reverse(),{range:[t[0].formatedDate,t[1].formatedDate],start:t[0],end:t[1],allDay:i,weeks:h,nthWeek:v,fweek:t[0].day,lweek:t[1].day}},a.prototype._getDateByWeeksAndDay=function(e,t,r){var i=this._getRangeInfo(r);if(e>i.weeks||e===0&&t<i.fweek||e===i.weeks&&t>i.lweek)return null;var n=(e-1)*7-i.fweek+t,o=new Date(i.start.time);return o.setDate(+i.start.d+n),this.getDateInfo(o)},a.create=function(e,t){var r=[];return e.eachComponent("calendar",function(i){var n=new a(i);r.push(n),i.coordinateSystem=n}),e.eachSeries(function(i){i.get("coordinateSystem")==="calendar"&&(i.coordinateSystem=r[i.get("calendarIndex")||0])}),r},a.dimensions=["time","value"],a}();function on(a){var e=a.calendarModel,t=a.seriesModel,r=e?e.coordinateSystem:t?t.coordinateSystem:null;return r}function Vf(a){a.registerComponentModel(Sv),a.registerComponentView(bv),a.registerCoordinateSystem("calendar",_v)}function Av(a,e){var t=a.existing;if(e.id=a.keyInfo.id,!e.type&&t&&(e.type=t.type),e.parentId==null){var r=e.parentOption;r?e.parentId=r.id:t&&(e.parentId=t.parentId)}e.parentOption=null}function sn(a,e){var t;return w(e,function(r){a[r]!=null&&a[r]!=="auto"&&(t=!0)}),t}function wv(a,e,t){var r=F({},t),i=a[e],n=t.$action||"merge";n==="merge"?i?(Z(i,r,!0),Sa(i,r,{ignoreSize:!0}),cl(t,i),Pe(t,i),Pe(t,i,"shape"),Pe(t,i,"style"),Pe(t,i,"extra"),t.clipPath=i.clipPath):a[e]=r:n==="replace"?a[e]=r:n==="remove"&&i&&(a[e]=null)}var vs=["transition","enterFrom","leaveTo"],Mv=vs.concat(["enterAnimation","updateAnimation","leaveAnimation"]);function Pe(a,e,t){if(t&&(!a[t]&&e[t]&&(a[t]={}),a=a[t],e=e[t]),!(!a||!e))for(var r=t?vs:Mv,i=0;i<r.length;i++){var n=r[i];a[n]==null&&e[n]!=null&&(a[n]=e[n])}}function Cv(a,e){if(a&&(a.hv=e.hv=[sn(e,["left","right"]),sn(e,["top","bottom"])],a.type==="group")){var t=a,r=e;t.width==null&&(t.width=r.width=0),t.height==null&&(t.height=r.height=0)}}var Dv=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.preventAutoZ=!0,t}return e.prototype.mergeOption=function(t,r){var i=this.option.elements;this.option.elements=null,a.prototype.mergeOption.call(this,t,r),this.option.elements=i},e.prototype.optionUpdated=function(t,r){var i=this.option,n=(r?i:t).elements,o=i.elements=r?[]:i.elements,s=[];this._flatten(n,s,null);var l=vl(o,s,"normalMerge"),u=this._elOptionsToUpdate=[];w(l,function(h,v){var c=h.newOption;c&&(u.push(c),Av(h,c),wv(o,v,c),Cv(o[v],c))},this),i.elements=Pt(o,function(h){return h&&delete h.$action,h!=null})},e.prototype._flatten=function(t,r,i){w(t,function(n){if(n){i&&(n.parentOption=i),r.push(n);var o=n.children;o&&o.length&&this._flatten(o,r,n),delete n.children}},this)},e.prototype.useElOptionsToUpdate=function(){var t=this._elOptionsToUpdate;return this._elOptionsToUpdate=null,t},e.type="graphic",e.defaultOption={elements:[]},e}(Mt),ln={path:null,compoundPath:null,group:$,image:ga,text:tt},vt=ft(),Tv=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this._elMap=ot()},e.prototype.render=function(t,r,i){t!==this._lastGraphicModel&&this._clear(),this._lastGraphicModel=t,this._updateElements(t),this._relocate(t,i)},e.prototype._updateElements=function(t){var r=t.useElOptionsToUpdate();if(r){var i=this._elMap,n=this.group,o=t.get("z"),s=t.get("zlevel");w(r,function(l){var u=ve(l.id,null),h=u!=null?i.get(u):null,v=ve(l.parentId,null),c=v!=null?i.get(v):n,f=l.type,p=l.style;f==="text"&&p&&l.hv&&l.hv[1]&&(p.textVerticalAlign=p.textBaseline=p.verticalAlign=p.align=null);var d=l.textContent,g=l.textConfig;if(p&&ov(p,f,!!g,!!d)){var y=sv(p,f,!0);!g&&y.textConfig&&(g=l.textConfig=y.textConfig),!d&&y.textContent&&(d=y.textContent)}var m=Lv(l),S=l.$action||"merge",x=S==="merge",b=S==="replace";if(x){var _=!h,A=h;_?A=un(u,c,l.type,i):(A&&(vt(A).isNew=!1),xv(A)),A&&(Rr(A,m,t,{isInit:_}),hn(A,l,o,s))}else if(b){Ge(h,l,i,t);var D=un(u,c,l.type,i);D&&(Rr(D,m,t,{isInit:!0}),hn(D,l,o,s))}else S==="remove"&&(us(h,l),Ge(h,l,i,t));var M=i.get(u);if(M&&d)if(x){var T=M.getTextContent();T?T.attr(d):M.setTextContent(new tt(d))}else b&&M.setTextContent(new tt(d));if(M){var C=l.clipPath;if(C){var L=C.type,P=void 0,_=!1;if(x){var B=M.getClipPath();_=!B||vt(B).type!==L,P=_?ha(L):B}else b&&(_=!0,P=ha(L));M.setClipPath(P),Rr(P,C,t,{isInit:_}),ua(P,C.keyframeAnimation,t)}var V=vt(M);M.setTextConfig(g),V.option=l,Iv(M,t,l),fo({el:M,componentModel:t,itemName:M.name,itemTooltipOption:l.tooltip}),ua(M,l.keyframeAnimation,t)}})}},e.prototype._relocate=function(t,r){for(var i=t.option.elements,n=this.group,o=this._elMap,s=r.getWidth(),l=r.getHeight(),u=["x","y"],h=0;h<i.length;h++){var v=i[h],c=ve(v.id,null),f=c!=null?o.get(c):null;if(!(!f||!f.isGroup)){var p=f.parent,d=p===n,g=vt(f),y=vt(p);g.width=ct(g.option.width,d?s:y.width)||0,g.height=ct(g.option.height,d?l:y.height)||0}}for(var h=i.length-1;h>=0;h--){var v=i[h],c=ve(v.id,null),f=c!=null?o.get(c):null;if(f){var p=f.parent,y=vt(p),m=p===n?{width:s,height:l}:{width:y.width,height:y.height},S={},x=po(f,v,m,null,{hv:v.hv,boundingMode:v.bounding},S);if(!vt(f).isNew&&x){for(var b=v.transition,_={},A=0;A<u.length;A++){var D=u[A],M=S[D];b&&(Gt(b)||et(b,D)>=0)?_[D]=M:f[D]=M}ut(f,_,t,0)}else f.attr(S)}}},e.prototype._clear=function(){var t=this,r=this._elMap;r.each(function(i){Ge(i,vt(i).option,r,t._lastGraphicModel)}),this._elMap=ot()},e.prototype.dispose=function(){this._clear()},e.type="graphic",e}(Et);function ha(a){var e=k(ln,a)?ln[a]:pl(a),t=new e({});return vt(t).type=a,t}function un(a,e,t,r){var i=ha(t);return e.add(i),r.set(a,i),vt(i).id=a,vt(i).isNew=!0,i}function Ge(a,e,t,r){var i=a&&a.parent;i&&(a.type==="group"&&a.traverse(function(n){Ge(n,e,t,r)}),lv(a,e,r),t.removeKey(vt(a).id))}function hn(a,e,t,r){a.isGroup||w([["cursor",vo.prototype.cursor],["zlevel",r||0],["z",t||0],["z2",0]],function(i){var n=i[0];k(e,n)?a[n]=Lt(e[n],i[1]):a[n]==null&&(a[n]=i[1])}),w(Rt(e),function(i){if(i.indexOf("on")===0){var n=e[i];a[i]=rt(n)?n:null}}),k(e,"draggable")&&(a.draggable=e.draggable),e.name!=null&&(a.name=e.name),e.id!=null&&(a.id=e.id)}function Lv(a){return a=F({},a),w(["id","parentId","$action","hv","bounding","textContent","clipPath"].concat(fl),function(e){delete a[e]}),a}function Iv(a,e,t){var r=at(a).eventData;!a.silent&&!a.ignore&&!r&&(r=at(a).eventData={componentType:"graphic",componentIndex:e.componentIndex,name:a.name}),r&&(r.info=t.info)}function zf(a){a.registerComponentModel(Dv),a.registerComponentView(Tv),a.registerPreprocessor(function(e){var t=e.graphic;O(t)?!t[0]||!t[0].elements?e.graphic=[{elements:t}]:e.graphic=[e.graphic[0]]:t&&!t.elements&&(e.graphic=[{elements:[t]}])})}var vn=["x","y","radius","angle","single"],kv=["cartesian2d","polar","singleAxis"];function Pv(a){var e=a.get("coordinateSystem");return et(kv,e)>=0}function It(a){return a+"Axis"}function Rv(a,e){var t=ot(),r=[],i=ot();a.eachComponent({mainType:"dataZoom",query:e},function(h){i.get(h.uid)||s(h)});var n;do n=!1,a.eachComponent("dataZoom",o);while(n);function o(h){!i.get(h.uid)&&l(h)&&(s(h),n=!0)}function s(h){i.set(h.uid,!0),r.push(h),u(h)}function l(h){var v=!1;return h.eachTargetAxis(function(c,f){var p=t.get(c);p&&p[f]&&(v=!0)}),v}function u(h){h.eachTargetAxis(function(v,c){(t.get(v)||t.set(v,[]))[c]=!0})}return r}function cs(a){var e=a.ecModel,t={infoList:[],infoMap:ot()};return a.eachTargetAxis(function(r,i){var n=e.getComponent(It(r),i);if(n){var o=n.getCoordSysModel();if(o){var s=o.uid,l=t.infoMap.get(s);l||(l={model:o,axisModels:[]},t.infoList.push(l),t.infoMap.set(s,l)),l.axisModels.push(n)}}}),t}var Or=function(){function a(){this.indexList=[],this.indexMap=[]}return a.prototype.add=function(e){this.indexMap[e]||(this.indexList.push(e),this.indexMap[e]=!0)},a}(),_e=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._autoThrottle=!0,t._noTarget=!0,t._rangePropMode=["percent","percent"],t}return e.prototype.init=function(t,r,i){var n=cn(t);this.settledOption=n,this.mergeDefaultAndTheme(t,i),this._doInit(n)},e.prototype.mergeOption=function(t){var r=cn(t);Z(this.option,t,!0),Z(this.settledOption,r,!0),this._doInit(r)},e.prototype._doInit=function(t){var r=this.option;this._setDefaultThrottle(t),this._updateRangeUse(t);var i=this.settledOption;w([["start","startValue"],["end","endValue"]],function(n,o){this._rangePropMode[o]==="value"&&(r[n[0]]=i[n[0]]=null)},this),this._resetTarget()},e.prototype._resetTarget=function(){var t=this.get("orient",!0),r=this._targetAxisInfoMap=ot(),i=this._fillSpecifiedTargetAxis(r);i?this._orient=t||this._makeAutoOrientByTargetAxis():(this._orient=t||"horizontal",this._fillAutoTargetAxisByOrient(r,this._orient)),this._noTarget=!0,r.each(function(n){n.indexList.length&&(this._noTarget=!1)},this)},e.prototype._fillSpecifiedTargetAxis=function(t){var r=!1;return w(vn,function(i){var n=this.getReferringComponents(It(i),dl);if(n.specified){r=!0;var o=new Or;w(n.models,function(s){o.add(s.componentIndex)}),t.set(i,o)}},this),r},e.prototype._fillAutoTargetAxisByOrient=function(t,r){var i=this.ecModel,n=!0;if(n){var o=r==="vertical"?"y":"x",s=i.findComponents({mainType:o+"Axis"});l(s,o)}if(n){var s=i.findComponents({mainType:"singleAxis",filter:function(h){return h.get("orient",!0)===r}});l(s,"single")}function l(u,h){var v=u[0];if(v){var c=new Or;if(c.add(v.componentIndex),t.set(h,c),n=!1,h==="x"||h==="y"){var f=v.getReferringComponents("grid",dt).models[0];f&&w(u,function(p){v.componentIndex!==p.componentIndex&&f===p.getReferringComponents("grid",dt).models[0]&&c.add(p.componentIndex)})}}}n&&w(vn,function(u){if(n){var h=i.findComponents({mainType:It(u),filter:function(c){return c.get("type",!0)==="category"}});if(h[0]){var v=new Or;v.add(h[0].componentIndex),t.set(u,v),n=!1}}},this)},e.prototype._makeAutoOrientByTargetAxis=function(){var t;return this.eachTargetAxis(function(r){!t&&(t=r)},this),t==="y"?"vertical":"horizontal"},e.prototype._setDefaultThrottle=function(t){if(t.hasOwnProperty("throttle")&&(this._autoThrottle=!1),this._autoThrottle){var r=this.ecModel.option;this.option.throttle=r.animation&&r.animationDurationUpdate>0?100:20}},e.prototype._updateRangeUse=function(t){var r=this._rangePropMode,i=this.get("rangeMode");w([["start","startValue"],["end","endValue"]],function(n,o){var s=t[n[0]]!=null,l=t[n[1]]!=null;s&&!l?r[o]="percent":!s&&l?r[o]="value":i?r[o]=i[o]:s&&(r[o]="percent")})},e.prototype.noTarget=function(){return this._noTarget},e.prototype.getFirstTargetAxisModel=function(){var t;return this.eachTargetAxis(function(r,i){t==null&&(t=this.ecModel.getComponent(It(r),i))},this),t},e.prototype.eachTargetAxis=function(t,r){this._targetAxisInfoMap.each(function(i,n){w(i.indexList,function(o){t.call(r,n,o)})})},e.prototype.getAxisProxy=function(t,r){var i=this.getAxisModel(t,r);if(i)return i.__dzAxisProxy},e.prototype.getAxisModel=function(t,r){var i=this._targetAxisInfoMap.get(t);if(i&&i.indexMap[r])return this.ecModel.getComponent(It(t),r)},e.prototype.setRawRange=function(t){var r=this.option,i=this.settledOption;w([["start","startValue"],["end","endValue"]],function(n){(t[n[0]]!=null||t[n[1]]!=null)&&(r[n[0]]=i[n[0]]=t[n[0]],r[n[1]]=i[n[1]]=t[n[1]])},this),this._updateRangeUse(t)},e.prototype.setCalculatedRange=function(t){var r=this.option;w(["start","startValue","end","endValue"],function(i){r[i]=t[i]})},e.prototype.getPercentRange=function(){var t=this.findRepresentativeAxisProxy();if(t)return t.getDataPercentWindow()},e.prototype.getValueRange=function(t,r){if(t==null&&r==null){var i=this.findRepresentativeAxisProxy();if(i)return i.getDataValueWindow()}else return this.getAxisProxy(t,r).getDataValueWindow()},e.prototype.findRepresentativeAxisProxy=function(t){if(t)return t.__dzAxisProxy;for(var r,i=this._targetAxisInfoMap.keys(),n=0;n<i.length;n++)for(var o=i[n],s=this._targetAxisInfoMap.get(o),l=0;l<s.indexList.length;l++){var u=this.getAxisProxy(o,s.indexList[l]);if(u.hostedBy(this))return u;r||(r=u)}return r},e.prototype.getRangePropMode=function(){return this._rangePropMode.slice()},e.prototype.getOrient=function(){return this._orient},e.type="dataZoom",e.dependencies=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","series","toolbox"],e.defaultOption={z:4,filterMode:"filter",start:0,end:100},e}(Mt);function cn(a){var e={};return w(["start","end","startValue","endValue","throttle"],function(t){a.hasOwnProperty(t)&&(e[t]=a[t])}),e}var Ev=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.select",e}(_e),Na=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,r,i,n){this.dataZoomModel=t,this.ecModel=r,this.api=i},e.type="dataZoom",e}(Et),Ov=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.select",e}(Na),jt=w,fn=bt,Vv=function(){function a(e,t,r,i){this._dimName=e,this._axisIndex=t,this.ecModel=i,this._dataZoomModel=r}return a.prototype.hostedBy=function(e){return this._dataZoomModel===e},a.prototype.getDataValueWindow=function(){return this._valueWindow.slice()},a.prototype.getDataPercentWindow=function(){return this._percentWindow.slice()},a.prototype.getTargetSeriesModels=function(){var e=[];return this.ecModel.eachSeries(function(t){if(Pv(t)){var r=It(this._dimName),i=t.getReferringComponents(r,dt).models[0];i&&this._axisIndex===i.componentIndex&&e.push(t)}},this),e},a.prototype.getAxisModel=function(){return this.ecModel.getComponent(this._dimName+"Axis",this._axisIndex)},a.prototype.getMinMaxSpan=function(){return U(this._minMaxSpan)},a.prototype.calculateDataWindow=function(e){var t=this._dataExtent,r=this.getAxisModel(),i=r.axis.scale,n=this._dataZoomModel.getRangePropMode(),o=[0,100],s=[],l=[],u;jt(["start","end"],function(c,f){var p=e[c],d=e[c+"Value"];n[f]==="percent"?(p==null&&(p=o[f]),d=i.parse(H(p,o,t))):(u=!0,d=d==null?t[f]:i.parse(d),p=H(d,t,o)),l[f]=d==null||isNaN(d)?t[f]:d,s[f]=p==null||isNaN(p)?o[f]:p}),fn(l),fn(s);var h=this._minMaxSpan;u?v(l,s,t,o,!1):v(s,l,o,t,!0);function v(c,f,p,d,g){var y=g?"Span":"ValueSpan";oe(0,c,p,"all",h["min"+y],h["max"+y]);for(var m=0;m<2;m++)f[m]=H(c[m],p,d,!0),g&&(f[m]=i.parse(f[m]))}return{valueWindow:l,percentWindow:s}},a.prototype.reset=function(e){if(e===this._dataZoomModel){var t=this.getTargetSeriesModels();this._dataExtent=zv(this,this._dimName,t),this._updateMinMaxSpan();var r=this.calculateDataWindow(e.settledOption);this._valueWindow=r.valueWindow,this._percentWindow=r.percentWindow,this._setAxisModel()}},a.prototype.filterData=function(e,t){if(e!==this._dataZoomModel)return;var r=this._dimName,i=this.getTargetSeriesModels(),n=e.get("filterMode"),o=this._valueWindow;if(n==="none")return;jt(i,function(l){var u=l.getData(),h=u.mapDimensionsAll(r);if(h.length){if(n==="weakFilter"){var v=u.getStore(),c=E(h,function(f){return u.getDimensionIndex(f)},u);u.filterSelf(function(f){for(var p,d,g,y=0;y<h.length;y++){var m=v.get(c[y],f),S=!isNaN(m),x=m<o[0],b=m>o[1];if(S&&!x&&!b)return!0;S&&(g=!0),x&&(p=!0),b&&(d=!0)}return g&&p&&d})}else jt(h,function(f){if(n==="empty")l.setData(u=u.map(f,function(d){return s(d)?d:NaN}));else{var p={};p[f]=o,u.selectRange(p)}});jt(h,function(f){u.setApproximateExtent(o,f)})}});function s(l){return l>=o[0]&&l<=o[1]}},a.prototype._updateMinMaxSpan=function(){var e=this._minMaxSpan={},t=this._dataZoomModel,r=this._dataExtent;jt(["min","max"],function(i){var n=t.get(i+"Span"),o=t.get(i+"ValueSpan");o!=null&&(o=this.getAxisModel().axis.scale.parse(o)),o!=null?n=H(r[0]+o,r,[0,100],!0):n!=null&&(o=H(n,[0,100],r,!0)-r[0]),e[i+"Span"]=n,e[i+"ValueSpan"]=o},this)},a.prototype._setAxisModel=function(){var e=this.getAxisModel(),t=this._percentWindow,r=this._valueWindow;if(t){var i=ao(r,[0,500]);i=Math.min(i,20);var n=e.axis.scale.rawExtentInfo;t[0]!==0&&n.setDeterminedMinMax("min",+r[0].toFixed(i)),t[1]!==100&&n.setDeterminedMinMax("max",+r[1].toFixed(i)),n.freeze()}},a}();function zv(a,e,t){var r=[1/0,-1/0];jt(t,function(o){gl(r,o.getData(),e)});var i=a.getAxisModel(),n=yl(i.axis.scale,i,r).calculate();return[n.min,n.max]}var Bv={getTargetSeries:function(a){function e(i){a.eachComponent("dataZoom",function(n){n.eachTargetAxis(function(o,s){var l=a.getComponent(It(o),s);i(o,s,l,n)})})}e(function(i,n,o,s){o.__dzAxisProxy=null});var t=[];e(function(i,n,o,s){o.__dzAxisProxy||(o.__dzAxisProxy=new Vv(i,n,s,a),t.push(o.__dzAxisProxy))});var r=ot();return w(t,function(i){w(i.getTargetSeriesModels(),function(n){r.set(n.uid,n)})}),r},overallReset:function(a,e){a.eachComponent("dataZoom",function(t){t.eachTargetAxis(function(r,i){t.getAxisProxy(r,i).reset(t)}),t.eachTargetAxis(function(r,i){t.getAxisProxy(r,i).filterData(t,e)})}),a.eachComponent("dataZoom",function(t){var r=t.findRepresentativeAxisProxy();if(r){var i=r.getDataPercentWindow(),n=r.getDataValueWindow();t.setCalculatedRange({start:i[0],end:i[1],startValue:n[0],endValue:n[1]})}})}};function Nv(a){a.registerAction("dataZoom",function(e,t){var r=Rv(t,e);w(r,function(i){i.setRawRange({start:e.start,end:e.end,startValue:e.startValue,endValue:e.endValue})})})}var pn=!1;function Ga(a){pn||(pn=!0,a.registerProcessor(a.PRIORITY.PROCESSOR.FILTER,Bv),Nv(a),a.registerSubTypeDefaulter("dataZoom",function(){return"slider"}))}function Gv(a){a.registerComponentModel(Ev),a.registerComponentView(Ov),Ga(a)}var pt=function(){function a(){}return a}(),fs={};function le(a,e){fs[a]=e}function ps(a){return fs[a]}var Fv=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){a.prototype.optionUpdated.apply(this,arguments);var t=this.ecModel;w(this.option.feature,function(r,i){var n=ps(i);n&&(n.getDefaultOption&&(n.defaultOption=n.getDefaultOption(t)),Z(r,n.defaultOption))})},e.type="toolbox",e.layoutMode={type:"box",ignoreSize:!0},e.defaultOption={show:!0,z:6,orient:"horizontal",left:"right",top:"top",backgroundColor:"transparent",borderColor:"#ccc",borderRadius:0,borderWidth:0,padding:5,itemSize:15,itemGap:8,showTitle:!0,iconStyle:{borderColor:"#666",color:"none"},emphasis:{iconStyle:{borderColor:"#3E98C5"}},tooltip:{show:!1,position:"bottom"}},e}(Mt),Wv=function(a){I(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.render=function(t,r,i,n){var o=this.group;if(o.removeAll(),!t.get("show"))return;var s=+t.get("itemSize"),l=t.get("orient")==="vertical",u=t.get("feature")||{},h=this._features||(this._features={}),v=[];w(u,function(p,d){v.push(d)}),new ho(this._featureNames||[],v).add(c).update(c).remove(G(c,null)).execute(),this._featureNames=v;function c(p,d){var g=v[p],y=v[d],m=u[g],S=new ml(m,t,t.ecModel),x;if(n&&n.newTitle!=null&&n.featureName===g&&(m.title=n.newTitle),g&&!y){if(Hv(g))x={onclick:S.option.onclick,featureName:g};else{var b=ps(g);if(!b)return;x=new b}h[g]=x}else if(x=h[y],!x)return;x.uid=xl("toolbox-feature"),x.model=S,x.ecModel=r,x.api=i;var _=x instanceof pt;if(!g&&y){_&&x.dispose&&x.dispose(r,i);return}if(!S.get("show")||_&&x.unusable){_&&x.remove&&x.remove(r,i);return}f(S,x,g),S.setIconStatus=function(A,D){var M=this.option,T=this.iconPaths;M.iconStatus=M.iconStatus||{},M.iconStatus[A]=D,T[A]&&(D==="emphasis"?We:He)(T[A])},x instanceof pt&&x.render&&x.render(S,r,i,n)}function f(p,d,g){var y=p.getModel("iconStyle"),m=p.getModel(["emphasis","iconStyle"]),S=d instanceof pt&&d.getIcons?d.getIcons():p.get("icon"),x=p.get("title")||{},b,_;it(S)?(b={},b[g]=S):b=S,it(x)?(_={},_[g]=x):_=x;var A=p.iconPaths={};w(b,function(D,M){var T=go(D,{},{x:-s/2,y:-s/2,width:s,height:s});T.setStyle(y.getItemStyle());var C=T.ensureState("emphasis");C.style=m.getItemStyle();var L=new tt({style:{text:_[M],align:m.get("textAlign"),borderRadius:m.get("textBorderRadius"),padding:m.get("textPadding"),fill:null,font:Sl({fontStyle:m.get("textFontStyle"),fontFamily:m.get("textFontFamily"),fontSize:m.get("textFontSize"),fontWeight:m.get("textFontWeight")},r)},ignore:!0});T.setTextContent(L),fo({el:T,componentModel:t,itemName:M,formatterParamsExtra:{title:_[M]}}),T.__title=_[M],T.on("mouseover",function(){var P=m.getItemStyle(),B=l?t.get("right")==null&&t.get("left")!=="right"?"right":"left":t.get("bottom")==null&&t.get("top")!=="bottom"?"bottom":"top";L.setStyle({fill:m.get("textFill")||P.fill||P.stroke||"#000",backgroundColor:m.get("textBackgroundColor")}),T.setTextConfig({position:m.get("textPosition")||B}),L.ignore=!t.get("showTitle"),i.enterEmphasis(this)}).on("mouseout",function(){p.get(["iconStatus",M])!=="emphasis"&&i.leaveEmphasis(this),L.hide()}),(p.get(["iconStatus",M])==="emphasis"?We:He)(T),o.add(T),T.on("click",R(d.onclick,d,r,i,M)),A[M]=T})}bl(o,t,i),o.add(_l(o.getBoundingRect(),t)),l||o.eachChild(function(p){var d=p.__title,g=p.ensureState("emphasis"),y=g.textConfig||(g.textConfig={}),m=p.getTextContent(),S=m&&m.ensureState("emphasis");if(S&&!rt(S)&&d){var x=S.style||(S.style={}),b=eo(d,tt.makeFont(x)),_=p.x+o.x,A=p.y+o.y+s,D=!1;A+b.height>i.getHeight()&&(y.position="top",D=!0);var M=D?-5-b.height:s+10;_+b.width/2>i.getWidth()?(y.position=["100%",M],x.align="right"):_-b.width/2<0&&(y.position=[0,M],x.align="left")}})},e.prototype.updateView=function(t,r,i,n){w(this._features,function(o){o instanceof pt&&o.updateView&&o.updateView(o.model,r,i,n)})},e.prototype.remove=function(t,r){w(this._features,function(i){i instanceof pt&&i.remove&&i.remove(t,r)}),this.group.removeAll()},e.prototype.dispose=function(t,r){w(this._features,function(i){i instanceof pt&&i.dispose&&i.dispose(t,r)})},e.type="toolbox",e}(Et);function Hv(a){return a.indexOf("my")===0}var Zv=function(a){I(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.onclick=function(t,r){var i=this.model,n=i.get("name")||t.get("title.0.text")||"echarts",o=r.getZr().painter.getType()==="svg",s=o?"svg":i.get("type",!0)||"png",l=r.getConnectedDataURL({type:s,backgroundColor:i.get("backgroundColor",!0)||t.get("backgroundColor")||"#fff",connectedBackgroundColor:i.get("connectedBackgroundColor"),excludeComponents:i.get("excludeComponents"),pixelRatio:i.get("pixelRatio")}),u=yo.browser;if(typeof MouseEvent=="function"&&(u.newEdge||!u.ie&&!u.edge)){var h=document.createElement("a");h.download=n+"."+s,h.target="_blank",h.href=l;var v=new MouseEvent("click",{view:document.defaultView,bubbles:!0,cancelable:!1});h.dispatchEvent(v)}else if(window.navigator.msSaveOrOpenBlob||o){var c=l.split(","),f=c[0].indexOf("base64")>-1,p=o?decodeURIComponent(c[1]):c[1];f&&(p=window.atob(p));var d=n+"."+s;if(window.navigator.msSaveOrOpenBlob){for(var g=p.length,y=new Uint8Array(g);g--;)y[g]=p.charCodeAt(g);var m=new Blob([y]);window.navigator.msSaveOrOpenBlob(m,d)}else{var S=document.createElement("iframe");document.body.appendChild(S);var x=S.contentWindow,b=x.document;b.open("image/svg+xml","replace"),b.write(p),b.close(),x.focus(),b.execCommand("SaveAs",!0,d),document.body.removeChild(S)}}else{var _=i.get("lang"),A='<body style="margin:0;"><img src="'+l+'" style="max-width:100%;" title="'+(_&&_[0]||"")+'" /></body>',D=window.open();D.document.write(A),D.document.title=n}},e.getDefaultOption=function(t){var r={show:!0,icon:"M4.7,22.9L29.3,45.5L54.7,23.4M4.6,43.6L4.6,58L53.8,58L53.8,43.6M29.2,45.1L29.2,0",title:t.getLocaleModel().get(["toolbox","saveAsImage","title"]),type:"png",connectedBackgroundColor:"#fff",name:"",excludeComponents:["toolbox"],lang:t.getLocaleModel().get(["toolbox","saveAsImage","lang"])};return r},e}(pt),dn="__ec_magicType_stack__",Uv=[["line","bar"],["stack"]],Xv=function(a){I(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.getIcons=function(){var t=this.model,r=t.get("icon"),i={};return w(t.get("type"),function(n){r[n]&&(i[n]=r[n])}),i},e.getDefaultOption=function(t){var r={show:!0,type:[],icon:{line:"M4.1,28.9h7.1l9.3-22l7.4,38l9.7-19.7l3,12.8h14.9M4.1,58h51.4",bar:"M6.7,22.9h10V48h-10V22.9zM24.9,13h10v35h-10V13zM43.2,2h10v46h-10V2zM3.1,58h53.7",stack:"M8.2,38.4l-8.4,4.1l30.6,15.3L60,42.5l-8.1-4.1l-21.5,11L8.2,38.4z M51.9,30l-8.1,4.2l-13.4,6.9l-13.9-6.9L8.2,30l-8.4,4.2l8.4,4.2l22.2,11l21.5-11l8.1-4.2L51.9,30z M51.9,21.7l-8.1,4.2L35.7,30l-5.3,2.8L24.9,30l-8.4-4.1l-8.3-4.2l-8.4,4.2L8.2,30l8.3,4.2l13.9,6.9l13.4-6.9l8.1-4.2l8.1-4.1L51.9,21.7zM30.4,2.2L-0.2,17.5l8.4,4.1l8.3,4.2l8.4,4.2l5.5,2.7l5.3-2.7l8.1-4.2l8.1-4.2l8.1-4.1L30.4,2.2z"},title:t.getLocaleModel().get(["toolbox","magicType","title"]),option:{},seriesIndex:{}};return r},e.prototype.onclick=function(t,r,i){var n=this.model,o=n.get(["seriesIndex",i]);if(gn[i]){var s={series:[]},l=function(v){var c=v.subType,f=v.id,p=gn[i](c,f,v,n);p&&(ht(p,v.option),s.series.push(p));var d=v.coordinateSystem;if(d&&d.type==="cartesian2d"&&(i==="line"||i==="bar")){var g=d.getAxesByScale("ordinal")[0];if(g){var y=g.dim,m=y+"Axis",S=v.getReferringComponents(m,dt).models[0],x=S.componentIndex;s[m]=s[m]||[];for(var b=0;b<=x;b++)s[m][x]=s[m][x]||{};s[m][x].boundaryGap=i==="bar"}}};w(Uv,function(v){et(v,i)>=0&&w(v,function(c){n.setIconStatus(c,"normal")})}),n.setIconStatus(i,"emphasis"),t.eachComponent({mainType:"series",query:o==null?null:{seriesIndex:o}},l);var u,h=i;i==="stack"&&(u=Z({stack:n.option.title.tiled,tiled:n.option.title.stack},n.option.title),n.get(["iconStatus",i])!=="emphasis"&&(h="tiled")),r.dispatchAction({type:"changeMagicType",currentType:h,newOption:s,newTitle:u,featureName:"magicType"})}},e}(pt),gn={line:function(a,e,t,r){if(a==="bar")return Z({id:e,type:"line",data:t.get("data"),stack:t.get("stack"),markPoint:t.get("markPoint"),markLine:t.get("markLine")},r.get(["option","line"])||{},!0)},bar:function(a,e,t,r){if(a==="line")return Z({id:e,type:"bar",data:t.get("data"),stack:t.get("stack"),markPoint:t.get("markPoint"),markLine:t.get("markLine")},r.get(["option","bar"])||{},!0)},stack:function(a,e,t,r){var i=t.get("stack")===dn;if(a==="line"||a==="bar")return r.setIconStatus("stack",i?"normal":"emphasis"),Z({id:e,stack:i?"":dn},r.get(["option","stack"])||{},!0)}};nr({type:"changeMagicType",event:"magicTypeChanged",update:"prepareAndUpdate"},function(a,e){e.mergeOption(a.newOption)});var ur=new Array(60).join("-"),ae="	";function Yv(a){var e={},t=[],r=[];return a.eachRawSeries(function(i){var n=i.coordinateSystem;if(n&&(n.type==="cartesian2d"||n.type==="polar")){var o=n.getBaseAxis();if(o.type==="category"){var s=o.dim+"_"+o.index;e[s]||(e[s]={categoryAxis:o,valueAxis:n.getOtherAxis(o),series:[]},r.push({axisDim:o.dim,axisIndex:o.index})),e[s].series.push(i)}else t.push(i)}else t.push(i)}),{seriesGroupByCategoryAxis:e,other:t,meta:r}}function $v(a){var e=[];return w(a,function(t,r){var i=t.categoryAxis,n=t.valueAxis,o=n.dim,s=[" "].concat(E(t.series,function(f){return f.name})),l=[i.model.getCategories()];w(t.series,function(f){var p=f.getRawData();l.push(f.getRawData().mapArray(p.mapDimension(o),function(d){return d}))});for(var u=[s.join(ae)],h=0;h<l[0].length;h++){for(var v=[],c=0;c<l.length;c++)v.push(l[c][h]);u.push(v.join(ae))}e.push(u.join(`
`))}),e.join(`

`+ur+`

`)}function Kv(a){return E(a,function(e){var t=e.getRawData(),r=[e.name],i=[];return t.each(t.dimensions,function(){for(var n=arguments.length,o=arguments[n-1],s=t.getName(o),l=0;l<n-1;l++)i[l]=arguments[l];r.push((s?s+ae:"")+i.join(ae))}),r.join(`
`)}).join(`

`+ur+`

`)}function jv(a){var e=Yv(a);return{value:Pt([$v(e.seriesGroupByCategoryAxis),Kv(e.other)],function(t){return!!t.replace(/[\n\t\s]/g,"")}).join(`

`+ur+`

`),meta:e.meta}}function $e(a){return a.replace(/^\s\s*/,"").replace(/\s\s*$/,"")}function qv(a){var e=a.slice(0,a.indexOf(`
`));if(e.indexOf(ae)>=0)return!0}var va=new RegExp("["+ae+"]+","g");function Qv(a){for(var e=a.split(/\n+/g),t=$e(e.shift()).split(va),r=[],i=E(t,function(l){return{name:l,data:[]}}),n=0;n<e.length;n++){var o=$e(e[n]).split(va);r.push(o.shift());for(var s=0;s<o.length;s++)i[s]&&(i[s].data[n]=o[s])}return{series:i,categories:r}}function Jv(a){for(var e=a.split(/\n+/g),t=$e(e.shift()),r=[],i=0;i<e.length;i++){var n=$e(e[i]);if(n){var o=n.split(va),s="",l=void 0,u=!1;isNaN(o[0])?(u=!0,s=o[0],o=o.slice(1),r[i]={name:s,value:[]},l=r[i].value):l=r[i]=[];for(var h=0;h<o.length;h++)l.push(+o[h]);l.length===1&&(u?r[i].value=l[0]:r[i]=l[0])}}return{name:t,data:r}}function tc(a,e){var t=a.split(new RegExp(`
*`+ur+`
*`,"g")),r={series:[]};return w(t,function(i,n){if(qv(i)){var o=Qv(i),s=e[n],l=s.axisDim+"Axis";s&&(r[l]=r[l]||[],r[l][s.axisIndex]={data:o.categories},r.series=r.series.concat(o.series))}else{var o=Jv(i);r.series.push(o)}}),r}var ec=function(a){I(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.onclick=function(t,r){setTimeout(function(){r.dispatchAction({type:"hideTip"})});var i=r.getDom(),n=this.model;this._dom&&i.removeChild(this._dom);var o=document.createElement("div");o.style.cssText="position:absolute;top:0;bottom:0;left:0;right:0;padding:5px",o.style.backgroundColor=n.get("backgroundColor")||"#fff";var s=document.createElement("h4"),l=n.get("lang")||[];s.innerHTML=l[0]||n.get("title"),s.style.cssText="margin:10px 20px",s.style.color=n.get("textColor");var u=document.createElement("div"),h=document.createElement("textarea");u.style.cssText="overflow:auto";var v=n.get("optionToContent"),c=n.get("contentToOption"),f=jv(t);if(rt(v)){var p=v(r.getOption());it(p)?u.innerHTML=p:Al(p)&&u.appendChild(p)}else{h.readOnly=n.get("readOnly");var d=h.style;d.cssText="display:block;width:100%;height:100%;font-family:monospace;font-size:14px;line-height:1.6rem;resize:none;box-sizing:border-box;outline:none",d.color=n.get("textColor"),d.borderColor=n.get("textareaBorderColor"),d.backgroundColor=n.get("textareaColor"),h.value=f.value,u.appendChild(h)}var g=f.meta,y=document.createElement("div");y.style.cssText="position:absolute;bottom:5px;left:0;right:0";var m="float:right;margin-right:20px;border:none;cursor:pointer;padding:2px 5px;font-size:12px;border-radius:3px",S=document.createElement("div"),x=document.createElement("div");m+=";background-color:"+n.get("buttonColor"),m+=";color:"+n.get("buttonTextColor");var b=this;function _(){i.removeChild(o),b._dom=null}si(S,"click",_),si(x,"click",function(){if(c==null&&v!=null||c!=null&&v==null){_();return}var A;try{rt(c)?A=c(u,r.getOption()):A=tc(h.value,g)}catch(D){throw _(),new Error("Data view format error "+D)}A&&r.dispatchAction({type:"changeDataView",newOption:A}),_()}),S.innerHTML=l[1],x.innerHTML=l[2],x.style.cssText=S.style.cssText=m,!n.get("readOnly")&&y.appendChild(x),y.appendChild(S),o.appendChild(s),o.appendChild(u),o.appendChild(y),u.style.height=i.clientHeight-80+"px",i.appendChild(o),this._dom=o},e.prototype.remove=function(t,r){this._dom&&r.getDom().removeChild(this._dom)},e.prototype.dispose=function(t,r){this.remove(t,r)},e.getDefaultOption=function(t){var r={show:!0,readOnly:!1,optionToContent:null,contentToOption:null,icon:"M17.5,17.3H33 M17.5,17.3H33 M45.4,29.5h-28 M11.5,2v56H51V14.8L38.4,2H11.5z M38.4,2.2v12.7H51 M45.4,41.7h-28",title:t.getLocaleModel().get(["toolbox","dataView","title"]),lang:t.getLocaleModel().get(["toolbox","dataView","lang"]),backgroundColor:"#fff",textColor:"#000",textareaColor:"#fff",textareaBorderColor:"#333",buttonColor:"#c23531",buttonTextColor:"#fff"};return r},e}(pt);function rc(a,e){return E(a,function(t,r){var i=e&&e[r];if(st(i)&&!O(i)){var n=st(t)&&!O(t);n||(t={value:t});var o=i.name!=null&&t.name==null;return t=ht(t,i),o&&delete t.name,t}else return t})}nr({type:"changeDataView",event:"dataViewChanged",update:"prepareAndUpdate"},function(a,e){var t=[];w(a.newOption.series,function(r){var i=e.getSeriesByName(r.name)[0];if(!i)t.push(F({type:"scatter"},r));else{var n=i.get("data");t.push({name:r.name,data:rc(r.data,n)})}}),e.mergeOption(ht({series:t},a.newOption))});var ds=w,gs=ft();function ac(a,e){var t=Fa(a);ds(e,function(r,i){for(var n=t.length-1;n>=0;n--){var o=t[n];if(o[i])break}if(n<0){var s=a.queryComponents({mainType:"dataZoom",subType:"select",id:i})[0];if(s){var l=s.getPercentRange();t[0][i]={dataZoomId:i,start:l[0],end:l[1]}}}}),t.push(e)}function ic(a){var e=Fa(a),t=e[e.length-1];e.length>1&&e.pop();var r={};return ds(t,function(i,n){for(var o=e.length-1;o>=0;o--)if(i=e[o][n],i){r[n]=i;break}}),r}function nc(a){gs(a).snapshots=null}function oc(a){return Fa(a).length}function Fa(a){var e=gs(a);return e.snapshots||(e.snapshots=[{}]),e.snapshots}var sc=function(a){I(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.onclick=function(t,r){nc(t),r.dispatchAction({type:"restore",from:this.uid})},e.getDefaultOption=function(t){var r={show:!0,icon:"M3.8,33.4 M47,18.9h9.8V8.7 M56.3,20.1 C52.1,9,40.5,0.6,26.8,2.1C12.6,3.7,1.6,16.2,2.1,30.6 M13,41.1H3.1v10.2 M3.7,39.9c4.2,11.1,15.8,19.5,29.5,18 c14.2-1.6,25.2-14.1,24.7-28.5",title:t.getLocaleModel().get(["toolbox","restore","title"])};return r},e}(pt);nr({type:"restore",event:"restore",update:"prepareAndUpdate"},function(a,e){e.resetOption("recreate")});var lc=["grid","xAxis","yAxis","geo","graph","polar","radiusAxis","angleAxis","bmap"],ys=function(){function a(e,t,r){var i=this;this._targetInfoList=[];var n=yn(t,e);w(uc,function(o,s){(!r||!r.include||et(r.include,s)>=0)&&o(n,i._targetInfoList)})}return a.prototype.setOutputRanges=function(e,t){return this.matchOutputRanges(e,t,function(r,i,n){if((r.coordRanges||(r.coordRanges=[])).push(i),!r.coordRange){r.coordRange=i;var o=Vr[r.brushType](0,n,i);r.__rangeOffset={offset:bn[r.brushType](o.values,r.range,[1,1]),xyMinMax:o.xyMinMax}}}),e},a.prototype.matchOutputRanges=function(e,t,r){w(e,function(i){var n=this.findTargetInfo(i,t);n&&n!==!0&&w(n.coordSyses,function(o){var s=Vr[i.brushType](1,o,i.range,!0);r(i,s.values,o,t)})},this)},a.prototype.setInputRanges=function(e,t){w(e,function(r){var i=this.findTargetInfo(r,t);if(r.range=r.range||[],i&&i!==!0){r.panelId=i.panelId;var n=Vr[r.brushType](0,i.coordSys,r.coordRange),o=r.__rangeOffset;r.range=o?bn[r.brushType](n.values,o.offset,hc(n.xyMinMax,o.xyMinMax)):n.values}},this)},a.prototype.makePanelOpts=function(e,t){return E(this._targetInfoList,function(r){var i=r.getPanelRect();return{panelId:r.panelId,defaultBrushType:t?t(r):null,clipPath:Fh(i),isTargetByCursor:Hh(i,e,r.coordSysModel),getLinearBrushOtherExtent:Wh(i)}})},a.prototype.controlSeries=function(e,t,r){var i=this.findTargetInfo(e,r);return i===!0||i&&et(i.coordSyses,t.coordinateSystem)>=0},a.prototype.findTargetInfo=function(e,t){for(var r=this._targetInfoList,i=yn(t,e),n=0;n<r.length;n++){var o=r[n],s=e.panelId;if(s){if(o.panelId===s)return o}else for(var l=0;l<mn.length;l++)if(mn[l](i,o))return o}return!0},a}();function ca(a){return a[0]>a[1]&&a.reverse(),a}function yn(a,e){return mo(a,e,{includeMainTypes:lc})}var uc={grid:function(a,e){var t=a.xAxisModels,r=a.yAxisModels,i=a.gridModels,n=ot(),o={},s={};!t&&!r&&!i||(w(t,function(l){var u=l.axis.grid.model;n.set(u.id,u),o[u.id]=!0}),w(r,function(l){var u=l.axis.grid.model;n.set(u.id,u),s[u.id]=!0}),w(i,function(l){n.set(l.id,l),o[l.id]=!0,s[l.id]=!0}),n.each(function(l){var u=l.coordinateSystem,h=[];w(u.getCartesians(),function(v,c){(et(t,v.getAxis("x").model)>=0||et(r,v.getAxis("y").model)>=0)&&h.push(v)}),e.push({panelId:"grid--"+l.id,gridModel:l,coordSysModel:l,coordSys:h[0],coordSyses:h,getPanelRect:xn.grid,xAxisDeclared:o[l.id],yAxisDeclared:s[l.id]})}))},geo:function(a,e){w(a.geoModels,function(t){var r=t.coordinateSystem;e.push({panelId:"geo--"+t.id,geoModel:t,coordSysModel:t,coordSys:r,coordSyses:[r],getPanelRect:xn.geo})})}},mn=[function(a,e){var t=a.xAxisModel,r=a.yAxisModel,i=a.gridModel;return!i&&t&&(i=t.axis.grid.model),!i&&r&&(i=r.axis.grid.model),i&&i===e.gridModel},function(a,e){var t=a.geoModel;return t&&t===e.geoModel}],xn={grid:function(){return this.coordSys.master.getRect().clone()},geo:function(){var a=this.coordSys,e=a.getBoundingRect().clone();return e.applyTransform(Jt(a)),e}},Vr={lineX:G(Sn,0),lineY:G(Sn,1),rect:function(a,e,t,r){var i=a?e.pointToData([t[0][0],t[1][0]],r):e.dataToPoint([t[0][0],t[1][0]],r),n=a?e.pointToData([t[0][1],t[1][1]],r):e.dataToPoint([t[0][1],t[1][1]],r),o=[ca([i[0],n[0]]),ca([i[1],n[1]])];return{values:o,xyMinMax:o}},polygon:function(a,e,t,r){var i=[[1/0,-1/0],[1/0,-1/0]],n=E(t,function(o){var s=a?e.pointToData(o,r):e.dataToPoint(o,r);return i[0][0]=Math.min(i[0][0],s[0]),i[1][0]=Math.min(i[1][0],s[1]),i[0][1]=Math.max(i[0][1],s[0]),i[1][1]=Math.max(i[1][1],s[1]),s});return{values:n,xyMinMax:i}}};function Sn(a,e,t,r){var i=t.getAxis(["x","y"][a]),n=ca(E([0,1],function(s){return e?i.coordToData(i.toLocalCoord(r[s]),!0):i.toGlobalCoord(i.dataToCoord(r[s]))})),o=[];return o[a]=n,o[1-a]=[NaN,NaN],{values:n,xyMinMax:o}}var bn={lineX:G(_n,0),lineY:G(_n,1),rect:function(a,e,t){return[[a[0][0]-t[0]*e[0][0],a[0][1]-t[0]*e[0][1]],[a[1][0]-t[1]*e[1][0],a[1][1]-t[1]*e[1][1]]]},polygon:function(a,e,t){return E(a,function(r,i){return[r[0]-t[0]*e[i][0],r[1]-t[1]*e[i][1]]})}};function _n(a,e,t,r){return[e[0]-r[a]*t[0],e[1]-r[a]*t[1]]}function hc(a,e){var t=An(a),r=An(e),i=[t[0]/r[0],t[1]/r[1]];return isNaN(i[0])&&(i[0]=1),isNaN(i[1])&&(i[1]=1),i}function An(a){return a?[a[0][1]-a[0][0],a[1][1]-a[1][0]]:[NaN,NaN]}var fa=w,vc=Ml("toolbox-dataZoom_"),cc=function(a){I(e,a);function e(){return a!==null&&a.apply(this,arguments)||this}return e.prototype.render=function(t,r,i,n){this._brushController||(this._brushController=new Ph(i.getZr()),this._brushController.on("brush",R(this._onBrush,this)).mount()),dc(t,r,this,n,i),pc(t,r)},e.prototype.onclick=function(t,r,i){fc[i].call(this)},e.prototype.remove=function(t,r){this._brushController&&this._brushController.unmount()},e.prototype.dispose=function(t,r){this._brushController&&this._brushController.dispose()},e.prototype._onBrush=function(t){var r=t.areas;if(!t.isEnd||!r.length)return;var i={},n=this.ecModel;this._brushController.updateCovers([]);var o=new ys(Wa(this.model),n,{include:["grid"]});o.matchOutputRanges(r,n,function(u,h,v){if(v.type==="cartesian2d"){var c=u.brushType;c==="rect"?(s("x",v,h[0]),s("y",v,h[1])):s({lineX:"x",lineY:"y"}[c],v,h)}}),ac(n,i),this._dispatchZoomAction(i);function s(u,h,v){var c=h.getAxis(u),f=c.model,p=l(u,f,n),d=p.findRepresentativeAxisProxy(f).getMinMaxSpan();(d.minValueSpan!=null||d.maxValueSpan!=null)&&(v=oe(0,v.slice(),c.scale.getExtent(),0,d.minValueSpan,d.maxValueSpan)),p&&(i[p.id]={dataZoomId:p.id,startValue:v[0],endValue:v[1]})}function l(u,h,v){var c;return v.eachComponent({mainType:"dataZoom",subType:"select"},function(f){var p=f.getAxisModel(u,h.componentIndex);p&&(c=f)}),c}},e.prototype._dispatchZoomAction=function(t){var r=[];fa(t,function(i,n){r.push(U(i))}),r.length&&this.api.dispatchAction({type:"dataZoom",from:this.uid,batch:r})},e.getDefaultOption=function(t){var r={show:!0,filterMode:"filter",icon:{zoom:"M0,13.5h26.9 M13.5,26.9V0 M32.1,13.5H58V58H13.5 V32.1",back:"M22,1.4L9.9,13.5l12.3,12.3 M10.3,13.5H54.9v44.6 H10.3v-26"},title:t.getLocaleModel().get(["toolbox","dataZoom","title"]),brushStyle:{borderWidth:0,color:"rgba(210,219,238,0.2)"}};return r},e}(pt),fc={zoom:function(){var a=!this._isZoomActive;this.api.dispatchAction({type:"takeGlobalCursor",key:"dataZoomSelect",dataZoomSelectActive:a})},back:function(){this._dispatchZoomAction(ic(this.ecModel))}};function Wa(a){var e={xAxisIndex:a.get("xAxisIndex",!0),yAxisIndex:a.get("yAxisIndex",!0),xAxisId:a.get("xAxisId",!0),yAxisId:a.get("yAxisId",!0)};return e.xAxisIndex==null&&e.xAxisId==null&&(e.xAxisIndex="all"),e.yAxisIndex==null&&e.yAxisId==null&&(e.yAxisIndex="all"),e}function pc(a,e){a.setIconStatus("back",oc(e)>1?"emphasis":"normal")}function dc(a,e,t,r,i){var n=t._isZoomActive;r&&r.type==="takeGlobalCursor"&&(n=r.key==="dataZoomSelect"?r.dataZoomSelectActive:!1),t._isZoomActive=n,a.setIconStatus("zoom",n?"emphasis":"normal");var o=new ys(Wa(a),e,{include:["grid"]}),s=o.makePanelOpts(i,function(l){return l.xAxisDeclared&&!l.yAxisDeclared?"lineX":!l.xAxisDeclared&&l.yAxisDeclared?"lineY":"rect"});t._brushController.setPanels(s).enableBrush(n&&s.length?{brushType:"auto",brushStyle:a.getModel("brushStyle").getItemStyle()}:!1)}wl("dataZoom",function(a){var e=a.getComponent("toolbox",0),t=["feature","dataZoom"];if(!e||e.get(t)==null)return;var r=e.getModel(t),i=[],n=Wa(r),o=mo(a,n);fa(o.xAxisModels,function(l){return s(l,"xAxis","xAxisIndex")}),fa(o.yAxisModels,function(l){return s(l,"yAxis","yAxisIndex")});function s(l,u,h){var v=l.componentIndex,c={type:"select",$fromToolbox:!0,filterMode:r.get("filterMode",!0)||"filter",id:vc+u+v};c[h]=v,i.push(c)}return i});function Bf(a){a.registerComponentModel(Fv),a.registerComponentView(Wv),le("saveAsImage",Zv),le("magicType",Xv),le("dataView",ec),le("dataZoom",cc),le("restore",sc),kt(Gv)}var wn=w;function Mn(a){if(a){for(var e in a)if(a.hasOwnProperty(e))return!0}}function Cn(a,e,t){var r={};return wn(e,function(n){var o=r[n]=i();wn(a[n],function(s,l){if(J.isValidType(l)){var u={type:l,visual:s};t&&t(u,n),o[l]=new J(u),l==="opacity"&&(u=U(u),u.type="colorAlpha",o.__hidden.__alphaForOpacity=new J(u))}})}),r;function i(){var n=function(){};n.prototype.__hidden=n.prototype;var o=new n;return o}}function gc(a,e,t){var r;w(t,function(i){e.hasOwnProperty(i)&&Mn(e[i])&&(r=!0)}),r&&w(t,function(i){e.hasOwnProperty(i)&&Mn(e[i])?a[i]=U(e[i]):delete a[i]})}function Nf(a,e,t,r,i,n){var o={};w(a,function(v){var c=J.prepareVisualTypes(e[v]);o[v]=c});var s;function l(v){return xo(t,s,v)}function u(v,c){So(t,s,v,c)}t.each(h);function h(v,c){s=v;var f=t.getRawDataItem(s);if(!(f&&f.visualMap===!1))for(var p=r.call(i,v),d=e[p],g=o[p],y=0,m=g.length;y<m;y++){var S=g[y];d[S]&&d[S].applyVisual(v,l,u)}}}function yc(a,e,t,r){var i={};return w(a,function(n){var o=J.prepareVisualTypes(e[n]);i[n]=o}),{progress:function(o,s){var l;r!=null&&(l=s.getDimensionIndex(r));function u(b){return xo(s,v,b)}function h(b,_){So(s,v,b,_)}for(var v,c=s.getStore();(v=o.next())!=null;){var f=s.getRawDataItem(v);if(!(f&&f.visualMap===!1))for(var p=r!=null?c.get(l,v):v,d=t(p),g=e[d],y=i[d],m=0,S=y.length;m<S;m++){var x=y[m];g[x]&&g[x].applyVisual(p,u,h)}}}}}var Dn=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.layoutMode="box",t}return e.prototype.init=function(t,r,i){this.mergeDefaultAndTheme(t,i),this._initData()},e.prototype.mergeOption=function(t){a.prototype.mergeOption.apply(this,arguments),this._initData()},e.prototype.setCurrentIndex=function(t){t==null&&(t=this.option.currentIndex);var r=this._data.count();this.option.loop?t=(t%r+r)%r:(t>=r&&(t=r-1),t<0&&(t=0)),this.option.currentIndex=t},e.prototype.getCurrentIndex=function(){return this.option.currentIndex},e.prototype.isIndexMax=function(){return this.getCurrentIndex()>=this._data.count()-1},e.prototype.setPlayState=function(t){this.option.autoPlay=!!t},e.prototype.getPlayState=function(){return!!this.option.autoPlay},e.prototype._initData=function(){var t=this.option,r=t.data||[],i=t.axisType,n=this._names=[],o;i==="category"?(o=[],w(r,function(u,h){var v=ve(to(u),""),c;st(u)?(c=U(u),c.value=h):c=h,o.push(c),n.push(v)})):o=r;var s={category:"ordinal",time:"time",value:"number"}[i]||"number",l=this._data=new Bt([{name:"value",type:s}],this);l.initData(o,n)},e.prototype.getData=function(){return this._data},e.prototype.getCategories=function(){if(this.get("axisType")==="category")return this._names.slice()},e.type="timeline",e.defaultOption={z:4,show:!0,axisType:"time",realtime:!0,left:"20%",top:null,right:"20%",bottom:0,width:null,height:40,padding:5,controlPosition:"left",autoPlay:!1,rewind:!1,loop:!0,playInterval:2e3,currentIndex:0,itemStyle:{},label:{color:"#000"},data:[]},e}(Mt),ms=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="timeline.slider",e.defaultOption=ie(Dn.defaultOption,{backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",borderWidth:0,orient:"horizontal",inverse:!1,tooltip:{trigger:"item"},symbol:"circle",symbolSize:12,lineStyle:{show:!0,width:2,color:"#DAE1F5"},label:{position:"auto",show:!0,interval:"auto",rotate:0,color:"#A4B1D7"},itemStyle:{color:"#A4B1D7",borderWidth:1},checkpointStyle:{symbol:"circle",symbolSize:15,color:"#316bf3",borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0, 0, 0, 0.3)",animation:!0,animationDuration:300,animationEasing:"quinticInOut"},controlStyle:{show:!0,showPlayBtn:!0,showPrevBtn:!0,showNextBtn:!0,itemSize:24,itemGap:12,position:"left",playIcon:"path://M31.6,53C17.5,53,6,41.5,6,27.4S17.5,1.8,31.6,1.8C45.7,1.8,57.2,13.3,57.2,27.4S45.7,53,31.6,53z M31.6,3.3 C18.4,3.3,7.5,14.1,7.5,27.4c0,13.3,10.8,24.1,24.1,24.1C44.9,51.5,55.7,40.7,55.7,27.4C55.7,14.1,44.9,3.3,31.6,3.3z M24.9,21.3 c0-2.2,1.6-3.1,3.5-2l10.5,6.1c1.899,1.1,1.899,2.9,0,4l-10.5,6.1c-1.9,1.1-3.5,0.2-3.5-2V21.3z",stopIcon:"path://M30.9,53.2C16.8,53.2,5.3,41.7,5.3,27.6S16.8,2,30.9,2C45,2,56.4,13.5,56.4,27.6S45,53.2,30.9,53.2z M30.9,3.5C17.6,3.5,6.8,14.4,6.8,27.6c0,13.3,10.8,24.1,24.101,24.1C44.2,51.7,55,40.9,55,27.6C54.9,14.4,44.1,3.5,30.9,3.5z M36.9,35.8c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H36c0.5,0,0.9,0.4,0.9,1V35.8z M27.8,35.8 c0,0.601-0.4,1-0.9,1h-1.3c-0.5,0-0.9-0.399-0.9-1V19.5c0-0.6,0.4-1,0.9-1H27c0.5,0,0.9,0.4,0.9,1L27.8,35.8L27.8,35.8z",nextIcon:"M2,18.5A1.52,1.52,0,0,1,.92,18a1.49,1.49,0,0,1,0-2.12L7.81,9.36,1,3.11A1.5,1.5,0,1,1,3,.89l8,7.34a1.48,1.48,0,0,1,.49,1.09,1.51,1.51,0,0,1-.46,1.1L3,18.08A1.5,1.5,0,0,1,2,18.5Z",prevIcon:"M10,.5A1.52,1.52,0,0,1,11.08,1a1.49,1.49,0,0,1,0,2.12L4.19,9.64,11,15.89a1.5,1.5,0,1,1-2,2.22L1,10.77A1.48,1.48,0,0,1,.5,9.68,1.51,1.51,0,0,1,1,8.58L9,.92A1.5,1.5,0,0,1,10,.5Z",prevBtnSize:18,nextBtnSize:18,color:"#A4B1D7",borderColor:"#A4B1D7",borderWidth:1},emphasis:{label:{show:!0,color:"#6f778d"},itemStyle:{color:"#316BF3"},controlStyle:{color:"#316BF3",borderColor:"#316BF3",borderWidth:2}},progress:{lineStyle:{color:"#316BF3"},itemStyle:{color:"#316BF3"},label:{color:"#6f778d"}},data:[]}),e}(Dn);ir(ms,Aa.prototype);var mc=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="timeline",e}(Et),xc=function(a){I(e,a);function e(t,r,i,n){var o=a.call(this,t,r,i)||this;return o.type=n||"value",o}return e.prototype.getLabelModel=function(){return this.model.getModel("label")},e.prototype.isHorizontal=function(){return this.model.get("orient")==="horizontal"},e}(Lo),zr=Math.PI,Tn=ft(),Sc=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,r){this.api=r},e.prototype.render=function(t,r,i){if(this.model=t,this.api=i,this.ecModel=r,this.group.removeAll(),t.get("show",!0)){var n=this._layout(t,i),o=this._createGroup("_mainGroup"),s=this._createGroup("_labelGroup"),l=this._axis=this._createAxis(n,t);t.formatTooltip=function(u){var h=l.scale.getLabel({value:u});return $r("nameValue",{noName:!0,value:h})},w(["AxisLine","AxisTick","Control","CurrentPointer"],function(u){this["_render"+u](n,o,l,t)},this),this._renderAxisLabel(n,s,l,t),this._position(n,t)}this._doPlayStop(),this._updateTicksStatus()},e.prototype.remove=function(){this._clearTimer(),this.group.removeAll()},e.prototype.dispose=function(){this._clearTimer()},e.prototype._layout=function(t,r){var i=t.get(["label","position"]),n=t.get("orient"),o=_c(t,r),s;i==null||i==="auto"?s=n==="horizontal"?o.y+o.height/2<r.getHeight()/2?"-":"+":o.x+o.width/2<r.getWidth()/2?"+":"-":it(i)?s={horizontal:{top:"-",bottom:"+"},vertical:{left:"-",right:"+"}}[n][i]:s=i;var l={horizontal:"center",vertical:s>=0||s==="+"?"left":"right"},u={horizontal:s>=0||s==="+"?"top":"bottom",vertical:"middle"},h={horizontal:0,vertical:zr/2},v=n==="vertical"?o.height:o.width,c=t.getModel("controlStyle"),f=c.get("show",!0),p=f?c.get("itemSize"):0,d=f?c.get("itemGap"):0,g=p+d,y=t.get(["label","rotate"])||0;y=y*zr/180;var m,S,x,b=c.get("position",!0),_=f&&c.get("showPlayBtn",!0),A=f&&c.get("showPrevBtn",!0),D=f&&c.get("showNextBtn",!0),M=0,T=v;b==="left"||b==="bottom"?(_&&(m=[0,0],M+=g),A&&(S=[M,0],M+=g),D&&(x=[T-p,0],T-=g)):(_&&(m=[T-p,0],T-=g),A&&(S=[0,0],M+=g),D&&(x=[T-p,0],T-=g));var C=[M,T];return t.get("inverse")&&C.reverse(),{viewRect:o,mainLength:v,orient:n,rotation:h[n],labelRotation:y,labelPosOpt:s,labelAlign:t.get(["label","align"])||l[n],labelBaseline:t.get(["label","verticalAlign"])||t.get(["label","baseline"])||u[n],playPosition:m,prevBtnPosition:S,nextBtnPosition:x,axisExtent:C,controlSize:p,controlGap:d}},e.prototype._position=function(t,r){var i=this._mainGroup,n=this._labelGroup,o=t.viewRect;if(t.orient==="vertical"){var s=Dl(),l=o.x,u=o.y+o.height;li(s,s,[-l,-u]),Cl(s,s,-zr/2),li(s,s,[l,u]),o=o.clone(),o.applyTransform(s)}var h=m(o),v=m(i.getBoundingRect()),c=m(n.getBoundingRect()),f=[i.x,i.y],p=[n.x,n.y];p[0]=f[0]=h[0][0];var d=t.labelPosOpt;if(d==null||it(d)){var g=d==="+"?0:1;S(f,v,h,1,g),S(p,c,h,1,1-g)}else{var g=d>=0?0:1;S(f,v,h,1,g),p[1]=f[1]+d}i.setPosition(f),n.setPosition(p),i.rotation=n.rotation=t.rotation,y(i),y(n);function y(x){x.originX=h[0][0]-x.x,x.originY=h[1][0]-x.y}function m(x){return[[x.x,x.x+x.width],[x.y,x.y+x.height]]}function S(x,b,_,A,D){x[A]+=_[A][D]-b[A][D]}},e.prototype._createAxis=function(t,r){var i=r.getData(),n=r.get("axisType"),o=bc(r,n);o.getTicks=function(){return i.mapArray(["value"],function(u){return{value:u}})};var s=i.getDataExtent("value");o.setExtent(s[0],s[1]),o.calcNiceTicks();var l=new xc("value",o,t.axisExtent,n);return l.model=r,l},e.prototype._createGroup=function(t){var r=this[t]=new $;return this.group.add(r),r},e.prototype._renderAxisLine=function(t,r,i,n){var o=i.getExtent();if(n.get(["lineStyle","show"])){var s=new ge({shape:{x1:o[0],y1:0,x2:o[1],y2:0},style:F({lineCap:"round"},n.getModel("lineStyle").getLineStyle()),silent:!0,z2:1});r.add(s);var l=this._progressLine=new ge({shape:{x1:o[0],x2:this._currentPointer?this._currentPointer.x:o[0],y1:0,y2:0},style:ht({lineCap:"round",lineWidth:s.style.lineWidth},n.getModel(["progress","lineStyle"]).getLineStyle()),silent:!0,z2:1});r.add(l)}},e.prototype._renderAxisTick=function(t,r,i,n){var o=this,s=n.getData(),l=i.scale.getTicks();this._tickSymbols=[],w(l,function(u){var h=i.dataToCoord(u.value),v=s.getItemModel(u.value),c=v.getModel("itemStyle"),f=v.getModel(["emphasis","itemStyle"]),p=v.getModel(["progress","itemStyle"]),d={x:h,y:0,onclick:R(o._changeTimeline,o,u.value)},g=Ln(v,c,r,d);g.ensureState("emphasis").style=f.getItemStyle(),g.ensureState("progress").style=p.getItemStyle(),Be(g);var y=at(g);v.get("tooltip")?(y.dataIndex=u.value,y.dataModel=n):y.dataIndex=y.dataModel=null,o._tickSymbols.push(g)})},e.prototype._renderAxisLabel=function(t,r,i,n){var o=this,s=i.getLabelModel();if(s.get("show")){var l=n.getData(),u=i.getViewLabels();this._tickLabels=[],w(u,function(h){var v=h.tickValue,c=l.getItemModel(v),f=c.getModel("label"),p=c.getModel(["emphasis","label"]),d=c.getModel(["progress","label"]),g=i.dataToCoord(h.tickValue),y=new tt({x:g,y:0,rotation:t.labelRotation-t.rotation,onclick:R(o._changeTimeline,o,v),silent:!1,style:gt(f,{text:h.formattedLabel,align:t.labelAlign,verticalAlign:t.labelBaseline})});y.ensureState("emphasis").style=gt(p),y.ensureState("progress").style=gt(d),r.add(y),Be(y),Tn(y).dataIndex=v,o._tickLabels.push(y)})}},e.prototype._renderControl=function(t,r,i,n){var o=t.controlSize,s=t.rotation,l=n.getModel("controlStyle").getItemStyle(),u=n.getModel(["emphasis","controlStyle"]).getItemStyle(),h=n.getPlayState(),v=n.get("inverse",!0);c(t.nextBtnPosition,"next",R(this._changeTimeline,this,v?"-":"+")),c(t.prevBtnPosition,"prev",R(this._changeTimeline,this,v?"+":"-")),c(t.playPosition,h?"stop":"play",R(this._handlePlayClick,this,!h),!0);function c(f,p,d,g){if(f){var y=Kr(Lt(n.get(["controlStyle",p+"BtnSize"]),o),o),m=[0,-y/2,y,y],S=Ac(n,p+"Icon",m,{x:f[0],y:f[1],originX:o/2,originY:0,rotation:g?-s:0,rectHover:!0,style:l,onclick:d});S.ensureState("emphasis").style=u,r.add(S),Be(S)}}},e.prototype._renderCurrentPointer=function(t,r,i,n){var o=n.getData(),s=n.getCurrentIndex(),l=o.getItemModel(s).getModel("checkpointStyle"),u=this,h={onCreate:function(v){v.draggable=!0,v.drift=R(u._handlePointerDrag,u),v.ondragend=R(u._handlePointerDragend,u),In(v,u._progressLine,s,i,n,!0)},onUpdate:function(v){In(v,u._progressLine,s,i,n)}};this._currentPointer=Ln(l,l,this._mainGroup,{},this._currentPointer,h)},e.prototype._handlePlayClick=function(t){this._clearTimer(),this.api.dispatchAction({type:"timelinePlayChange",playState:t,from:this.uid})},e.prototype._handlePointerDrag=function(t,r,i){this._clearTimer(),this._pointerChangeTimeline([i.offsetX,i.offsetY])},e.prototype._handlePointerDragend=function(t){this._pointerChangeTimeline([t.offsetX,t.offsetY],!0)},e.prototype._pointerChangeTimeline=function(t,r){var i=this._toAxisCoord(t)[0],n=this._axis,o=bt(n.getExtent().slice());i>o[1]&&(i=o[1]),i<o[0]&&(i=o[0]),this._currentPointer.x=i,this._currentPointer.markRedraw();var s=this._progressLine;s&&(s.shape.x2=i,s.dirty());var l=this._findNearestTick(i),u=this.model;(r||l!==u.getCurrentIndex()&&u.get("realtime"))&&this._changeTimeline(l)},e.prototype._doPlayStop=function(){var t=this;this._clearTimer(),this.model.getPlayState()&&(this._timer=setTimeout(function(){var r=t.model;t._changeTimeline(r.getCurrentIndex()+(r.get("rewind",!0)?-1:1))},this.model.get("playInterval")))},e.prototype._toAxisCoord=function(t){var r=this._mainGroup.getLocalTransform();return te(t,r,!0)},e.prototype._findNearestTick=function(t){var r=this.model.getData(),i=1/0,n,o=this._axis;return r.each(["value"],function(s,l){var u=o.dataToCoord(s),h=Math.abs(u-t);h<i&&(i=h,n=l)}),n},e.prototype._clearTimer=function(){this._timer&&(clearTimeout(this._timer),this._timer=null)},e.prototype._changeTimeline=function(t){var r=this.model.getCurrentIndex();t==="+"?t=r+1:t==="-"&&(t=r-1),this.api.dispatchAction({type:"timelineChange",currentIndex:t,from:this.uid})},e.prototype._updateTicksStatus=function(){var t=this.model.getCurrentIndex(),r=this._tickSymbols,i=this._tickLabels;if(r)for(var n=0;n<r.length;n++)r&&r[n]&&r[n].toggleState("progress",n<t);if(i)for(var n=0;n<i.length;n++)i&&i[n]&&i[n].toggleState("progress",Tn(i[n]).dataIndex<=t)},e.type="timeline.slider",e}(mc);function bc(a,e){if(e=e||a.get("type"),e)switch(e){case"category":return new Ll({ordinalMeta:a.getCategories(),extent:[1/0,-1/0]});case"time":return new Tl({locale:a.ecModel.getLocaleModel(),useUTC:a.ecModel.get("useUTC")});default:return new oo}}function _c(a,e){return Ce(a.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()},a.get("padding"))}function Ac(a,e,t,r){var i=r.style,n=go(a.get(["controlStyle",e]),r||{},new de(t[0],t[1],t[2],t[3]));return i&&n.setStyle(i),n}function Ln(a,e,t,r,i,n){var o=e.get("color");if(i)i.setColor(o),t.add(i),n&&n.onUpdate(i);else{var s=a.get("symbol");i=yt(s,-1,-1,2,2,o),i.setStyle("strokeNoScale",!0),t.add(i),n&&n.onCreate(i)}var l=e.getItemStyle(["color"]);i.setStyle(l),r=Z({rectHover:!0,z2:100},r,!0);var u=er(a.get("symbolSize"));r.scaleX=u[0]/2,r.scaleY=u[1]/2;var h=tr(a.get("symbolOffset"),u);h&&(r.x=(r.x||0)+h[0],r.y=(r.y||0)+h[1]);var v=a.get("symbolRotate");return r.rotation=(v||0)*Math.PI/180||0,i.attr(r),i.updateTransform(),i}function In(a,e,t,r,i,n){if(!a.dragging){var o=i.getModel("checkpointStyle"),s=r.dataToCoord(i.getData().get("value",t));if(n||!o.get("animation",!0))a.attr({x:s,y:0}),e&&e.attr({shape:{x2:s}});else{var l={duration:o.get("animationDuration",!0),easing:o.get("animationEasing",!0)};a.stopAnimation(null,!0),a.animateTo({x:s,y:0},l),e&&e.animateTo({shape:{x2:s}},l)}}}function wc(a){a.registerAction({type:"timelineChange",event:"timelineChanged",update:"prepareAndUpdate"},function(e,t,r){var i=t.getComponent("timeline");return i&&e.currentIndex!=null&&(i.setCurrentIndex(e.currentIndex),!i.get("loop",!0)&&i.isIndexMax()&&i.getPlayState()&&(i.setPlayState(!1),r.dispatchAction({type:"timelinePlayChange",playState:!1,from:e.from}))),t.resetOption("timeline",{replaceMerge:i.get("replaceMerge",!0)}),ht({currentIndex:i.option.currentIndex},e)}),a.registerAction({type:"timelinePlayChange",event:"timelinePlayChanged",update:"update"},function(e,t){var r=t.getComponent("timeline");r&&e.playState!=null&&r.setPlayState(e.playState)})}function Mc(a){var e=a&&a.timeline;O(e)||(e=e?[e]:[]),w(e,function(t){t&&Cc(t)})}function Cc(a){var e=a.type,t={number:"value",time:"time"};if(t[e]&&(a.axisType=t[e],delete a.type),kn(a),Vt(a,"controlPosition")){var r=a.controlStyle||(a.controlStyle={});Vt(r,"position")||(r.position=a.controlPosition),r.position==="none"&&!Vt(r,"show")&&(r.show=!1,delete r.position),delete a.controlPosition}w(a.data||[],function(i){st(i)&&!O(i)&&(!Vt(i,"value")&&Vt(i,"name")&&(i.value=i.name),kn(i))})}function kn(a){var e=a.itemStyle||(a.itemStyle={}),t=e.emphasis||(e.emphasis={}),r=a.label||a.label||{},i=r.normal||(r.normal={}),n={normal:1,emphasis:1};w(r,function(o,s){!n[s]&&!Vt(i,s)&&(i[s]=o)}),t.label&&!Vt(r,"emphasis")&&(r.emphasis=t.label,delete t.label)}function Vt(a,e){return a.hasOwnProperty(e)}function Gf(a){a.registerComponentModel(ms),a.registerComponentView(Sc),a.registerSubTypeDefaulter("timeline",function(){return"slider"}),wc(a),a.registerPreprocessor(Mc)}function xs(a,e){if(!a)return!1;for(var t=O(a)?a:[a],r=0;r<t.length;r++)if(t[r]&&t[r][e])return!0;return!1}function Re(a){Il(a,"label",["show"])}var Ee=ft(),Ut=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.createdBySelf=!1,t}return e.prototype.init=function(t,r,i){this.mergeDefaultAndTheme(t,i),this._mergeOption(t,i,!1,!0)},e.prototype.isAnimationEnabled=function(){if(yo.node)return!1;var t=this.__hostSeries;return this.getShallow("animation")&&t&&t.isAnimationEnabled()},e.prototype.mergeOption=function(t,r){this._mergeOption(t,r,!1,!1)},e.prototype._mergeOption=function(t,r,i,n){var o=this.mainType;i||r.eachSeries(function(s){var l=s.get(this.mainType,!0),u=Ee(s)[o];if(!l||!l.data){Ee(s)[o]=null;return}u?u._mergeOption(l,r,!0):(n&&Re(l),w(l.data,function(h){h instanceof Array?(Re(h[0]),Re(h[1])):Re(h)}),u=this.createMarkerModelFromSeries(l,this,r),F(u,{mainType:this.mainType,seriesIndex:s.seriesIndex,name:s.name,createdBySelf:!0}),u.__hostSeries=s),Ee(s)[o]=u},this)},e.prototype.formatTooltip=function(t,r,i){var n=this.getData(),o=this.getRawValue(t),s=n.getName(t);return $r("section",{header:this.name,blocks:[$r("nameValue",{name:s,value:o,noName:!s,noValue:o==null})]})},e.prototype.getData=function(){return this._data},e.prototype.setData=function(t){this._data=t},e.prototype.getDataParams=function(t,r){var i=Aa.prototype.getDataParams.call(this,t,r),n=this.__hostSeries;return n&&(i.seriesId=n.id,i.seriesName=n.name,i.seriesType=n.subType),i},e.getMarkerModelFromSeries=function(t,r){return Ee(t)[r]},e.type="marker",e.dependencies=["series","grid","polar","geo"],e}(Mt);ir(Ut,Aa.prototype);function pa(a){return!(isNaN(parseFloat(a.x))&&isNaN(parseFloat(a.y)))}function Dc(a){return!isNaN(parseFloat(a.x))&&!isNaN(parseFloat(a.y))}function Oe(a,e,t,r,i,n){var o=[],s=fe(e,r),l=s?e.getCalculationInfo("stackResultDimension"):r,u=Ha(e,l,a),h=e.indicesOfNearest(l,u)[0];o[i]=e.get(t,h),o[n]=e.get(l,h);var v=e.get(r,h),c=kl(e.get(r,h));return c=Math.min(c,20),c>=0&&(o[n]=+o[n].toFixed(c)),[o,v]}var Br={min:G(Oe,"min"),max:G(Oe,"max"),average:G(Oe,"average"),median:G(Oe,"median")};function Ke(a,e){if(e){var t=a.getData(),r=a.coordinateSystem,i=r&&r.dimensions;if(!Dc(e)&&!O(e.coord)&&O(i)){var n=Ss(e,t,r,a);if(e=U(e),e.type&&Br[e.type]&&n.baseAxis&&n.valueAxis){var o=et(i,n.baseAxis.dim),s=et(i,n.valueAxis.dim),l=Br[e.type](t,n.baseDataDim,n.valueDataDim,o,s);e.coord=l[0],e.value=l[1]}else e.coord=[e.xAxis!=null?e.xAxis:e.radiusAxis,e.yAxis!=null?e.yAxis:e.angleAxis]}if(e.coord==null||!O(i))e.coord=[];else for(var u=e.coord,h=0;h<2;h++)Br[u[h]]&&(u[h]=Ha(t,t.mapDimension(i[h]),u[h]));return e}}function Ss(a,e,t,r){var i={};return a.valueIndex!=null||a.valueDim!=null?(i.valueDataDim=a.valueIndex!=null?e.getDimension(a.valueIndex):a.valueDim,i.valueAxis=t.getAxis(Tc(r,i.valueDataDim)),i.baseAxis=t.getOtherAxis(i.valueAxis),i.baseDataDim=e.mapDimension(i.baseAxis.dim)):(i.baseAxis=r.getBaseAxis(),i.valueAxis=t.getOtherAxis(i.baseAxis),i.baseDataDim=e.mapDimension(i.baseAxis.dim),i.valueDataDim=e.mapDimension(i.valueAxis.dim)),i}function Tc(a,e){var t=a.getData().getDimensionInfo(e);return t&&t.coordDim}function je(a,e){return a&&a.containData&&e.coord&&!pa(e)?a.containData(e.coord):!0}function Lc(a,e,t){return a&&a.containZone&&e.coord&&t.coord&&!pa(e)&&!pa(t)?a.containZone(e.coord,t.coord):!0}function Ic(a,e){return a?function(t,r,i,n){var o=n<2?t.coord&&t.coord[n]:t.value;return Ue(o,e[n])}:function(t,r,i,n){return Ue(t.value,e[n])}}function Ha(a,e,t){if(t==="average"){var r=0,i=0;return a.each(e,function(n,o){isNaN(n)||(r+=n,i++)}),r/i}else return t==="median"?a.getMedian(e):a.getDataExtent(e)[t==="max"?1:0]}var Nr=ft(),bs=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this.markerGroupMap=ot()},e.prototype.render=function(t,r,i){var n=this,o=this.markerGroupMap;o.each(function(s){Nr(s).keep=!1}),r.eachSeries(function(s){var l=Ut.getMarkerModelFromSeries(s,n.type);l&&n.renderSeries(s,l,r,i)}),o.each(function(s){!Nr(s).keep&&n.group.remove(s.group)})},e.prototype.markKeep=function(t){Nr(t).keep=!0},e.prototype.toggleBlurSeries=function(t,r){var i=this;w(t,function(n){var o=Ut.getMarkerModelFromSeries(n,i.type);if(o){var s=o.getData();s.eachItemGraphicEl(function(l){l&&(r?Pl(l):Rl(l))})}})},e.type="marker",e}(Et),kc=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,i){return new e(t,r,i)},e.type="markLine",e.defaultOption={z:5,symbol:["circle","arrow"],symbolSize:[8,16],symbolOffset:0,precision:2,tooltip:{trigger:"item"},label:{show:!0,position:"end",distance:5},lineStyle:{type:"dashed"},emphasis:{label:{show:!0},lineStyle:{width:3}},animationEasing:"linear"},e}(Ut),Ve=ft(),Pc=function(a,e,t,r){var i=a.getData(),n;if(O(r))n=r;else{var o=r.type;if(o==="min"||o==="max"||o==="average"||o==="median"||r.xAxis!=null||r.yAxis!=null){var s=void 0,l=void 0;if(r.yAxis!=null||r.xAxis!=null)s=e.getAxis(r.yAxis!=null?"y":"x"),l=qt(r.yAxis,r.xAxis);else{var u=Ss(r,i,e,a);s=u.valueAxis;var h=El(i,u.valueDataDim);l=Ha(i,h,o)}var v=s.dim==="x"?0:1,c=1-v,f=U(r),p={coord:[]};f.type=null,f.coord=[],f.coord[c]=-1/0,p.coord[c]=1/0;var d=t.get("precision");d>=0&&ar(l)&&(l=+l.toFixed(Math.min(d,20))),f.coord[v]=p.coord[v]=l,n=[f,p,{type:o,valueIndex:r.valueIndex,value:l}]}else n=[]}var g=[Ke(a,n[0]),Ke(a,n[1]),F({},n[2])];return g[2].type=g[2].type||null,Z(g[2],g[0]),Z(g[2],g[1]),g};function qe(a){return!isNaN(a)&&!isFinite(a)}function Pn(a,e,t,r){var i=1-a,n=r.dimensions[a];return qe(e[i])&&qe(t[i])&&e[a]===t[a]&&r.getAxis(n).containData(e[a])}function Rc(a,e){if(a.type==="cartesian2d"){var t=e[0].coord,r=e[1].coord;if(t&&r&&(Pn(1,t,r,a)||Pn(0,t,r,a)))return!0}return je(a,e[0])&&je(a,e[1])}function Gr(a,e,t,r,i){var n=r.coordinateSystem,o=a.getItemModel(e),s,l=ct(o.get("x"),i.getWidth()),u=ct(o.get("y"),i.getHeight());if(!isNaN(l)&&!isNaN(u))s=[l,u];else{if(r.getMarkerPosition)s=r.getMarkerPosition(a.getValues(a.dimensions,e));else{var h=n.dimensions,v=a.get(h[0],e),c=a.get(h[1],e);s=n.dataToPoint([v,c])}if(ne(n,"cartesian2d")){var f=n.getAxis("x"),p=n.getAxis("y"),h=n.dimensions;qe(a.get(h[0],e))?s[0]=f.toGlobalCoord(f.getExtent()[t?0:1]):qe(a.get(h[1],e))&&(s[1]=p.toGlobalCoord(p.getExtent()[t?0:1]))}isNaN(l)||(s[0]=l),isNaN(u)||(s[1]=u)}a.setItemLayout(e,s)}var Ec=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,i){r.eachSeries(function(n){var o=Ut.getMarkerModelFromSeries(n,"markLine");if(o){var s=o.getData(),l=Ve(o).from,u=Ve(o).to;l.each(function(h){Gr(l,h,!0,n,i),Gr(u,h,!1,n,i)}),s.each(function(h){s.setItemLayout(h,[l.getItemLayout(h),u.getItemLayout(h)])}),this.markerGroupMap.get(n.id).updateLayout()}},this)},e.prototype.renderSeries=function(t,r,i,n){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,h=u.get(s)||u.set(s,new Ah);this.group.add(h.group);var v=Oc(o,t,r),c=v.from,f=v.to,p=v.line;Ve(r).from=c,Ve(r).to=f,r.setData(p);var d=r.get("symbol"),g=r.get("symbolSize"),y=r.get("symbolRotate"),m=r.get("symbolOffset");O(d)||(d=[d,d]),O(g)||(g=[g,g]),O(y)||(y=[y,y]),O(m)||(m=[m,m]),v.from.each(function(x){S(c,x,!0),S(f,x,!1)}),p.each(function(x){var b=p.getItemModel(x).getModel("lineStyle").getLineStyle();p.setItemLayout(x,[c.getItemLayout(x),f.getItemLayout(x)]),b.stroke==null&&(b.stroke=c.getItemVisual(x,"style").fill),p.setItemVisual(x,{fromSymbolKeepAspect:c.getItemVisual(x,"symbolKeepAspect"),fromSymbolOffset:c.getItemVisual(x,"symbolOffset"),fromSymbolRotate:c.getItemVisual(x,"symbolRotate"),fromSymbolSize:c.getItemVisual(x,"symbolSize"),fromSymbol:c.getItemVisual(x,"symbol"),toSymbolKeepAspect:f.getItemVisual(x,"symbolKeepAspect"),toSymbolOffset:f.getItemVisual(x,"symbolOffset"),toSymbolRotate:f.getItemVisual(x,"symbolRotate"),toSymbolSize:f.getItemVisual(x,"symbolSize"),toSymbol:f.getItemVisual(x,"symbol"),style:b})}),h.updateData(p),v.line.eachItemGraphicEl(function(x){at(x).dataModel=r,x.traverse(function(b){at(b).dataModel=r})});function S(x,b,_){var A=x.getItemModel(b);Gr(x,b,_,t,n);var D=A.getModel("itemStyle").getItemStyle();D.fill==null&&(D.fill=wa(l,"color")),x.setItemVisual(b,{symbolKeepAspect:A.get("symbolKeepAspect"),symbolOffset:Lt(A.get("symbolOffset",!0),m[_?0:1]),symbolRotate:Lt(A.get("symbolRotate",!0),y[_?0:1]),symbolSize:Lt(A.get("symbolSize"),g[_?0:1]),symbol:Lt(A.get("symbol",!0),d[_?0:1]),style:D})}this.markKeep(h),h.group.silent=r.get("silent")||t.get("silent")},e.type="markLine",e}(bs);function Oc(a,e,t){var r;a?r=E(a&&a.dimensions,function(u){var h=e.getData().getDimensionInfo(e.getData().mapDimension(u))||{};return F(F({},h),{name:u,ordinalMeta:null})}):r=[{name:"value",type:"float"}];var i=new Bt(r,t),n=new Bt(r,t),o=new Bt([],t),s=E(t.get("data"),G(Pc,e,a,t));a&&(s=Pt(s,G(Rc,a)));var l=Ic(!!a,r);return i.initData(E(s,function(u){return u[0]}),null,l),n.initData(E(s,function(u){return u[1]}),null,l),o.initData(E(s,function(u){return u[2]})),o.hasItemOption=!0,{from:i,to:n,line:o}}function Ff(a){a.registerComponentModel(kc),a.registerComponentView(Ec),a.registerPreprocessor(function(e){xs(e.series,"markLine")&&(e.markLine=e.markLine||{})})}var Vc=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,r,i){return new e(t,r,i)},e.type="markArea",e.defaultOption={z:1,tooltip:{trigger:"item"},animation:!1,label:{show:!0,position:"top"},itemStyle:{borderWidth:0},emphasis:{label:{show:!0,position:"top"}}},e}(Ut),ze=ft(),zc=function(a,e,t,r){var i=r[0],n=r[1];if(!(!i||!n)){var o=Ke(a,i),s=Ke(a,n),l=o.coord,u=s.coord;l[0]=qt(l[0],-1/0),l[1]=qt(l[1],-1/0),u[0]=qt(u[0],1/0),u[1]=qt(u[1],1/0);var h=Ol([{},o,s]);return h.coord=[o.coord,s.coord],h.x0=o.x,h.y0=o.y,h.x1=s.x,h.y1=s.y,h}};function Qe(a){return!isNaN(a)&&!isFinite(a)}function Rn(a,e,t,r){var i=1-a;return Qe(e[i])&&Qe(t[i])}function Bc(a,e){var t=e.coord[0],r=e.coord[1],i={coord:t,x:e.x0,y:e.y0},n={coord:r,x:e.x1,y:e.y1};return ne(a,"cartesian2d")?t&&r&&(Rn(1,t,r)||Rn(0,t,r))?!0:Lc(a,i,n):je(a,i)||je(a,n)}function En(a,e,t,r,i){var n=r.coordinateSystem,o=a.getItemModel(e),s,l=ct(o.get(t[0]),i.getWidth()),u=ct(o.get(t[1]),i.getHeight());if(!isNaN(l)&&!isNaN(u))s=[l,u];else{if(r.getMarkerPosition){var h=a.getValues(["x0","y0"],e),v=a.getValues(["x1","y1"],e),c=n.clampData(h),f=n.clampData(v),p=[];t[0]==="x0"?p[0]=c[0]>f[0]?v[0]:h[0]:p[0]=c[0]>f[0]?h[0]:v[0],t[1]==="y0"?p[1]=c[1]>f[1]?v[1]:h[1]:p[1]=c[1]>f[1]?h[1]:v[1],s=r.getMarkerPosition(p,t,!0)}else{var d=a.get(t[0],e),g=a.get(t[1],e),y=[d,g];n.clampData&&n.clampData(y,y),s=n.dataToPoint(y,!0)}if(ne(n,"cartesian2d")){var m=n.getAxis("x"),S=n.getAxis("y"),d=a.get(t[0],e),g=a.get(t[1],e);Qe(d)?s[0]=m.toGlobalCoord(m.getExtent()[t[0]==="x0"?0:1]):Qe(g)&&(s[1]=S.toGlobalCoord(S.getExtent()[t[1]==="y0"?0:1]))}isNaN(l)||(s[0]=l),isNaN(u)||(s[1]=u)}return s}var On=[["x0","y0"],["x1","y0"],["x1","y1"],["x0","y1"]],Nc=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,r,i){r.eachSeries(function(n){var o=Ut.getMarkerModelFromSeries(n,"markArea");if(o){var s=o.getData();s.each(function(l){var u=E(On,function(v){return En(s,l,v,n,i)});s.setItemLayout(l,u);var h=s.getItemGraphicEl(l);h.setShape("points",u)})}},this)},e.prototype.renderSeries=function(t,r,i,n){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,h=u.get(s)||u.set(s,{group:new $});this.group.add(h.group),this.markKeep(h);var v=Gc(o,t,r);r.setData(v),v.each(function(c){var f=E(On,function(D){return En(v,c,D,t,n)}),p=o.getAxis("x").scale,d=o.getAxis("y").scale,g=p.getExtent(),y=d.getExtent(),m=[p.parse(v.get("x0",c)),p.parse(v.get("x1",c))],S=[d.parse(v.get("y0",c)),d.parse(v.get("y1",c))];bt(m),bt(S);var x=!(g[0]>m[1]||g[1]<m[0]||y[0]>S[1]||y[1]<S[0]),b=!x;v.setItemLayout(c,{points:f,allClipped:b});var _=v.getItemModel(c).getModel("itemStyle").getItemStyle(),A=wa(l,"color");_.fill||(_.fill=A,it(_.fill)&&(_.fill=Yr(_.fill,.4))),_.stroke||(_.stroke=A),v.setItemVisual(c,"style",_)}),v.diff(ze(h).data).add(function(c){var f=v.getItemLayout(c);if(!f.allClipped){var p=new ye({shape:{points:f.points}});v.setItemGraphicEl(c,p),h.group.add(p)}}).update(function(c,f){var p=ze(h).data.getItemGraphicEl(f),d=v.getItemLayout(c);d.allClipped?p&&h.group.remove(p):(p?ut(p,{shape:{points:d.points}},r,c):p=new ye({shape:{points:d.points}}),v.setItemGraphicEl(c,p),h.group.add(p))}).remove(function(c){var f=ze(h).data.getItemGraphicEl(c);h.group.remove(f)}).execute(),v.eachItemGraphicEl(function(c,f){var p=v.getItemModel(f),d=v.getItemVisual(f,"style");c.useStyle(v.getItemVisual(f,"style")),Me(c,Xt(p),{labelFetcher:r,labelDataIndex:f,defaultText:v.getName(f)||"",inheritColor:it(d.fill)?Yr(d.fill,1):"#000"}),pe(c,p),Ft(c,null,null,p.get(["emphasis","disabled"])),at(c).dataModel=r}),ze(h).data=v,h.group.silent=r.get("silent")||t.get("silent")},e.type="markArea",e}(bs);function Gc(a,e,t){var r,i,n=["x0","y0","x1","y1"];if(a){var o=E(a&&a.dimensions,function(u){var h=e.getData(),v=h.getDimensionInfo(h.mapDimension(u))||{};return F(F({},v),{name:u,ordinalMeta:null})});i=E(n,function(u,h){return{name:u,type:o[h%2].type}}),r=new Bt(i,t)}else i=[{name:"value",type:"float"}],r=new Bt(i,t);var s=E(t.get("data"),G(zc,e,a,t));a&&(s=Pt(s,G(Bc,a)));var l=a?function(u,h,v,c){var f=u.coord[Math.floor(c/2)][c%2];return Ue(f,i[c])}:function(u,h,v,c){return Ue(u.value,i[c])};return r.initData(s,null,l),r.hasItemOption=!0,r}function Wf(a){a.registerComponentModel(Vc),a.registerComponentView(Nc),a.registerPreprocessor(function(e){xs(e.series,"markArea")&&(e.markArea=e.markArea||{})})}var Fc=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.inside",e.defaultOption=ie(_e.defaultOption,{disabled:!1,zoomLock:!1,zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!1,preventDefaultMouseMove:!0}),e}(_e),Za=ft();function Wc(a,e,t){Za(a).coordSysRecordMap.each(function(r){var i=r.dataZoomInfoMap.get(e.uid);i&&(i.getRange=t)})}function Hc(a,e){for(var t=Za(a).coordSysRecordMap,r=t.keys(),i=0;i<r.length;i++){var n=r[i],o=t.get(n),s=o.dataZoomInfoMap;if(s){var l=e.uid,u=s.get(l);u&&(s.removeKey(l),s.keys().length||_s(t,o))}}}function _s(a,e){if(e){a.removeKey(e.model.uid);var t=e.controller;t&&t.dispose()}}function Zc(a,e){var t={model:e,containsPoint:G(Xc,e),dispatchAction:G(Uc,a),dataZoomInfoMap:null,controller:null},r=t.controller=new fh(a.getZr());return w(["pan","zoom","scrollMove"],function(i){r.on(i,function(n){var o=[];t.dataZoomInfoMap.each(function(s){if(n.isAvailableBehavior(s.model.option)){var l=(s.getRange||{})[i],u=l&&l(s.dzReferCoordSysInfo,t.model.mainType,t.controller,n);!s.model.get("disabled",!0)&&u&&o.push({dataZoomId:s.model.id,start:u[0],end:u[1]})}}),o.length&&t.dispatchAction(o)})}),t}function Uc(a,e){a.isDisposed()||a.dispatchAction({type:"dataZoom",animation:{easing:"cubicOut",duration:100},batch:e})}function Xc(a,e,t,r){return a.coordinateSystem.containPoint([t,r])}function Yc(a){var e,t="type_",r={type_true:2,type_move:1,type_false:0,type_undefined:-1},i=!0;return a.each(function(n){var o=n.model,s=o.get("disabled",!0)?!1:o.get("zoomLock",!0)?"move":!0;r[t+s]>r[t+e]&&(e=s),i=i&&o.get("preventDefaultMouseMove",!0)}),{controlType:e,opt:{zoomOnMouseWheel:!0,moveOnMouseMove:!0,moveOnMouseWheel:!0,preventDefaultMouseMove:!!i}}}function $c(a){a.registerProcessor(a.PRIORITY.PROCESSOR.FILTER,function(e,t){var r=Za(t),i=r.coordSysRecordMap||(r.coordSysRecordMap=ot());i.each(function(n){n.dataZoomInfoMap=null}),e.eachComponent({mainType:"dataZoom",subType:"inside"},function(n){var o=cs(n);w(o.infoList,function(s){var l=s.model.uid,u=i.get(l)||i.set(l,Zc(t,s.model)),h=u.dataZoomInfoMap||(u.dataZoomInfoMap=ot());h.set(n.uid,{dzReferCoordSysInfo:s,model:n,getRange:null})})}),i.each(function(n){var o=n.controller,s,l=n.dataZoomInfoMap;if(l){var u=l.keys()[0];u!=null&&(s=l.get(u))}if(!s){_s(i,n);return}var h=Yc(l);o.enable(h.controlType,h.opt),o.setPointerChecker(n.containsPoint),bo(n,"dispatchAction",s.model.get("throttle",!0),"fixRate")})})}var Kc=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type="dataZoom.inside",t}return e.prototype.render=function(t,r,i){if(a.prototype.render.apply(this,arguments),t.noTarget()){this._clear();return}this.range=t.getPercentRange(),Wc(i,t,{pan:R(Fr.pan,this),zoom:R(Fr.zoom,this),scrollMove:R(Fr.scrollMove,this)})},e.prototype.dispose=function(){this._clear(),a.prototype.dispose.apply(this,arguments)},e.prototype._clear=function(){Hc(this.api,this.dataZoomModel),this.range=null},e.type="dataZoom.inside",e}(Na),Fr={zoom:function(a,e,t,r){var i=this.range,n=i.slice(),o=a.axisModels[0];if(o){var s=Wr[e](null,[r.originX,r.originY],o,t,a),l=(s.signal>0?s.pixelStart+s.pixelLength-s.pixel:s.pixel-s.pixelStart)/s.pixelLength*(n[1]-n[0])+n[0],u=Math.max(1/r.scale,0);n[0]=(n[0]-l)*u+l,n[1]=(n[1]-l)*u+l;var h=this.dataZoomModel.findRepresentativeAxisProxy().getMinMaxSpan();if(oe(0,n,[0,100],0,h.minSpan,h.maxSpan),this.range=n,i[0]!==n[0]||i[1]!==n[1])return n}},pan:Vn(function(a,e,t,r,i,n){var o=Wr[r]([n.oldX,n.oldY],[n.newX,n.newY],e,i,t);return o.signal*(a[1]-a[0])*o.pixel/o.pixelLength}),scrollMove:Vn(function(a,e,t,r,i,n){var o=Wr[r]([0,0],[n.scrollDelta,n.scrollDelta],e,i,t);return o.signal*(a[1]-a[0])*n.scrollDelta})};function Vn(a){return function(e,t,r,i){var n=this.range,o=n.slice(),s=e.axisModels[0];if(s){var l=a(o,s,e,t,r,i);if(oe(l,o,[0,100],"all"),this.range=o,n[0]!==o[0]||n[1]!==o[1])return o}}}var Wr={grid:function(a,e,t,r,i){var n=t.axis,o={},s=i.model.coordinateSystem.getRect();return a=a||[0,0],n.dim==="x"?(o.pixel=e[0]-a[0],o.pixelLength=s.width,o.pixelStart=s.x,o.signal=n.inverse?1:-1):(o.pixel=e[1]-a[1],o.pixelLength=s.height,o.pixelStart=s.y,o.signal=n.inverse?-1:1),o},polar:function(a,e,t,r,i){var n=t.axis,o={},s=i.model.coordinateSystem,l=s.getRadiusAxis().getExtent(),u=s.getAngleAxis().getExtent();return a=a?s.pointToCoord(a):[0,0],e=s.pointToCoord(e),t.mainType==="radiusAxis"?(o.pixel=e[0]-a[0],o.pixelLength=l[1]-l[0],o.pixelStart=l[0],o.signal=n.inverse?1:-1):(o.pixel=e[1]-a[1],o.pixelLength=u[1]-u[0],o.pixelStart=u[0],o.signal=n.inverse?-1:1),o},singleAxis:function(a,e,t,r,i){var n=t.axis,o=i.model.coordinateSystem.getRect(),s={};return a=a||[0,0],n.orient==="horizontal"?(s.pixel=e[0]-a[0],s.pixelLength=o.width,s.pixelStart=o.x,s.signal=n.inverse?1:-1):(s.pixel=e[1]-a[1],s.pixelLength=o.height,s.pixelStart=o.y,s.signal=n.inverse?-1:1),s}};function jc(a){Ga(a),a.registerComponentModel(Fc),a.registerComponentView(Kc),$c(a)}var qc=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.type="dataZoom.slider",e.layoutMode="box",e.defaultOption=ie(_e.defaultOption,{show:!0,right:"ph",top:"ph",width:"ph",height:"ph",left:null,bottom:null,borderColor:"#d2dbee",borderRadius:3,backgroundColor:"rgba(47,69,84,0)",dataBackground:{lineStyle:{color:"#d2dbee",width:.5},areaStyle:{color:"#d2dbee",opacity:.2}},selectedDataBackground:{lineStyle:{color:"#8fb0f7",width:.5},areaStyle:{color:"#8fb0f7",opacity:.2}},fillerColor:"rgba(135,175,274,0.2)",handleIcon:"path://M-9.35,34.56V42m0-40V9.5m-2,0h4a2,2,0,0,1,2,2v21a2,2,0,0,1-2,2h-4a2,2,0,0,1-2-2v-21A2,2,0,0,1-11.35,9.5Z",handleSize:"100%",handleStyle:{color:"#fff",borderColor:"#ACB8D1"},moveHandleSize:7,moveHandleIcon:"path://M-320.9-50L-320.9-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-348-41-339-50-320.9-50z M-212.3-50L-212.3-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-239.4-41-230.4-50-212.3-50z M-103.7-50L-103.7-50c18.1,0,27.1,9,27.1,27.1V85.7c0,18.1-9,27.1-27.1,27.1l0,0c-18.1,0-27.1-9-27.1-27.1V-22.9C-130.9-41-121.8-50-103.7-50z",moveHandleStyle:{color:"#D2DBEE",opacity:.7},showDetail:!0,showDataShadow:"auto",realtime:!0,zoomLock:!1,textStyle:{color:"#6E7079"},brushSelect:!0,brushStyle:{color:"rgba(135,175,274,0.15)"},emphasis:{handleLabel:{show:!0},handleStyle:{borderColor:"#8FB0F7"},moveHandleStyle:{color:"#8FB0F7"}}}),e}(_e),ue=nt,zn=7,Qc=1,Hr=30,Jc=7,he="horizontal",Bn="vertical",tf=5,ef=["line","bar","candlestick","scatter"],rf={easing:"cubicOut",duration:100,delay:0},af=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._displayables={},t}return e.prototype.init=function(t,r){this.api=r,this._onBrush=R(this._onBrush,this),this._onBrushEnd=R(this._onBrushEnd,this)},e.prototype.render=function(t,r,i,n){if(a.prototype.render.apply(this,arguments),bo(this,"_dispatchZoomAction",t.get("throttle"),"fixRate"),this._orient=t.getOrient(),t.get("show")===!1){this.group.removeAll();return}if(t.noTarget()){this._clear(),this.group.removeAll();return}(!n||n.type!=="dataZoom"||n.from!==this.uid)&&this._buildView(),this._updateView()},e.prototype.dispose=function(){this._clear(),a.prototype.dispose.apply(this,arguments)},e.prototype._clear=function(){Vl(this,"_dispatchZoomAction");var t=this.api.getZr();t.off("mousemove",this._onBrush),t.off("mouseup",this._onBrushEnd)},e.prototype._buildView=function(){var t=this.group;t.removeAll(),this._brushing=!1,this._displayables.brushRect=null,this._resetLocation(),this._resetInterval();var r=this._displayables.sliderGroup=new $;this._renderBackground(),this._renderHandle(),this._renderDataShadow(),t.add(r),this._positionGroup()},e.prototype._resetLocation=function(){var t=this.dataZoomModel,r=this.api,i=t.get("brushSelect"),n=i?Jc:0,o=this._findCoordRect(),s={width:r.getWidth(),height:r.getHeight()},l=this._orient===he?{right:s.width-o.x-o.width,top:s.height-Hr-zn-n,width:o.width,height:Hr}:{right:zn,top:o.y,width:Hr,height:o.height},u=xa(t.option);w(["right","top","width","height"],function(v){u[v]==="ph"&&(u[v]=l[v])});var h=Ce(u,s);this._location={x:h.x,y:h.y},this._size=[h.width,h.height],this._orient===Bn&&this._size.reverse()},e.prototype._positionGroup=function(){var t=this.group,r=this._location,i=this._orient,n=this.dataZoomModel.getFirstTargetAxisModel(),o=n&&n.get("inverse"),s=this._displayables.sliderGroup,l=(this._dataShadowInfo||{}).otherAxisInverse;s.attr(i===he&&!o?{scaleY:l?1:-1,scaleX:1}:i===he&&o?{scaleY:l?1:-1,scaleX:-1}:i===Bn&&!o?{scaleY:l?-1:1,scaleX:1,rotation:Math.PI/2}:{scaleY:l?-1:1,scaleX:-1,rotation:Math.PI/2});var u=t.getBoundingRect([s]);t.x=r.x-u.x,t.y=r.y-u.y,t.markRedraw()},e.prototype._getViewExtent=function(){return[0,this._size[0]]},e.prototype._renderBackground=function(){var t=this.dataZoomModel,r=this._size,i=this._displayables.sliderGroup,n=t.get("brushSelect");i.add(new ue({silent:!0,shape:{x:0,y:0,width:r[0],height:r[1]},style:{fill:t.get("backgroundColor")},z2:-40}));var o=new ue({shape:{x:0,y:0,width:r[0],height:r[1]},style:{fill:"transparent"},z2:0,onclick:R(this._onClickPanel,this)}),s=this.api.getZr();n?(o.on("mousedown",this._onBrushStart,this),o.cursor="crosshair",s.on("mousemove",this._onBrush),s.on("mouseup",this._onBrushEnd)):(s.off("mousemove",this._onBrush),s.off("mouseup",this._onBrushEnd)),i.add(o)},e.prototype._renderDataShadow=function(){var t=this._dataShadowInfo=this._prepareDataShadowInfo();if(this._displayables.dataShadowSegs=[],!t)return;var r=this._size,i=this._shadowSize||[],n=t.series,o=n.getRawData(),s=n.getShadowDim&&n.getShadowDim(),l=s&&o.getDimensionInfo(s)?n.getShadowDim():t.otherDim;if(l==null)return;var u=this._shadowPolygonPts,h=this._shadowPolylinePts;if(o!==this._shadowData||l!==this._shadowDim||r[0]!==i[0]||r[1]!==i[1]){var v=o.getDataExtent(l),c=(v[1]-v[0])*.3;v=[v[0]-c,v[1]+c];var f=[0,r[1]],p=[0,r[0]],d=[[r[0],0],[0,0]],g=[],y=p[1]/(o.count()-1),m=0,S=Math.round(o.count()/r[0]),x;o.each([l],function(M,T){if(S>0&&T%S){m+=y;return}var C=M==null||isNaN(M)||M==="",L=C?0:H(M,v,f,!0);C&&!x&&T?(d.push([d[d.length-1][0],0]),g.push([g[g.length-1][0],0])):!C&&x&&(d.push([m,0]),g.push([m,0])),d.push([m,L]),g.push([m,L]),m+=y,x=C}),u=this._shadowPolygonPts=d,h=this._shadowPolylinePts=g}this._shadowData=o,this._shadowDim=l,this._shadowSize=[r[0],r[1]];var b=this.dataZoomModel;function _(M){var T=b.getModel(M?"selectedDataBackground":"dataBackground"),C=new $,L=new ye({shape:{points:u},segmentIgnoreThreshold:1,style:T.getModel("areaStyle").getAreaStyle(),silent:!0,z2:-20}),P=new ba({shape:{points:h},segmentIgnoreThreshold:1,style:T.getModel("lineStyle").getLineStyle(),silent:!0,z2:-19});return C.add(L),C.add(P),C}for(var A=0;A<3;A++){var D=_(A===1);this._displayables.sliderGroup.add(D),this._displayables.dataShadowSegs.push(D)}},e.prototype._prepareDataShadowInfo=function(){var t=this.dataZoomModel,r=t.get("showDataShadow");if(r!==!1){var i,n=this.ecModel;return t.eachTargetAxis(function(o,s){var l=t.getAxisProxy(o,s).getTargetSeriesModels();w(l,function(u){if(!i&&!(r!==!0&&et(ef,u.get("type"))<0)){var h=n.getComponent(It(o),s).axis,v=nf(o),c,f=u.coordinateSystem;v!=null&&f.getOtherAxis&&(c=f.getOtherAxis(h).inverse),v=u.getData().mapDimension(v),i={thisAxis:h,series:u,thisDim:o,otherDim:v,otherAxisInverse:c}}},this)},this),i}},e.prototype._renderHandle=function(){var t=this.group,r=this._displayables,i=r.handles=[null,null],n=r.handleLabels=[null,null],o=this._displayables.sliderGroup,s=this._size,l=this.dataZoomModel,u=this.api,h=l.get("borderRadius")||0,v=l.get("brushSelect"),c=r.filler=new ue({silent:v,style:{fill:l.get("fillerColor")},textConfig:{position:"inside"}});o.add(c),o.add(new ue({silent:!0,subPixelOptimize:!0,shape:{x:0,y:0,width:s[0],height:s[1],r:h},style:{stroke:l.get("dataBackgroundColor")||l.get("borderColor"),lineWidth:Qc,fill:"rgba(0,0,0,0)"}})),w([0,1],function(S){var x=l.get("handleIcon");!zl[x]&&x.indexOf("path://")<0&&x.indexOf("image://")<0&&(x="path://"+x);var b=yt(x,-1,0,2,2,null,!0);b.attr({cursor:Nn(this._orient),draggable:!0,drift:R(this._onDragMove,this,S),ondragend:R(this._onDragEnd,this),onmouseover:R(this._showDataInfo,this,!0),onmouseout:R(this._showDataInfo,this,!1),z2:5});var _=b.getBoundingRect(),A=l.get("handleSize");this._handleHeight=ct(A,this._size[1]),this._handleWidth=_.width/_.height*this._handleHeight,b.setStyle(l.getModel("handleStyle").getItemStyle()),b.style.strokeNoScale=!0,b.rectHover=!0,b.ensureState("emphasis").style=l.getModel(["emphasis","handleStyle"]).getItemStyle(),Be(b);var D=l.get("handleColor");D!=null&&(b.style.fill=D),o.add(i[S]=b);var M=l.getModel("textStyle"),T=l.get("handleLabel")||{},C=T.show||!1;t.add(n[S]=new tt({silent:!0,invisible:!C,style:gt(M,{x:0,y:0,text:"",verticalAlign:"middle",align:"center",fill:M.getTextColor(),font:M.getFont()}),z2:10}))},this);var f=c;if(v){var p=ct(l.get("moveHandleSize"),s[1]),d=r.moveHandle=new nt({style:l.getModel("moveHandleStyle").getItemStyle(),silent:!0,shape:{r:[0,0,2,2],y:s[1]-.5,height:p}}),g=p*.8,y=r.moveHandleIcon=yt(l.get("moveHandleIcon"),-g/2,-g/2,g,g,"#fff",!0);y.silent=!0,y.y=s[1]+p/2-.5,d.ensureState("emphasis").style=l.getModel(["emphasis","moveHandleStyle"]).getItemStyle();var m=Math.min(s[1]/2,Math.max(p,10));f=r.moveZone=new nt({invisible:!0,shape:{y:s[1]-m,height:p+m}}),f.on("mouseover",function(){u.enterEmphasis(d)}).on("mouseout",function(){u.leaveEmphasis(d)}),o.add(d),o.add(y),o.add(f)}f.attr({draggable:!0,cursor:Nn(this._orient),drift:R(this._onDragMove,this,"all"),ondragstart:R(this._showDataInfo,this,!0),ondragend:R(this._onDragEnd,this),onmouseover:R(this._showDataInfo,this,!0),onmouseout:R(this._showDataInfo,this,!1)})},e.prototype._resetInterval=function(){var t=this._range=this.dataZoomModel.getPercentRange(),r=this._getViewExtent();this._handleEnds=[H(t[0],[0,100],r,!0),H(t[1],[0,100],r,!0)]},e.prototype._updateInterval=function(t,r){var i=this.dataZoomModel,n=this._handleEnds,o=this._getViewExtent(),s=i.findRepresentativeAxisProxy().getMinMaxSpan(),l=[0,100];oe(r,n,o,i.get("zoomLock")?"all":t,s.minSpan!=null?H(s.minSpan,l,o,!0):null,s.maxSpan!=null?H(s.maxSpan,l,o,!0):null);var u=this._range,h=this._range=bt([H(n[0],o,l,!0),H(n[1],o,l,!0)]);return!u||u[0]!==h[0]||u[1]!==h[1]},e.prototype._updateView=function(t){var r=this._displayables,i=this._handleEnds,n=bt(i.slice()),o=this._size;w([0,1],function(f){var p=r.handles[f],d=this._handleHeight;p.attr({scaleX:d/2,scaleY:d/2,x:i[f]+(f?-1:1),y:o[1]/2-d/2})},this),r.filler.setShape({x:n[0],y:0,width:n[1]-n[0],height:o[1]});var s={x:n[0],width:n[1]-n[0]};r.moveHandle&&(r.moveHandle.setShape(s),r.moveZone.setShape(s),r.moveZone.getBoundingRect(),r.moveHandleIcon&&r.moveHandleIcon.attr("x",s.x+s.width/2));for(var l=r.dataShadowSegs,u=[0,n[0],n[1],o[0]],h=0;h<l.length;h++){var v=l[h],c=v.getClipPath();c||(c=new nt,v.setClipPath(c)),c.setShape({x:u[h],y:0,width:u[h+1]-u[h],height:o[1]})}this._updateDataInfo(t)},e.prototype._updateDataInfo=function(t){var r=this.dataZoomModel,i=this._displayables,n=i.handleLabels,o=this._orient,s=["",""];if(r.get("showDetail")){var l=r.findRepresentativeAxisProxy();if(l){var u=l.getAxisModel().axis,h=this._range,v=t?l.calculateDataWindow({start:h[0],end:h[1]}).valueWindow:l.getDataValueWindow();s=[this._formatLabel(v[0],u),this._formatLabel(v[1],u)]}}var c=bt(this._handleEnds.slice());f.call(this,0),f.call(this,1);function f(p){var d=Jt(i.handles[p].parent,this.group),g=_a(p===0?"right":"left",d),y=this._handleWidth/2+tf,m=te([c[p]+(p===0?-y:y),this._size[1]/2],d);n[p].setStyle({x:m[0],y:m[1],verticalAlign:o===he?"middle":g,align:o===he?g:"center",text:s[p]})}},e.prototype._formatLabel=function(t,r){var i=this.dataZoomModel,n=i.get("labelFormatter"),o=i.get("labelPrecision");(o==null||o==="auto")&&(o=r.getPixelPrecision());var s=t==null||isNaN(t)?"":r.type==="category"||r.type==="time"?r.scale.getLabel({value:Math.round(t)}):t.toFixed(Math.min(o,20));return rt(n)?n(t,s):it(n)?n.replace("{value}",s):s},e.prototype._showDataInfo=function(t){var r=this.dataZoomModel.get("handleLabel")||{},i=r.show||!1,n=this.dataZoomModel.getModel(["emphasis","handleLabel"]),o=n.get("show")||!1,s=t||this._dragging?o:i,l=this._displayables,u=l.handleLabels;u[0].attr("invisible",!s),u[1].attr("invisible",!s),l.moveHandle&&this.api[s?"enterEmphasis":"leaveEmphasis"](l.moveHandle,1)},e.prototype._onDragMove=function(t,r,i,n){this._dragging=!0,Wt(n.event);var o=this._displayables.sliderGroup.getLocalTransform(),s=te([r,i],o,!0),l=this._updateInterval(t,s[0]),u=this.dataZoomModel.get("realtime");this._updateView(!u),l&&u&&this._dispatchZoomAction(!0)},e.prototype._onDragEnd=function(){this._dragging=!1,this._showDataInfo(!1);var t=this.dataZoomModel.get("realtime");!t&&this._dispatchZoomAction(!1)},e.prototype._onClickPanel=function(t){var r=this._size,i=this._displayables.sliderGroup.transformCoordToLocal(t.offsetX,t.offsetY);if(!(i[0]<0||i[0]>r[0]||i[1]<0||i[1]>r[1])){var n=this._handleEnds,o=(n[0]+n[1])/2,s=this._updateInterval("all",i[0]-o);this._updateView(),s&&this._dispatchZoomAction(!1)}},e.prototype._onBrushStart=function(t){var r=t.offsetX,i=t.offsetY;this._brushStart=new Bl(r,i),this._brushing=!0,this._brushStartTime=+new Date},e.prototype._onBrushEnd=function(t){if(this._brushing){var r=this._displayables.brushRect;if(this._brushing=!1,!!r){r.attr("ignore",!0);var i=r.shape,n=+new Date;if(!(n-this._brushStartTime<200&&Math.abs(i.width)<5)){var o=this._getViewExtent(),s=[0,100];this._range=bt([H(i.x,o,s,!0),H(i.x+i.width,o,s,!0)]),this._handleEnds=[i.x,i.x+i.width],this._updateView(),this._dispatchZoomAction(!1)}}}},e.prototype._onBrush=function(t){this._brushing&&(Wt(t.event),this._updateBrushRect(t.offsetX,t.offsetY))},e.prototype._updateBrushRect=function(t,r){var i=this._displayables,n=this.dataZoomModel,o=i.brushRect;o||(o=i.brushRect=new ue({silent:!0,style:n.getModel("brushStyle").getItemStyle()}),i.sliderGroup.add(o)),o.attr("ignore",!1);var s=this._brushStart,l=this._displayables.sliderGroup,u=l.transformCoordToLocal(t,r),h=l.transformCoordToLocal(s.x,s.y),v=this._size;u[0]=Math.max(Math.min(v[0],u[0]),0),o.setShape({x:h[0],y:0,width:u[0]-h[0],height:v[1]})},e.prototype._dispatchZoomAction=function(t){var r=this._range;this.api.dispatchAction({type:"dataZoom",from:this.uid,dataZoomId:this.dataZoomModel.id,animation:t?rf:null,start:r[0],end:r[1]})},e.prototype._findCoordRect=function(){var t,r=cs(this.dataZoomModel).infoList;if(!t&&r.length){var i=r[0].model.coordinateSystem;t=i.getRect&&i.getRect()}if(!t){var n=this.api.getWidth(),o=this.api.getHeight();t={x:n*.2,y:o*.2,width:n*.6,height:o*.6}}return t},e.type="dataZoom.slider",e}(Na);function nf(a){var e={x:"y",y:"x",radius:"angle",angle:"radius"};return e[a]}function Nn(a){return a==="vertical"?"ns-resize":"ew-resize"}function of(a){a.registerComponentModel(qc),a.registerComponentView(af),Ga(a)}function Hf(a){kt(jc),kt(of)}var As={get:function(a,e,t){var r=U((sf[a]||{})[e]);return t&&O(r)?r[r.length-1]:r}},sf={color:{active:["#006edd","#e0ffff"],inactive:["rgba(0,0,0,0)"]},colorHue:{active:[0,360],inactive:[0,0]},colorSaturation:{active:[.3,1],inactive:[0,0]},colorLightness:{active:[.9,.5],inactive:[0,0]},colorAlpha:{active:[.3,1],inactive:[0,0]},opacity:{active:[.3,1],inactive:[0,0]},symbol:{active:["circle","roundRect","diamond"],inactive:["none"]},symbolSize:{active:[10,50],inactive:[0,0]}},Gn=J.mapVisual,lf=J.eachVisual,uf=O,Fn=w,hf=bt,vf=H,Je=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.stateList=["inRange","outOfRange"],t.replacableOptionKeys=["inRange","outOfRange","target","controller","color"],t.layoutMode={type:"box",ignoreSize:!0},t.dataBound=[-1/0,1/0],t.targetVisuals={},t.controllerVisuals={},t}return e.prototype.init=function(t,r,i){this.mergeDefaultAndTheme(t,i)},e.prototype.optionUpdated=function(t,r){var i=this.option;!r&&gc(i,t,this.replacableOptionKeys),this.textStyleModel=this.getModel("textStyle"),this.resetItemSize(),this.completeVisualOption()},e.prototype.resetVisual=function(t){var r=this.stateList;t=R(t,this),this.controllerVisuals=Cn(this.option.controller,r,t),this.targetVisuals=Cn(this.option.target,r,t)},e.prototype.getItemSymbol=function(){return null},e.prototype.getTargetSeriesIndices=function(){var t=this.option.seriesIndex,r=[];return t==null||t==="all"?this.ecModel.eachSeries(function(i,n){r.push(n)}):r=or(t),r},e.prototype.eachTargetSeries=function(t,r){w(this.getTargetSeriesIndices(),function(i){var n=this.ecModel.getSeriesByIndex(i);n&&t.call(r,n)},this)},e.prototype.isTargetSeries=function(t){var r=!1;return this.eachTargetSeries(function(i){i===t&&(r=!0)}),r},e.prototype.formatValueText=function(t,r,i){var n=this.option,o=n.precision,s=this.dataBound,l=n.formatter,u;i=i||["<",">"],O(t)&&(t=t.slice(),u=!0);var h=r?t:u?[v(t[0]),v(t[1])]:v(t);if(it(l))return l.replace("{value}",u?h[0]:h).replace("{value2}",u?h[1]:h);if(rt(l))return u?l(t[0],t[1]):l(t);if(u)return t[0]===s[0]?i[0]+" "+h[1]:t[1]===s[1]?i[1]+" "+h[0]:h[0]+" - "+h[1];return h;function v(c){return c===s[0]?"min":c===s[1]?"max":(+c).toFixed(Math.min(o,20))}},e.prototype.resetExtent=function(){var t=this.option,r=hf([t.min,t.max]);this._dataExtent=r},e.prototype.getDataDimensionIndex=function(t){var r=this.option.dimension;if(r!=null)return t.getDimensionIndex(r);for(var i=t.dimensions,n=i.length-1;n>=0;n--){var o=i[n],s=t.getDimensionInfo(o);if(!s.isCalculationCoord)return s.storeDimIndex}},e.prototype.getExtent=function(){return this._dataExtent.slice()},e.prototype.completeVisualOption=function(){var t=this.ecModel,r=this.option,i={inRange:r.inRange,outOfRange:r.outOfRange},n=r.target||(r.target={}),o=r.controller||(r.controller={});Z(n,i),Z(o,i);var s=this.isCategory();l.call(this,n),l.call(this,o),u.call(this,n,"inRange","outOfRange"),h.call(this,o);function l(v){uf(r.color)&&!v.inRange&&(v.inRange={color:r.color.slice().reverse()}),v.inRange=v.inRange||{color:t.get("gradientColor")}}function u(v,c,f){var p=v[c],d=v[f];p&&!d&&(d=v[f]={},Fn(p,function(g,y){if(J.isValidType(y)){var m=As.get(y,"inactive",s);m!=null&&(d[y]=m,y==="color"&&!d.hasOwnProperty("opacity")&&!d.hasOwnProperty("colorAlpha")&&(d.opacity=[0,0]))}}))}function h(v){var c=(v.inRange||{}).symbol||(v.outOfRange||{}).symbol,f=(v.inRange||{}).symbolSize||(v.outOfRange||{}).symbolSize,p=this.get("inactiveColor"),d=this.getItemSymbol(),g=d||"roundRect";Fn(this.stateList,function(y){var m=this.itemSize,S=v[y];S||(S=v[y]={color:s?p:[p]}),S.symbol==null&&(S.symbol=c&&U(c)||(s?g:[g])),S.symbolSize==null&&(S.symbolSize=f&&U(f)||(s?m[0]:[m[0],m[0]])),S.symbol=Gn(S.symbol,function(_){return _==="none"?g:_});var x=S.symbolSize;if(x!=null){var b=-1/0;lf(x,function(_){_>b&&(b=_)}),S.symbolSize=Gn(x,function(_){return vf(_,[0,b],[0,m[0]],!0)})}},this)}},e.prototype.resetItemSize=function(){this.itemSize=[parseFloat(this.get("itemWidth")),parseFloat(this.get("itemHeight"))]},e.prototype.isCategory=function(){return!!this.option.categories},e.prototype.setSelected=function(t){},e.prototype.getSelected=function(){return null},e.prototype.getValueState=function(t){return null},e.prototype.getVisualMeta=function(t){return null},e.type="visualMap",e.dependencies=["series"],e.defaultOption={show:!0,z:4,seriesIndex:"all",min:0,max:200,left:0,right:null,top:null,bottom:0,itemWidth:null,itemHeight:null,inverse:!1,orient:"vertical",backgroundColor:"rgba(0,0,0,0)",borderColor:"#ccc",contentColor:"#5793f3",inactiveColor:"#aaa",borderWidth:0,padding:5,textGap:10,precision:0,textStyle:{color:"#333"}},e}(Mt),Wn=[20,140],cf=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(t,r){a.prototype.optionUpdated.apply(this,arguments),this.resetExtent(),this.resetVisual(function(i){i.mappingMethod="linear",i.dataExtent=this.getExtent()}),this._resetRange()},e.prototype.resetItemSize=function(){a.prototype.resetItemSize.apply(this,arguments);var t=this.itemSize;(t[0]==null||isNaN(t[0]))&&(t[0]=Wn[0]),(t[1]==null||isNaN(t[1]))&&(t[1]=Wn[1])},e.prototype._resetRange=function(){var t=this.getExtent(),r=this.option.range;!r||r.auto?(t.auto=1,this.option.range=t):O(r)&&(r[0]>r[1]&&r.reverse(),r[0]=Math.max(r[0],t[0]),r[1]=Math.min(r[1],t[1]))},e.prototype.completeVisualOption=function(){a.prototype.completeVisualOption.apply(this,arguments),w(this.stateList,function(t){var r=this.option.controller[t].symbolSize;r&&r[0]!==r[1]&&(r[0]=r[1]/3)},this)},e.prototype.setSelected=function(t){this.option.range=t.slice(),this._resetRange()},e.prototype.getSelected=function(){var t=this.getExtent(),r=bt((this.get("range")||[]).slice());return r[0]>t[1]&&(r[0]=t[1]),r[1]>t[1]&&(r[1]=t[1]),r[0]<t[0]&&(r[0]=t[0]),r[1]<t[0]&&(r[1]=t[0]),r},e.prototype.getValueState=function(t){var r=this.option.range,i=this.getExtent();return(r[0]<=i[0]||r[0]<=t)&&(r[1]>=i[1]||t<=r[1])?"inRange":"outOfRange"},e.prototype.findTargetDataIndices=function(t){var r=[];return this.eachTargetSeries(function(i){var n=[],o=i.getData();o.each(this.getDataDimensionIndex(o),function(s,l){t[0]<=s&&s<=t[1]&&n.push(l)},this),r.push({seriesId:i.id,dataIndex:n})},this),r},e.prototype.getVisualMeta=function(t){var r=Hn(this,"outOfRange",this.getExtent()),i=Hn(this,"inRange",this.option.range.slice()),n=[];function o(f,p){n.push({value:f,color:t(f,p)})}for(var s=0,l=0,u=i.length,h=r.length;l<h&&(!i.length||r[l]<=i[0]);l++)r[l]<i[s]&&o(r[l],"outOfRange");for(var v=1;s<u;s++,v=0)v&&n.length&&o(i[s],"outOfRange"),o(i[s],"inRange");for(var v=1;l<h;l++)(!i.length||i[i.length-1]<r[l])&&(v&&(n.length&&o(n[n.length-1].value,"outOfRange"),v=0),o(r[l],"outOfRange"));var c=n.length;return{stops:n,outerColors:[c?n[0].color:"transparent",c?n[c-1].color:"transparent"]}},e.type="visualMap.continuous",e.defaultOption=ie(Je.defaultOption,{align:"auto",calculable:!1,hoverLink:!0,realtime:!0,handleIcon:"path://M-11.39,9.77h0a3.5,3.5,0,0,1-3.5,3.5h-22a3.5,3.5,0,0,1-3.5-3.5h0a3.5,3.5,0,0,1,3.5-3.5h22A3.5,3.5,0,0,1-11.39,9.77Z",handleSize:"120%",handleStyle:{borderColor:"#fff",borderWidth:1},indicatorIcon:"circle",indicatorSize:"50%",indicatorStyle:{borderColor:"#fff",borderWidth:2,shadowBlur:2,shadowOffsetX:1,shadowOffsetY:1,shadowColor:"rgba(0,0,0,0.2)"}}),e}(Je);function Hn(a,e,t){if(t[0]===t[1])return t.slice();for(var r=200,i=(t[1]-t[0])/r,n=t[0],o=[],s=0;s<=r&&n<t[1];s++)o.push(n),n+=i;return o.push(t[1]),o}var ws=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t.autoPositionValues={left:1,right:1,top:1,bottom:1},t}return e.prototype.init=function(t,r){this.ecModel=t,this.api=r},e.prototype.render=function(t,r,i,n){if(this.visualMapModel=t,t.get("show")===!1){this.group.removeAll();return}this.doRender(t,r,i,n)},e.prototype.renderBackground=function(t){var r=this.visualMapModel,i=Nl(r.get("padding")||0),n=t.getBoundingRect();t.add(new nt({z2:-1,silent:!0,shape:{x:n.x-i[3],y:n.y-i[0],width:n.width+i[3]+i[1],height:n.height+i[0]+i[2]},style:{fill:r.get("backgroundColor"),stroke:r.get("borderColor"),lineWidth:r.get("borderWidth")}}))},e.prototype.getControllerVisual=function(t,r,i){i=i||{};var n=i.forceState,o=this.visualMapModel,s={};if(r==="color"){var l=o.get("contentColor");s.color=l}function u(f){return s[f]}function h(f,p){s[f]=p}var v=o.controllerVisuals[n||o.getValueState(t)],c=J.prepareVisualTypes(v);return w(c,function(f){var p=v[f];i.convertOpacityToAlpha&&f==="opacity"&&(f="colorAlpha",p=v.__alphaForOpacity),J.dependsOn(f,r)&&p&&p.applyVisual(t,u,h)}),s[r]},e.prototype.positionGroup=function(t){var r=this.visualMapModel,i=this.api;po(t,r.getBoxLayoutParams(),{width:i.getWidth(),height:i.getHeight()})},e.prototype.doRender=function(t,r,i,n){},e.type="visualMap",e}(Et),Zn=[["left","right","width"],["top","bottom","height"]];function Ms(a,e,t){var r=a.option,i=r.align;if(i!=null&&i!=="auto")return i;for(var n={width:e.getWidth(),height:e.getHeight()},o=r.orient==="horizontal"?1:0,s=Zn[o],l=[0,null,10],u={},h=0;h<3;h++)u[Zn[1-o][h]]=l[h],u[s[h]]=h===2?t[0]:r[s[h]];var v=[["x","width",3],["y","height",0]][o],c=Ce(u,n,r.padding);return s[(c.margin[v[2]]||0)+c[v[0]]+c[v[1]]*.5<n[v[1]]*.5?0:1]}function Fe(a,e){return w(a||[],function(t){t.dataIndex!=null&&(t.dataIndexInside=t.dataIndex,t.dataIndex=null),t.highlightKey="visualMap"+(e?e.componentIndex:"")}),a}var xt=H,ff=w,Un=Math.min,Zr=Math.max,pf=12,df=6,gf=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._shapes={},t._dataInterval=[],t._handleEnds=[],t._hoverLinkDataIndices=[],t}return e.prototype.init=function(t,r){a.prototype.init.call(this,t,r),this._hoverLinkFromSeriesMouseOver=R(this._hoverLinkFromSeriesMouseOver,this),this._hideIndicator=R(this._hideIndicator,this)},e.prototype.doRender=function(t,r,i,n){(!n||n.type!=="selectDataRange"||n.from!==this.uid)&&this._buildView()},e.prototype._buildView=function(){this.group.removeAll();var t=this.visualMapModel,r=this.group;this._orient=t.get("orient"),this._useHandle=t.get("calculable"),this._resetInterval(),this._renderBar(r);var i=t.get("text");this._renderEndsText(r,i,0),this._renderEndsText(r,i,1),this._updateView(!0),this.renderBackground(r),this._updateView(),this._enableHoverLinkToSeries(),this._enableHoverLinkFromSeries(),this.positionGroup(r)},e.prototype._renderEndsText=function(t,r,i){if(r){var n=r[1-i];n=n!=null?n+"":"";var o=this.visualMapModel,s=o.get("textGap"),l=o.itemSize,u=this._shapes.mainGroup,h=this._applyTransform([l[0]/2,i===0?-s:l[1]+s],u),v=this._applyTransform(i===0?"bottom":"top",u),c=this._orient,f=this.visualMapModel.textStyleModel;this.group.add(new tt({style:gt(f,{x:h[0],y:h[1],verticalAlign:c==="horizontal"?"middle":v,align:c==="horizontal"?v:"center",text:n})}))}},e.prototype._renderBar=function(t){var r=this.visualMapModel,i=this._shapes,n=r.itemSize,o=this._orient,s=this._useHandle,l=Ms(r,this.api,n),u=i.mainGroup=this._createBarGroup(l),h=new $;u.add(h),h.add(i.outOfRange=Xn()),h.add(i.inRange=Xn(null,s?$n(this._orient):null,R(this._dragHandle,this,"all",!1),R(this._dragHandle,this,"all",!0))),h.setClipPath(new nt({shape:{x:0,y:0,width:n[0],height:n[1],r:3}}));var v=r.textStyleModel.getTextRect("国"),c=Zr(v.width,v.height);s&&(i.handleThumbs=[],i.handleLabels=[],i.handleLabelPoints=[],this._createHandle(r,u,0,n,c,o),this._createHandle(r,u,1,n,c,o)),this._createIndicator(r,u,n,c,o),t.add(u)},e.prototype._createHandle=function(t,r,i,n,o,s){var l=R(this._dragHandle,this,i,!1),u=R(this._dragHandle,this,i,!0),h=Kr(t.get("handleSize"),n[0]),v=yt(t.get("handleIcon"),-h/2,-h/2,h,h,null,!0),c=$n(this._orient);v.attr({cursor:c,draggable:!0,drift:l,ondragend:u,onmousemove:function(y){Wt(y.event)}}),v.x=n[0]/2,v.useStyle(t.getModel("handleStyle").getItemStyle()),v.setStyle({strokeNoScale:!0,strokeFirst:!0}),v.style.lineWidth*=2,v.ensureState("emphasis").style=t.getModel(["emphasis","handleStyle"]).getItemStyle(),Gl(v,!0),r.add(v);var f=this.visualMapModel.textStyleModel,p=new tt({cursor:c,draggable:!0,drift:l,onmousemove:function(y){Wt(y.event)},ondragend:u,style:gt(f,{x:0,y:0,text:""})});p.ensureState("blur").style={opacity:.1},p.stateTransition={duration:200},this.group.add(p);var d=[h,0],g=this._shapes;g.handleThumbs[i]=v,g.handleLabelPoints[i]=d,g.handleLabels[i]=p},e.prototype._createIndicator=function(t,r,i,n,o){var s=Kr(t.get("indicatorSize"),i[0]),l=yt(t.get("indicatorIcon"),-s/2,-s/2,s,s,null,!0);l.attr({cursor:"move",invisible:!0,silent:!0,x:i[0]/2});var u=t.getModel("indicatorStyle").getItemStyle();if(l instanceof ga){var h=l.style;l.useStyle(F({image:h.image,x:h.x,y:h.y,width:h.width,height:h.height},u))}else l.useStyle(u);r.add(l);var v=this.visualMapModel.textStyleModel,c=new tt({silent:!0,invisible:!0,style:gt(v,{x:0,y:0,text:""})});this.group.add(c);var f=[(o==="horizontal"?n/2:df)+i[0]/2,0],p=this._shapes;p.indicator=l,p.indicatorLabel=c,p.indicatorLabelPoint=f,this._firstShowIndicator=!0},e.prototype._dragHandle=function(t,r,i,n){if(this._useHandle){if(this._dragging=!r,!r){var o=this._applyTransform([i,n],this._shapes.mainGroup,!0);this._updateInterval(t,o[1]),this._hideIndicator(),this._updateView()}r===!this.visualMapModel.get("realtime")&&this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:this._dataInterval.slice()}),r?!this._hovering&&this._clearHoverLinkToSeries():Yn(this.visualMapModel)&&this._doHoverLinkToSeries(this._handleEnds[t],!1)}},e.prototype._resetInterval=function(){var t=this.visualMapModel,r=this._dataInterval=t.getSelected(),i=t.getExtent(),n=[0,t.itemSize[1]];this._handleEnds=[xt(r[0],i,n,!0),xt(r[1],i,n,!0)]},e.prototype._updateInterval=function(t,r){r=r||0;var i=this.visualMapModel,n=this._handleEnds,o=[0,i.itemSize[1]];oe(r,n,o,t,0);var s=i.getExtent();this._dataInterval=[xt(n[0],o,s,!0),xt(n[1],o,s,!0)]},e.prototype._updateView=function(t){var r=this.visualMapModel,i=r.getExtent(),n=this._shapes,o=[0,r.itemSize[1]],s=t?o:this._handleEnds,l=this._createBarVisual(this._dataInterval,i,s,"inRange"),u=this._createBarVisual(i,i,o,"outOfRange");n.inRange.setStyle({fill:l.barColor}).setShape("points",l.barPoints),n.outOfRange.setStyle({fill:u.barColor}).setShape("points",u.barPoints),this._updateHandle(s,l)},e.prototype._createBarVisual=function(t,r,i,n){var o={forceState:n,convertOpacityToAlpha:!0},s=this._makeColorGradient(t,o),l=[this.getControllerVisual(t[0],"symbolSize",o),this.getControllerVisual(t[1],"symbolSize",o)],u=this._createBarPoints(i,l);return{barColor:new no(0,0,0,1,s),barPoints:u,handlesColor:[s[0].color,s[s.length-1].color]}},e.prototype._makeColorGradient=function(t,r){var i=100,n=[],o=(t[1]-t[0])/i;n.push({color:this.getControllerVisual(t[0],"color",r),offset:0});for(var s=1;s<i;s++){var l=t[0]+o*s;if(l>t[1])break;n.push({color:this.getControllerVisual(l,"color",r),offset:s/i})}return n.push({color:this.getControllerVisual(t[1],"color",r),offset:1}),n},e.prototype._createBarPoints=function(t,r){var i=this.visualMapModel.itemSize;return[[i[0]-r[0],t[0]],[i[0],t[0]],[i[0],t[1]],[i[0]-r[1],t[1]]]},e.prototype._createBarGroup=function(t){var r=this._orient,i=this.visualMapModel.get("inverse");return new $(r==="horizontal"&&!i?{scaleX:t==="bottom"?1:-1,rotation:Math.PI/2}:r==="horizontal"&&i?{scaleX:t==="bottom"?-1:1,rotation:-Math.PI/2}:r==="vertical"&&!i?{scaleX:t==="left"?1:-1,scaleY:-1}:{scaleX:t==="left"?1:-1})},e.prototype._updateHandle=function(t,r){if(this._useHandle){var i=this._shapes,n=this.visualMapModel,o=i.handleThumbs,s=i.handleLabels,l=n.itemSize,u=n.getExtent(),h=this._applyTransform("left",i.mainGroup);ff([0,1],function(v){var c=o[v];c.setStyle("fill",r.handlesColor[v]),c.y=t[v];var f=xt(t[v],[0,l[1]],u,!0),p=this.getControllerVisual(f,"symbolSize");c.scaleX=c.scaleY=p/l[0],c.x=l[0]-p/2;var d=te(i.handleLabelPoints[v],Jt(c,this.group));if(this._orient==="horizontal"){var g=h==="left"||h==="top"?(l[0]-p)/2:(l[0]-p)/-2;d[1]+=g}s[v].setStyle({x:d[0],y:d[1],text:n.formatValueText(this._dataInterval[v]),verticalAlign:"middle",align:this._orient==="vertical"?this._applyTransform("left",i.mainGroup):"center"})},this)}},e.prototype._showIndicator=function(t,r,i,n){var o=this.visualMapModel,s=o.getExtent(),l=o.itemSize,u=[0,l[1]],h=this._shapes,v=h.indicator;if(v){v.attr("invisible",!1);var c={convertOpacityToAlpha:!0},f=this.getControllerVisual(t,"color",c),p=this.getControllerVisual(t,"symbolSize"),d=xt(t,s,u,!0),g=l[0]-p/2,y={x:v.x,y:v.y};v.y=d,v.x=g;var m=te(h.indicatorLabelPoint,Jt(v,this.group)),S=h.indicatorLabel;S.attr("invisible",!1);var x=this._applyTransform("left",h.mainGroup),b=this._orient,_=b==="horizontal";S.setStyle({text:(i||"")+o.formatValueText(r),verticalAlign:_?x:"middle",align:_?"center":x});var A={x:g,y:d,style:{fill:f}},D={style:{x:m[0],y:m[1]}};if(o.ecModel.isAnimationEnabled()&&!this._firstShowIndicator){var M={duration:100,easing:"cubicInOut",additive:!0};v.x=y.x,v.y=y.y,v.animateTo(A,M),S.animateTo(D,M)}else v.attr(A),S.attr(D);this._firstShowIndicator=!1;var T=this._shapes.handleLabels;if(T)for(var C=0;C<T.length;C++)this.api.enterBlur(T[C])}},e.prototype._enableHoverLinkToSeries=function(){var t=this;this._shapes.mainGroup.on("mousemove",function(r){if(t._hovering=!0,!t._dragging){var i=t.visualMapModel.itemSize,n=t._applyTransform([r.offsetX,r.offsetY],t._shapes.mainGroup,!0,!0);n[1]=Un(Zr(0,n[1]),i[1]),t._doHoverLinkToSeries(n[1],0<=n[0]&&n[0]<=i[0])}}).on("mouseout",function(){t._hovering=!1,!t._dragging&&t._clearHoverLinkToSeries()})},e.prototype._enableHoverLinkFromSeries=function(){var t=this.api.getZr();this.visualMapModel.option.hoverLink?(t.on("mouseover",this._hoverLinkFromSeriesMouseOver,this),t.on("mouseout",this._hideIndicator,this)):this._clearHoverLinkFromSeries()},e.prototype._doHoverLinkToSeries=function(t,r){var i=this.visualMapModel,n=i.itemSize;if(i.option.hoverLink){var o=[0,n[1]],s=i.getExtent();t=Un(Zr(o[0],t),o[1]);var l=yf(i,s,o),u=[t-l,t+l],h=xt(t,o,s,!0),v=[xt(u[0],o,s,!0),xt(u[1],o,s,!0)];u[0]<o[0]&&(v[0]=-1/0),u[1]>o[1]&&(v[1]=1/0),r&&(v[0]===-1/0?this._showIndicator(h,v[1],"< ",l):v[1]===1/0?this._showIndicator(h,v[0],"> ",l):this._showIndicator(h,h,"≈ ",l));var c=this._hoverLinkDataIndices,f=[];(r||Yn(i))&&(f=this._hoverLinkDataIndices=i.findTargetDataIndices(v));var p=Fl(c,f);this._dispatchHighDown("downplay",Fe(p[0],i)),this._dispatchHighDown("highlight",Fe(p[1],i))}},e.prototype._hoverLinkFromSeriesMouseOver=function(t){var r;if(Wl(t.target,function(l){var u=at(l);if(u.dataIndex!=null)return r=u,!0},!0),!!r){var i=this.ecModel.getSeriesByIndex(r.seriesIndex),n=this.visualMapModel;if(n.isTargetSeries(i)){var o=i.getData(r.dataType),s=o.getStore().get(n.getDataDimensionIndex(o),r.dataIndex);isNaN(s)||this._showIndicator(s,s)}}},e.prototype._hideIndicator=function(){var t=this._shapes;t.indicator&&t.indicator.attr("invisible",!0),t.indicatorLabel&&t.indicatorLabel.attr("invisible",!0);var r=this._shapes.handleLabels;if(r)for(var i=0;i<r.length;i++)this.api.leaveBlur(r[i])},e.prototype._clearHoverLinkToSeries=function(){this._hideIndicator();var t=this._hoverLinkDataIndices;this._dispatchHighDown("downplay",Fe(t,this.visualMapModel)),t.length=0},e.prototype._clearHoverLinkFromSeries=function(){this._hideIndicator();var t=this.api.getZr();t.off("mouseover",this._hoverLinkFromSeriesMouseOver),t.off("mouseout",this._hideIndicator)},e.prototype._applyTransform=function(t,r,i,n){var o=Jt(r,n?null:this.group);return O(t)?te(t,o,i):_a(t,o,i)},e.prototype._dispatchHighDown=function(t,r){r&&r.length&&this.api.dispatchAction({type:t,batch:r})},e.prototype.dispose=function(){this._clearHoverLinkFromSeries(),this._clearHoverLinkToSeries()},e.type="visualMap.continuous",e}(ws);function Xn(a,e,t,r){return new ye({shape:{points:a},draggable:!!t,cursor:e,drift:t,onmousemove:function(i){Wt(i.event)},ondragend:r})}function yf(a,e,t){var r=pf/2,i=a.get("hoverLinkDataSize");return i&&(r=xt(i,e,t,!0)/2),r}function Yn(a){var e=a.get("hoverLinkOnHandle");return!!(e??a.get("realtime"))}function $n(a){return a==="vertical"?"ns-resize":"ew-resize"}var mf={type:"selectDataRange",event:"dataRangeSelected",update:"update"},xf=function(a,e){e.eachComponent({mainType:"visualMap",query:a},function(t){t.setSelected(a.selected)})},Sf=[{createOnAllSeries:!0,reset:function(a,e){var t=[];return e.eachComponent("visualMap",function(r){var i=a.pipelineContext;!r.isTargetSeries(a)||i&&i.large||t.push(yc(r.stateList,r.targetVisuals,R(r.getValueState,r),r.getDataDimensionIndex(a.getData())))}),t}},{createOnAllSeries:!0,reset:function(a,e){var t=a.getData(),r=[];e.eachComponent("visualMap",function(i){if(i.isTargetSeries(a)){var n=i.getVisualMeta(R(bf,null,a,i))||{stops:[],outerColors:[]},o=i.getDataDimensionIndex(t);o>=0&&(n.dimension=o,r.push(n))}}),a.getData().setVisual("visualMeta",r)}}];function bf(a,e,t,r){for(var i=e.targetVisuals[r],n=J.prepareVisualTypes(i),o={color:wa(a.getData(),"color")},s=0,l=n.length;s<l;s++){var u=n[s],h=i[u==="opacity"?"__alphaForOpacity":u];h&&h.applyVisual(t,v,c)}return o.color;function v(f){return o[f]}function c(f,p){o[f]=p}}var Kn=w;function _f(a){var e=a&&a.visualMap;O(e)||(e=e?[e]:[]),Kn(e,function(t){if(t){Kt(t,"splitList")&&!Kt(t,"pieces")&&(t.pieces=t.splitList,delete t.splitList);var r=t.pieces;r&&O(r)&&Kn(r,function(i){st(i)&&(Kt(i,"start")&&!Kt(i,"min")&&(i.min=i.start),Kt(i,"end")&&!Kt(i,"max")&&(i.max=i.end))})}})}function Kt(a,e){return a&&a.hasOwnProperty&&a.hasOwnProperty(e)}var jn=!1;function Cs(a){jn||(jn=!0,a.registerSubTypeDefaulter("visualMap",function(e){return!e.categories&&(!(e.pieces?e.pieces.length>0:e.splitNumber>0)||e.calculable)?"continuous":"piecewise"}),a.registerAction(mf,xf),w(Sf,function(e){a.registerVisual(a.PRIORITY.VISUAL.COMPONENT,e)}),a.registerPreprocessor(_f))}function Af(a){a.registerComponentModel(cf),a.registerComponentView(gf),Cs(a)}var wf=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t._pieceList=[],t}return e.prototype.optionUpdated=function(t,r){a.prototype.optionUpdated.apply(this,arguments),this.resetExtent();var i=this._mode=this._determineMode();this._pieceList=[],Mf[this._mode].call(this,this._pieceList),this._resetSelected(t,r);var n=this.option.categories;this.resetVisual(function(o,s){i==="categories"?(o.mappingMethod="category",o.categories=U(n)):(o.dataExtent=this.getExtent(),o.mappingMethod="piecewise",o.pieceList=E(this._pieceList,function(l){return l=U(l),s!=="inRange"&&(l.visual=null),l}))})},e.prototype.completeVisualOption=function(){var t=this.option,r={},i=J.listVisualTypes(),n=this.isCategory();w(t.pieces,function(s){w(i,function(l){s.hasOwnProperty(l)&&(r[l]=1)})}),w(r,function(s,l){var u=!1;w(this.stateList,function(h){u=u||o(t,h,l)||o(t.target,h,l)},this),!u&&w(this.stateList,function(h){(t[h]||(t[h]={}))[l]=As.get(l,h==="inRange"?"active":"inactive",n)})},this);function o(s,l,u){return s&&s[l]&&s[l].hasOwnProperty(u)}a.prototype.completeVisualOption.apply(this,arguments)},e.prototype._resetSelected=function(t,r){var i=this.option,n=this._pieceList,o=(r?i:t).selected||{};if(i.selected=o,w(n,function(l,u){var h=this.getSelectedMapKey(l);o.hasOwnProperty(h)||(o[h]=!0)},this),i.selectedMode==="single"){var s=!1;w(n,function(l,u){var h=this.getSelectedMapKey(l);o[h]&&(s?o[h]=!1:s=!0)},this)}},e.prototype.getItemSymbol=function(){return this.get("itemSymbol")},e.prototype.getSelectedMapKey=function(t){return this._mode==="categories"?t.value+"":t.index+""},e.prototype.getPieceList=function(){return this._pieceList},e.prototype._determineMode=function(){var t=this.option;return t.pieces&&t.pieces.length>0?"pieces":this.option.categories?"categories":"splitNumber"},e.prototype.setSelected=function(t){this.option.selected=U(t)},e.prototype.getValueState=function(t){var r=J.findPieceIndex(t,this._pieceList);return r!=null&&this.option.selected[this.getSelectedMapKey(this._pieceList[r])]?"inRange":"outOfRange"},e.prototype.findTargetDataIndices=function(t){var r=[],i=this._pieceList;return this.eachTargetSeries(function(n){var o=[],s=n.getData();s.each(this.getDataDimensionIndex(s),function(l,u){var h=J.findPieceIndex(l,i);h===t&&o.push(u)},this),r.push({seriesId:n.id,dataIndex:o})},this),r},e.prototype.getRepresentValue=function(t){var r;if(this.isCategory())r=t.value;else if(t.value!=null)r=t.value;else{var i=t.interval||[];r=i[0]===-1/0&&i[1]===1/0?0:(i[0]+i[1])/2}return r},e.prototype.getVisualMeta=function(t){if(this.isCategory())return;var r=[],i=["",""],n=this;function o(h,v){var c=n.getRepresentValue({interval:h});v||(v=n.getValueState(c));var f=t(c,v);h[0]===-1/0?i[0]=f:h[1]===1/0?i[1]=f:r.push({value:h[0],color:f},{value:h[1],color:f})}var s=this._pieceList.slice();if(!s.length)s.push({interval:[-1/0,1/0]});else{var l=s[0].interval[0];l!==-1/0&&s.unshift({interval:[-1/0,l]}),l=s[s.length-1].interval[1],l!==1/0&&s.push({interval:[l,1/0]})}var u=-1/0;return w(s,function(h){var v=h.interval;v&&(v[0]>u&&o([u,v[0]],"outOfRange"),o(v.slice()),u=v[1])},this),{stops:r,outerColors:i}},e.type="visualMap.piecewise",e.defaultOption=ie(Je.defaultOption,{selected:null,minOpen:!1,maxOpen:!1,align:"auto",itemWidth:20,itemHeight:14,itemSymbol:"roundRect",pieces:null,categories:null,splitNumber:5,selectedMode:"multiple",itemGap:10,hoverLink:!0}),e}(Je),Mf={splitNumber:function(a){var e=this.option,t=Math.min(e.precision,20),r=this.getExtent(),i=e.splitNumber;i=Math.max(parseInt(i,10),1),e.splitNumber=i;for(var n=(r[1]-r[0])/i;+n.toFixed(t)!==n&&t<5;)t++;e.precision=t,n=+n.toFixed(t),e.minOpen&&a.push({interval:[-1/0,r[0]],close:[0,0]});for(var o=0,s=r[0];o<i;s+=n,o++){var l=o===i-1?r[1]:s+n;a.push({interval:[s,l],close:[1,1]})}e.maxOpen&&a.push({interval:[r[1],1/0],close:[0,0]}),ui(a),w(a,function(u,h){u.index=h,u.text=this.formatValueText(u.interval)},this)},categories:function(a){var e=this.option;w(e.categories,function(t){a.push({text:this.formatValueText(t,!0),value:t})},this),qn(e,a)},pieces:function(a){var e=this.option;w(e.pieces,function(t,r){st(t)||(t={value:t});var i={text:"",index:r};if(t.label!=null&&(i.text=t.label),t.hasOwnProperty("value")){var n=i.value=t.value;i.interval=[n,n],i.close=[1,1]}else{for(var o=i.interval=[],s=i.close=[0,0],l=[1,0,1],u=[-1/0,1/0],h=[],v=0;v<2;v++){for(var c=[["gte","gt","min"],["lte","lt","max"]][v],f=0;f<3&&o[v]==null;f++)o[v]=t[c[f]],s[v]=l[f],h[v]=f===2;o[v]==null&&(o[v]=u[v])}h[0]&&o[1]===1/0&&(s[0]=0),h[1]&&o[0]===-1/0&&(s[1]=0),o[0]===o[1]&&s[0]&&s[1]&&(i.value=o[0])}i.visual=J.retrieveVisuals(t),a.push(i)},this),qn(e,a),ui(a),w(a,function(t){var r=t.close,i=[["<","≤"][r[1]],[">","≥"][r[0]]];t.text=t.text||this.formatValueText(t.value!=null?t.value:t.interval,!1,i)},this)}};function qn(a,e){var t=a.inverse;(a.orient==="vertical"?!t:t)&&e.reverse()}var Cf=function(a){I(e,a);function e(){var t=a!==null&&a.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.doRender=function(){var t=this.group;t.removeAll();var r=this.visualMapModel,i=r.get("textGap"),n=r.textStyleModel,o=n.getFont(),s=n.getTextColor(),l=this._getItemAlign(),u=r.itemSize,h=this._getViewData(),v=h.endsText,c=qt(r.get("showLabel",!0),!v),f=!r.get("selectedMode");v&&this._renderEndsText(t,v[0],u,c,l),w(h.viewPieceList,function(p){var d=p.piece,g=new $;g.onclick=R(this._onItemClick,this,d),this._enableHoverLink(g,p.indexInModelPieceList);var y=r.getRepresentValue(d);if(this._createItemSymbol(g,y,[0,0,u[0],u[1]],f),c){var m=this.visualMapModel.getValueState(y);g.add(new tt({style:{x:l==="right"?-i:u[0]+i,y:u[1]/2,text:d.text,verticalAlign:"middle",align:l,font:o,fill:s,opacity:m==="outOfRange"?.5:1},silent:f}))}t.add(g)},this),v&&this._renderEndsText(t,v[1],u,c,l),Hl(r.get("orient"),t,r.get("itemGap")),this.renderBackground(t),this.positionGroup(t)},e.prototype._enableHoverLink=function(t,r){var i=this;t.on("mouseover",function(){return n("highlight")}).on("mouseout",function(){return n("downplay")});var n=function(o){var s=i.visualMapModel;s.option.hoverLink&&i.api.dispatchAction({type:o,batch:Fe(s.findTargetDataIndices(r),s)})}},e.prototype._getItemAlign=function(){var t=this.visualMapModel,r=t.option;if(r.orient==="vertical")return Ms(t,this.api,t.itemSize);var i=r.align;return(!i||i==="auto")&&(i="left"),i},e.prototype._renderEndsText=function(t,r,i,n,o){if(r){var s=new $,l=this.visualMapModel.textStyleModel;s.add(new tt({style:gt(l,{x:n?o==="right"?i[0]:0:i[0]/2,y:i[1]/2,verticalAlign:"middle",align:n?o:"center",text:r})})),t.add(s)}},e.prototype._getViewData=function(){var t=this.visualMapModel,r=E(t.getPieceList(),function(s,l){return{piece:s,indexInModelPieceList:l}}),i=t.get("text"),n=t.get("orient"),o=t.get("inverse");return(n==="horizontal"?o:!o)?r.reverse():i&&(i=i.slice().reverse()),{viewPieceList:r,endsText:i}},e.prototype._createItemSymbol=function(t,r,i,n){var o=yt(this.getControllerVisual(r,"symbol"),i[0],i[1],i[2],i[3],this.getControllerVisual(r,"color"));o.silent=n,t.add(o)},e.prototype._onItemClick=function(t){var r=this.visualMapModel,i=r.option,n=i.selectedMode;if(n){var o=U(i.selected),s=r.getSelectedMapKey(t);n==="single"||n===!0?(o[s]=!0,w(o,function(l,u){o[u]=u===s})):o[s]=!o[s],this.api.dispatchAction({type:"selectDataRange",from:this.uid,visualMapId:this.visualMapModel.id,selected:o})}},e.type="visualMap.piecewise",e}(ws);function Df(a){a.registerComponentModel(wf),a.registerComponentView(Cf),Cs(a)}function Zf(a){kt(Af),kt(Df)}const Tf="path://M18.1 10.7V9.3c-.3-4.9-4.4-8.8-9.4-8.8-5 0-9.1 3.9-9.4 8.8v1.3c.3 4.9 4.4 8.8 9.4 8.8C13.7 19.5 17.8 15.6 18.1 10.7zM5.6 13.3V6.7H7v6.6H5.6zM10.4 13.3V6.7h1.4v6.6H10.4z",Uf={show:!0,type:"slider",handleIcon:Tf,handleSize:"80%"},Xf={left:"55",right:"30",bottom:80};export{gc as $,ql as A,Ph as B,Fh as C,Li as D,Da as E,_h as F,zo as G,ne as H,Ma as I,qr as J,pu as K,Ah as L,lv as M,Ef as N,xv as O,Rr as P,ua as Q,fh as R,Ca as S,ov as T,sv as U,J as V,nh as W,ih as X,Cn as Y,Nf as Z,ys as _,Vf as a,pt as a0,le as a1,Ut as a2,bs as a3,Ke as a4,je as a5,Ic as a6,xs as a7,jc as a8,of as a9,Af as aa,Zh as ab,Of as b,Hf as c,Uf as d,Xf as e,Zf as f,Pf as g,Gf as h,If as i,Bf as j,Df as k,Wf as l,Ff as m,Rf as n,kf as o,zf as p,ju as q,Lo as r,eh as s,dh as t,ko as u,sr as v,xi as w,oe as x,Wh as y,Hh as z};
//# sourceMappingURL=chartZoom-DB0tK3Do.js.map
