import{d as Ws,j as ee,K as Us,a$ as sv,r as $s,l as Hr,k as We,a as Se,f as nt,t as lv,u as uv,b5 as ge,o as vv,c as xa,e as ba,b as Qt,z as Pr,g as cv,h as Qe,y as hv,x as wa,D as Si,G as fv}from"./index-Cwqm8wBn.js";import{s as pv,a as dv}from"./index-DhBpwJns.js";import{w as xi,_ as G,f as gv,m as nn,g as Ys,h as fr,j as M,B as ct,k as Zs,l as Xs,n as F,o as Rt,p as yv,q,P as Ee,r as Re,s as Ht,G as X,t as Ze,v as ht,x as j,y as Wt,Z as ce,z as W,A as Ut,C as $t,D as Vt,F as dt,H as gt,I as wr,J as qs,K as De,L as it,M as la,N as mv,O as Nt,S as St,Q as qt,R as ot,T as st,U as Lt,V as Ve,W as he,X as Ce,Y as _r,$ as js,a0 as Et,a1 as Ne,a2 as z,a3 as bi,a4 as jt,u as $,a5 as Dt,a6 as At,a7 as xt,a8 as Ks,a9 as Sv,aa as xv,ab as re,ac as bv,ad as Js,ae as wv,af as Cn,ag as Qs,ah as pr,ai as Ar,aj as J,ak as Ur,al as _v,am as fe,an as tl,ao as dr,ap as Xe,aq as Ln,ar as ft,as as Av,at as Tv,au as Tr,av as el,aw as bt,ax as rl,ay as ir,az as Iv,aA as mt,aB as oe,aC as Ke,aD as al,aE as Le,aF as pe,aG as Pn,aH as Dv,aI as Cv,aJ as Lv,aK as ua,aL as $r,aM as Pv,aN as Yr,aO as Mv,aP as Gt,aQ as va,aR as Mn,aS as Pe,aT as Ge,aU as qe,aV as on,aW as Ft,aX as Ev,aY as Rv,aZ as Yt,a_ as Bt,a$ as je,b0 as nl,b1 as wi,b2 as Mr,b3 as _i,b4 as il,b5 as Vv,b6 as ol,b7 as Nv,b8 as Ai,b9 as Ir,ba as se,bb as En,bc as Gv,bd as kv,be as or,bf as Ti,bg as sn,bh as zv,bi as Mt,bj as Ov,bk as sl,bl as Bv,bm as ll,bn as Fv,bo as Hv,bp as Me,bq as ul,br as Ii,bs as Wv,bt as Uv,bu as $v,bv as ae,bw as Yv,bx as Zv,by as Rn,bz as vl,bA as Xv,bB as Vn,bC as Zr,bD as cl,bE as qv,bF as Nn,bG as hl,bH as fl,bI as ln,bJ as pl,bK as jv,bL as sr,bM as Kv,bN as Di,bO as Jv,bP as Qv,bQ as Ci,bR as dl,bS as tc,bT as ec,bU as rc,bV as ac,bW as nc,bX as Li,bY as ic,bZ as Er,b_ as oc,b$ as sc,c0 as lc,c1 as Pi,c2 as uc,c3 as vc,c4 as cc,c5 as hc,c6 as fc,c7 as gl,c8 as pc,c9 as yl,ca as Mi,cb as ml,cc as dc,cd as Ei,ce as Dr,cf as gc,e as Gn,cg as yc,ch as mc,ci as Sc,cj as xc,ck as Rr,cl as bc,cm as ne,cn as gr,co as wc,cp as Ri,cq as _c,cr as Ac,cs as Tc,ct as Sl,cu as xl,cv as bl,cw as Ic,cx as ca,cy as Dc,cz as Cc,cA as Lc,cB as Pc,cC as Mc,cD as Ec,cE as Rc,cF as Vc,cG as Nc,cH as Gc,cI as kc,cJ as zc,cK as Oc,cL as wl,cM as kn,cN as Bc,cO as Fc,cP as Hc,cQ as Vi,cR as Wc,cS as Xr,cT as zn,cU as Uc,cV as On,cW as _a,cX as $c,i as _l,d as Al,c as Tl,b as Il,a as Dl,cY as Yc,E as Zc}from"./installCanvasRenderer-SA1tPojE.js";import{A as ha,q as Xc,r as Jt,s as qc,R as fa,t as Bn,S as Cl,V as Ll,u as Fn,L as Pl,v as Cr,w as Ni,x as jc,B as Ml,y as Kc,z as Jc,C as Qc,D as qr,E as El,F as Rl,G as Hn,H as th,I as un,J as Gi,K as eh,M as Wn,N as ki,O as rh,P as ah,Q as nh,T as ih,U as oh,W as sh,X as lh,Y as uh,Z as vh,_ as ch,$ as hh,a0 as fh,a1 as ph,a2 as Vl,a3 as dh,a4 as gh,a5 as yh,a6 as mh,a7 as Sh,i as Nl,o as Gl,g as xh,n as bh,b as kl,a as wh,p as _h,j as Ah,h as Th,m as Ih,l as Dh,c as Ch,a8 as Lh,a9 as Ph,f as zl,aa as Mh,k as Eh}from"./chartZoom-DB0tK3Do.js";import{L as pa,d as da,s as Rh,g as Vh,i as Nh,a as Gh}from"./install-_krYdrE6.js";import{d as kh,i as Ol}from"./TradeList.vue_vue_type_script_setup_true_lang--83SPrIm.js";import{s as zh}from"./index-D6LFPO4a.js";var Oh=1e-8;function zi(r,e){return Math.abs(r-e)<Oh}function Te(r,e,t){var a=0,n=r[0];if(!n)return!1;for(var i=1;i<r.length;i++){var o=r[i];a+=xi(n[0],n[1],o[0],o[1],e,t),n=o}var s=r[0];return(!zi(n[0],s[0])||!zi(n[1],s[1]))&&(a+=xi(n[0],n[1],s[0],s[1],e,t)),a!==0}var Bh=[];function Aa(r,e){for(var t=0;t<r.length;t++)fr(r[t],r[t],e)}function Oi(r,e,t,a){for(var n=0;n<r.length;n++){var i=r[n];a&&(i=a.project(i)),i&&isFinite(i[0])&&isFinite(i[1])&&(Zs(e,e,i),Xs(t,t,i))}}function Fh(r){for(var e=0,t=0,a=0,n=r.length,i=r[n-1][0],o=r[n-1][1],s=0;s<n;s++){var l=r[s][0],u=r[s][1],v=i*u-l*o;e+=v,t+=(i+l)*v,a+=(o+u)*v,i=l,o=u}return e?[t/e/3,a/e/3,e]:[r[0][0]||0,r[0][1]||0]}var Bl=function(){function r(e){this.name=e}return r.prototype.setCenter=function(e){this._center=e},r.prototype.getCenter=function(){var e=this._center;return e||(e=this._center=this.calcCenter()),e},r}(),Bi=function(){function r(e,t){this.type="polygon",this.exterior=e,this.interiors=t}return r}(),Fi=function(){function r(e){this.type="linestring",this.points=e}return r}(),Fl=function(r){G(e,r);function e(t,a,n){var i=r.call(this,t)||this;return i.type="geoJSON",i.geometries=a,i._center=n&&[n[0],n[1]],i}return e.prototype.calcCenter=function(){for(var t=this.geometries,a,n=0,i=0;i<t.length;i++){var o=t[i],s=o.exterior,l=s&&s.length;l>n&&(a=o,n=l)}if(a)return Fh(a.exterior);var u=this.getBoundingRect();return[u.x+u.width/2,u.y+u.height/2]},e.prototype.getBoundingRect=function(t){var a=this._rect;if(a&&!t)return a;var n=[1/0,1/0],i=[-1/0,-1/0],o=this.geometries;return M(o,function(s){s.type==="polygon"?Oi(s.exterior,n,i,t):M(s.points,function(l){Oi(l,n,i,t)})}),isFinite(n[0])&&isFinite(n[1])&&isFinite(i[0])&&isFinite(i[1])||(n[0]=n[1]=i[0]=i[1]=0),a=new ct(n[0],n[1],i[0]-n[0],i[1]-n[1]),t||(this._rect=a),a},e.prototype.contain=function(t){var a=this.getBoundingRect(),n=this.geometries;if(!a.contain(t[0],t[1]))return!1;t:for(var i=0,o=n.length;i<o;i++){var s=n[i];if(s.type==="polygon"){var l=s.exterior,u=s.interiors;if(Te(l,t[0],t[1])){for(var v=0;v<(u?u.length:0);v++)if(Te(u[v],t[0],t[1]))continue t;return!0}}}return!1},e.prototype.transformTo=function(t,a,n,i){var o=this.getBoundingRect(),s=o.width/o.height;n?i||(i=n/s):n=s*i;for(var l=new ct(t,a,n,i),u=o.calculateTransform(l),v=this.geometries,c=0;c<v.length;c++){var h=v[c];h.type==="polygon"?(Aa(h.exterior,u),M(h.interiors,function(f){Aa(f,u)})):M(h.points,function(f){Aa(f,u)})}o=this._rect,o.copy(l),this._center=[o.x+o.width/2,o.y+o.height/2]},e.prototype.cloneShallow=function(t){t==null&&(t=this.name);var a=new e(t,this.geometries,this._center);return a._rect=this._rect,a.transformTo=null,a},e}(Bl),Hh=function(r){G(e,r);function e(t,a){var n=r.call(this,t)||this;return n.type="geoSVG",n._elOnlyForCalculate=a,n}return e.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,a=t.getBoundingRect(),n=[a.x+a.width/2,a.y+a.height/2],i=gv(Bh),o=t;o&&!o.isGeoSVGGraphicRoot;)nn(i,o.getLocalTransform(),i),o=o.parent;return Ys(i,i),fr(n,n,i),n},e}(Bl);function Wh(r){if(!r.UTF8Encoding)return r;var e=r,t=e.UTF8Scale;t==null&&(t=1024);var a=e.features;return M(a,function(n){var i=n.geometry,o=i.encodeOffsets,s=i.coordinates;if(o)switch(i.type){case"LineString":i.coordinates=Hl(s,o,t);break;case"Polygon":Ta(s,o,t);break;case"MultiLineString":Ta(s,o,t);break;case"MultiPolygon":M(s,function(l,u){return Ta(l,o[u],t)})}}),e.UTF8Encoding=!1,e}function Ta(r,e,t){for(var a=0;a<r.length;a++)r[a]=Hl(r[a],e[a],t)}function Hl(r,e,t){for(var a=[],n=e[0],i=e[1],o=0;o<r.length;o+=2){var s=r.charCodeAt(o)-64,l=r.charCodeAt(o+1)-64;s=s>>1^-(s&1),l=l>>1^-(l&1),s+=n,l+=i,n=s,i=l,a.push([s/t,l/t])}return a}function Uh(r,e){return r=Wh(r),F(Rt(r.features,function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0}),function(t){var a=t.properties,n=t.geometry,i=[];switch(n.type){case"Polygon":var o=n.coordinates;i.push(new Bi(o[0],o.slice(1)));break;case"MultiPolygon":M(n.coordinates,function(l){l[0]&&i.push(new Bi(l[0],l.slice(1)))});break;case"LineString":i.push(new Fi([n.coordinates]));break;case"MultiLineString":i.push(new Fi(n.coordinates))}var s=new Fl(a[e||"name"],i,a.cp);return s.properties=a,s})}function $h(r){r.eachSeriesByType("radar",function(e){var t=e.getData(),a=[],n=e.coordinateSystem;if(n){var i=n.getIndicatorAxes();M(i,function(o,s){t.each(t.mapDimension(i[s].dim),function(l,u){a[u]=a[u]||[];var v=n.dataToPoint(l,s);a[u][s]=Hi(v)?v:Wi(n)})}),t.each(function(o){var s=yv(a[o],function(l){return Hi(l)})||Wi(n);a[o].push(s.slice()),t.setItemLayout(o,a[o])})}})}function Hi(r){return!isNaN(r[0])&&!isNaN(r[1])}function Wi(r){return[r.cx,r.cy]}function Yh(r){var e=r.polar;if(e){q(e)||(e=[e]);var t=[];M(e,function(a,n){a.indicator?(a.type&&!a.shape&&(a.shape=a.type),r.radar=r.radar||[],q(r.radar)||(r.radar=[r.radar]),r.radar.push(a)):t.push(a)}),r.polar=t}M(r.series,function(a){a&&a.type==="radar"&&a.polarIndex&&(a.radarIndex=a.polarIndex)})}var Zh=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n){var i=t.coordinateSystem,o=this.group,s=t.getData(),l=this._data;function u(h,f){var p=h.getItemVisual(f,"symbol")||"circle";if(p!=="none"){var d=qs(h.getItemVisual(f,"symbolSize")),g=De(p,-1,-1,2,2),S=h.getItemVisual(f,"symbolRotate")||0;return g.attr({style:{strokeNoScale:!0},z2:100,scaleX:d[0]/2,scaleY:d[1]/2,rotation:S*Math.PI/180||0}),g}}function v(h,f,p,d,g,S){p.removeAll();for(var m=0;m<f.length-1;m++){var y=u(d,g);y&&(y.__dimIdx=m,h[m]?(y.setPosition(h[m]),wr[S?"initProps":"updateProps"](y,{x:f[m][0],y:f[m][1]},t,g)):y.setPosition(f[m]),p.add(y))}}function c(h){return F(h,function(f){return[i.cx,i.cy]})}s.diff(l).add(function(h){var f=s.getItemLayout(h);if(f){var p=new Ee,d=new Re,g={shape:{points:f}};p.shape.points=c(f),d.shape.points=c(f),Ht(p,g,t,h),Ht(d,g,t,h);var S=new X,m=new X;S.add(d),S.add(p),S.add(m),v(d.shape.points,f,m,s,h,!0),s.setItemGraphicEl(h,S)}}).update(function(h,f){var p=l.getItemGraphicEl(f),d=p.childAt(0),g=p.childAt(1),S=p.childAt(2),m={shape:{points:s.getItemLayout(h)}};m.shape.points&&(v(d.shape.points,m.shape.points,S,s,h,!1),Ze(g),Ze(d),ht(d,m,t),ht(g,m,t),s.setItemGraphicEl(h,p))}).remove(function(h){o.remove(l.getItemGraphicEl(h))}).execute(),s.eachItemGraphicEl(function(h,f){var p=s.getItemModel(f),d=h.childAt(0),g=h.childAt(1),S=h.childAt(2),m=s.getItemVisual(f,"style"),y=m.fill;o.add(h),d.useStyle(j(p.getModel("lineStyle").getLineStyle(),{fill:"none",stroke:y})),Wt(d,p,"lineStyle"),Wt(g,p,"areaStyle");var w=p.getModel("areaStyle"),x=w.isEmpty()&&w.parentModel.isEmpty();g.ignore=x,M(["emphasis","select","blur"],function(T){var I=p.getModel([T,"areaStyle"]),A=I.isEmpty()&&I.parentModel.isEmpty();g.ensureState(T).ignore=A&&x}),g.useStyle(j(w.getAreaStyle(),{fill:y,opacity:.7,decal:m.decal}));var b=p.getModel("emphasis"),_=b.getModel("itemStyle").getItemStyle();S.eachChild(function(T){if(T instanceof ce){var I=T.style;T.useStyle(W({image:I.image,x:I.x,y:I.y,width:I.width,height:I.height},m))}else T.useStyle(m),T.setColor(y),T.style.strokeNoScale=!0;var A=T.ensureState("emphasis");A.style=Ut(_);var D=s.getStore().get(s.getDimensionIndex(T.__dimIdx),f);(D==null||isNaN(D))&&(D=""),$t(T,Vt(p),{labelFetcher:s.hostModel,labelDataIndex:f,labelDimIndex:T.__dimIdx,defaultText:D,inheritColor:y,defaultOpacity:m.opacity})}),dt(h,b.get("focus"),b.get("blurScope"),b.get("disabled"))}),this._data=s},e.prototype.remove=function(){this.group.removeAll(),this._data=null},e.type="radar",e}(gt),Xh=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.init=function(t){r.prototype.init.apply(this,arguments),this.legendVisualProvider=new pa(it(this.getData,this),it(this.getRawData,this))},e.prototype.getInitialData=function(t,a){return la(this,{generateCoord:"indicator_",generateCoordCount:1/0})},e.prototype.formatTooltip=function(t,a,n){var i=this.getData(),o=this.coordinateSystem,s=o.getIndicatorAxes(),l=this.getData().getName(t),u=l===""?this.name:l,v=mv(this,t);return Nt("section",{header:u,sortBlocks:!0,blocks:F(s,function(c){var h=i.get(i.mapDimension(c.dim),t);return Nt("nameValue",{markerType:"subItem",markerColor:v,name:c.name,value:h,sortParam:h})})})},e.prototype.getTooltipPosition=function(t){if(t!=null){for(var a=this.getData(),n=this.coordinateSystem,i=a.getValues(F(n.dimensions,function(u){return a.mapDimension(u)}),t),o=0,s=i.length;o<s;o++)if(!isNaN(i[o])){var l=n.getIndicatorAxes();return n.coordToPoint(l[o].dataToCoord(i[o]),o)}}},e.type="series.radar",e.dependencies=["radar"],e.defaultOption={z:2,colorBy:"data",coordinateSystem:"radar",legendHoverLink:!0,radarIndex:0,lineStyle:{width:2,type:"solid",join:"round"},label:{position:"top"},symbolSize:8},e}(St),tr=Xc.value;function Vr(r,e){return j({show:e},r)}var qh=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){var t=this.get("boundaryGap"),a=this.get("splitNumber"),n=this.get("scale"),i=this.get("axisLine"),o=this.get("axisTick"),s=this.get("axisLabel"),l=this.get("axisName"),u=this.get(["axisName","show"]),v=this.get(["axisName","formatter"]),c=this.get("axisNameGap"),h=this.get("triggerEvent"),f=F(this.get("indicator")||[],function(p){p.max!=null&&p.max>0&&!p.min?p.min=0:p.min!=null&&p.min<0&&!p.max&&(p.max=0);var d=l;p.color!=null&&(d=j({color:p.color},l));var g=qt(Ut(p),{boundaryGap:t,splitNumber:a,scale:n,axisLine:i,axisTick:o,axisLabel:s,name:p.text,showName:u,nameLocation:"end",nameGap:c,nameTextStyle:d,triggerEvent:h},!1);if(ot(v)){var S=g.name;g.name=v.replace("{value}",S??"")}else st(v)&&(g.name=v(g.name,g));var m=new Lt(g,null,this.ecModel);return Ve(m,ha.prototype),m.mainType="radar",m.componentIndex=this.componentIndex,m},this);this._indicatorModels=f},e.prototype.getIndicatorModels=function(){return this._indicatorModels},e.type="radar",e.defaultOption={z:0,center:["50%","50%"],radius:"75%",startAngle:90,axisName:{show:!0},boundaryGap:[0,0],splitNumber:5,axisNameGap:15,scale:!1,shape:"polygon",axisLine:qt({lineStyle:{color:"#bbb"}},tr.axisLine),axisLabel:Vr(tr.axisLabel,!1),axisTick:Vr(tr.axisTick,!1),splitLine:Vr(tr.splitLine,!0),splitArea:Vr(tr.splitArea,!0),indicator:[]},e}(he),jh=["axisLine","axisTickLabel","axisName"],Kh=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n){var i=this.group;i.removeAll(),this._buildAxes(t),this._buildSplitLineAndArea(t)},e.prototype._buildAxes=function(t){var a=t.coordinateSystem,n=a.getIndicatorAxes(),i=F(n,function(o){var s=o.model.get("showName")?o.name:"",l=new Ce(o.model,{axisName:s,position:[a.cx,a.cy],rotation:o.angle,labelDirection:-1,tickDirection:-1,nameDirection:1});return l});M(i,function(o){M(jh,o.add,o),this.group.add(o.getGroup())},this)},e.prototype._buildSplitLineAndArea=function(t){var a=t.coordinateSystem,n=a.getIndicatorAxes();if(!n.length)return;var i=t.get("shape"),o=t.getModel("splitLine"),s=t.getModel("splitArea"),l=o.getModel("lineStyle"),u=s.getModel("areaStyle"),v=o.get("show"),c=s.get("show"),h=l.get("color"),f=u.get("color"),p=q(h)?h:[h],d=q(f)?f:[f],g=[],S=[];function m(L,R,V){var N=V%R.length;return L[N]=L[N]||[],N}if(i==="circle")for(var y=n[0].getTicksCoords(),w=a.cx,x=a.cy,b=0;b<y.length;b++){if(v){var _=m(g,p,b);g[_].push(new _r({shape:{cx:w,cy:x,r:y[b].coord}}))}if(c&&b<y.length-1){var _=m(S,d,b);S[_].push(new js({shape:{cx:w,cy:x,r0:y[b].coord,r:y[b+1].coord}}))}}else for(var T,I=F(n,function(L,R){var V=L.getTicksCoords();return T=T==null?V.length-1:Math.min(V.length-1,T),F(V,function(N){return a.coordToPoint(N.coord,R)})}),A=[],b=0;b<=T;b++){for(var D=[],E=0;E<n.length;E++)D.push(I[E][b]);if(D[0]&&D.push(D[0].slice()),v){var _=m(g,p,b);g[_].push(new Re({shape:{points:D}}))}if(c&&A){var _=m(S,d,b-1);S[_].push(new Ee({shape:{points:D.concat(A)}}))}A=D.slice().reverse()}var P=l.getLineStyle(),C=u.getAreaStyle();M(S,function(L,R){this.group.add(Et(L,{style:j({stroke:"none",fill:d[R%d.length]},C),silent:!0}))},this),M(g,function(L,R){this.group.add(Et(L,{style:j({fill:"none",stroke:p[R%p.length]},P),silent:!0}))},this)},e.type="radar",e}(Ne),Jh=function(r){G(e,r);function e(t,a,n){var i=r.call(this,t,a,n)||this;return i.type="value",i.angle=0,i.name="",i}return e}(Jt),Qh=function(){function r(e,t,a){this.dimensions=[],this._model=e,this._indicatorAxes=F(e.getIndicatorModels(),function(n,i){var o="indicator_"+i,s=new Jh(o,new bi);return s.name=n.get("name"),s.model=n,n.axis=s,this.dimensions.push(o),s},this),this.resize(e,a)}return r.prototype.getIndicatorAxes=function(){return this._indicatorAxes},r.prototype.dataToPoint=function(e,t){var a=this._indicatorAxes[t];return this.coordToPoint(a.dataToCoord(e),t)},r.prototype.coordToPoint=function(e,t){var a=this._indicatorAxes[t],n=a.angle,i=this.cx+e*Math.cos(n),o=this.cy-e*Math.sin(n);return[i,o]},r.prototype.pointToData=function(e){var t=e[0]-this.cx,a=e[1]-this.cy,n=Math.sqrt(t*t+a*a);t/=n,a/=n;for(var i=Math.atan2(-a,t),o=1/0,s,l=-1,u=0;u<this._indicatorAxes.length;u++){var v=this._indicatorAxes[u],c=Math.abs(i-v.angle);c<o&&(s=v,l=u,o=c)}return[l,+(s&&s.coordToData(n))]},r.prototype.resize=function(e,t){var a=e.get("center"),n=t.getWidth(),i=t.getHeight(),o=Math.min(n,i)/2;this.cx=z(a[0],n),this.cy=z(a[1],i),this.startAngle=e.get("startAngle")*Math.PI/180;var s=e.get("radius");(ot(s)||jt(s))&&(s=[0,s]),this.r0=z(s[0],o),this.r=z(s[1],o),M(this._indicatorAxes,function(l,u){l.setExtent(this.r0,this.r);var v=this.startAngle+u*Math.PI*2/this._indicatorAxes.length;v=Math.atan2(Math.sin(v),Math.cos(v)),l.angle=v},this)},r.prototype.update=function(e,t){var a=this._indicatorAxes,n=this._model;M(a,function(s){s.scale.setExtent(1/0,-1/0)}),e.eachSeriesByType("radar",function(s,l){if(!(s.get("coordinateSystem")!=="radar"||e.getComponent("radar",s.get("radarIndex"))!==n)){var u=s.getData();M(a,function(v){v.scale.unionExtentFromData(u,u.mapDimension(v.dim))})}},this);var i=n.get("splitNumber"),o=new bi;o.setExtent(0,i),o.setInterval(1),M(a,function(s,l){qc(s.scale,s.model,o)})},r.prototype.convertToPixel=function(e,t,a){return console.warn("Not implemented."),null},r.prototype.convertFromPixel=function(e,t,a){return console.warn("Not implemented."),null},r.prototype.containPoint=function(e){return console.warn("Not implemented."),!1},r.create=function(e,t){var a=[];return e.eachComponent("radar",function(n){var i=new r(n,e,t);a.push(i),n.coordinateSystem=i}),e.eachSeriesByType("radar",function(n){n.get("coordinateSystem")==="radar"&&(n.coordinateSystem=a[n.get("radarIndex")||0])}),a},r.dimensions=[],r}();function tf(r){r.registerCoordinateSystem("radar",Qh),r.registerComponentModel(qh),r.registerComponentView(Kh),r.registerVisual({seriesType:"radar",reset:function(e){var t=e.getData();t.each(function(a){t.setItemVisual(a,"legendIcon","roundRect")}),t.setVisual("legendIcon","roundRect")}})}function ef(r){$(tf),r.registerChartView(Zh),r.registerSeriesModel(Xh),r.registerLayout($h),r.registerProcessor(da("radar")),r.registerPreprocessor(Yh)}function Un(r,e,t){var a=r.target;a.x+=e,a.y+=t,a.dirty()}function $n(r,e,t,a){var n=r.target,i=r.zoomLimit,o=r.zoom=r.zoom||1;if(o*=e,i){var s=i.min||0,l=i.max||1/0;o=Math.max(Math.min(l,o),s)}var u=o/r.zoom;r.zoom=o,n.x-=(t-n.x)*(u-1),n.y-=(a-n.y)*(u-1),n.scaleX*=u,n.scaleY*=u,n.dirty()}function Wl(r){if(ot(r)){var e=new DOMParser;r=e.parseFromString(r,"text/xml")}var t=r;for(t.nodeType===9&&(t=t.firstChild);t.nodeName.toLowerCase()!=="svg"||t.nodeType!==1;)t=t.nextSibling;return t}var Ia,jr={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},Ui=Dt(jr),Kr={"alignment-baseline":"textBaseline","stop-color":"stopColor"},$i=Dt(Kr),rf=function(){function r(){this._defs={},this._root=null}return r.prototype.parse=function(e,t){t=t||{};var a=Wl(e);this._defsUsePending=[];var n=new X;this._root=n;var i=[],o=a.getAttribute("viewBox")||"",s=parseFloat(a.getAttribute("width")||t.width),l=parseFloat(a.getAttribute("height")||t.height);isNaN(s)&&(s=null),isNaN(l)&&(l=null),_t(a,n,null,!0,!1);for(var u=a.firstChild;u;)this._parseNode(u,n,i,null,!1,!1),u=u.nextSibling;of(this._defs,this._defsUsePending),this._defsUsePending=[];var v,c;if(o){var h=ga(o);h.length>=4&&(v={x:parseFloat(h[0]||0),y:parseFloat(h[1]||0),width:parseFloat(h[2]),height:parseFloat(h[3])})}if(v&&s!=null&&l!=null&&(c=$l(v,{x:0,y:0,width:s,height:l}),!t.ignoreViewBox)){var f=n;n=new X,n.add(f),f.scaleX=f.scaleY=c.scale,f.x=c.x,f.y=c.y}return!t.ignoreRootClip&&s!=null&&l!=null&&n.setClipPath(new At({shape:{x:0,y:0,width:s,height:l}})),{root:n,width:s,height:l,viewBoxRect:v,viewBoxTransform:c,named:i}},r.prototype._parseNode=function(e,t,a,n,i,o){var s=e.nodeName.toLowerCase(),l,u=n;if(s==="defs"&&(i=!0),s==="text"&&(o=!0),s==="defs"||s==="switch")l=t;else{if(!i){var v=Ia[s];if(v&&xt(Ia,s)){l=v.call(this,e,t);var c=e.getAttribute("name");if(c){var h={name:c,namedFrom:null,svgNodeTagLower:s,el:l};a.push(h),s==="g"&&(u=h)}else n&&a.push({name:n.name,namedFrom:n,svgNodeTagLower:s,el:l});t.add(l)}}var f=Yi[s];if(f&&xt(Yi,s)){var p=f.call(this,e),d=e.getAttribute("id");d&&(this._defs[d]=p)}}if(l&&l.isGroup)for(var g=e.firstChild;g;)g.nodeType===1?this._parseNode(g,l,a,u,i,o):g.nodeType===3&&o&&this._parseText(g,l),g=g.nextSibling},r.prototype._parseText=function(e,t){var a=new Ks({style:{text:e.textContent},silent:!0,x:this._textX||0,y:this._textY||0});Tt(t,a),_t(e,a,this._defsUsePending,!1,!1),af(a,t);var n=a.style,i=n.fontSize;i&&i<9&&(n.fontSize=9,a.scaleX*=i/9,a.scaleY*=i/9);var o=(n.fontSize||n.fontFamily)&&[n.fontStyle,n.fontWeight,(n.fontSize||12)+"px",n.fontFamily||"sans-serif"].join(" ");n.font=o;var s=a.getBoundingRect();return this._textX+=s.width,t.add(a),a},r.internalField=function(){Ia={g:function(e,t){var a=new X;return Tt(t,a),_t(e,a,this._defsUsePending,!1,!1),a},rect:function(e,t){var a=new At;return Tt(t,a),_t(e,a,this._defsUsePending,!1,!1),a.setShape({x:parseFloat(e.getAttribute("x")||"0"),y:parseFloat(e.getAttribute("y")||"0"),width:parseFloat(e.getAttribute("width")||"0"),height:parseFloat(e.getAttribute("height")||"0")}),a.silent=!0,a},circle:function(e,t){var a=new _r;return Tt(t,a),_t(e,a,this._defsUsePending,!1,!1),a.setShape({cx:parseFloat(e.getAttribute("cx")||"0"),cy:parseFloat(e.getAttribute("cy")||"0"),r:parseFloat(e.getAttribute("r")||"0")}),a.silent=!0,a},line:function(e,t){var a=new re;return Tt(t,a),_t(e,a,this._defsUsePending,!1,!1),a.setShape({x1:parseFloat(e.getAttribute("x1")||"0"),y1:parseFloat(e.getAttribute("y1")||"0"),x2:parseFloat(e.getAttribute("x2")||"0"),y2:parseFloat(e.getAttribute("y2")||"0")}),a.silent=!0,a},ellipse:function(e,t){var a=new xv;return Tt(t,a),_t(e,a,this._defsUsePending,!1,!1),a.setShape({cx:parseFloat(e.getAttribute("cx")||"0"),cy:parseFloat(e.getAttribute("cy")||"0"),rx:parseFloat(e.getAttribute("rx")||"0"),ry:parseFloat(e.getAttribute("ry")||"0")}),a.silent=!0,a},polygon:function(e,t){var a=e.getAttribute("points"),n;a&&(n=qi(a));var i=new Ee({shape:{points:n||[]},silent:!0});return Tt(t,i),_t(e,i,this._defsUsePending,!1,!1),i},polyline:function(e,t){var a=e.getAttribute("points"),n;a&&(n=qi(a));var i=new Re({shape:{points:n||[]},silent:!0});return Tt(t,i),_t(e,i,this._defsUsePending,!1,!1),i},image:function(e,t){var a=new ce;return Tt(t,a),_t(e,a,this._defsUsePending,!1,!1),a.setStyle({image:e.getAttribute("xlink:href")||e.getAttribute("href"),x:+e.getAttribute("x"),y:+e.getAttribute("y"),width:+e.getAttribute("width"),height:+e.getAttribute("height")}),a.silent=!0,a},text:function(e,t){var a=e.getAttribute("x")||"0",n=e.getAttribute("y")||"0",i=e.getAttribute("dx")||"0",o=e.getAttribute("dy")||"0";this._textX=parseFloat(a)+parseFloat(i),this._textY=parseFloat(n)+parseFloat(o);var s=new X;return Tt(t,s),_t(e,s,this._defsUsePending,!1,!0),s},tspan:function(e,t){var a=e.getAttribute("x"),n=e.getAttribute("y");a!=null&&(this._textX=parseFloat(a)),n!=null&&(this._textY=parseFloat(n));var i=e.getAttribute("dx")||"0",o=e.getAttribute("dy")||"0",s=new X;return Tt(t,s),_t(e,s,this._defsUsePending,!1,!0),this._textX+=parseFloat(i),this._textY+=parseFloat(o),s},path:function(e,t){var a=e.getAttribute("d")||"",n=Sv(a);return Tt(t,n),_t(e,n,this._defsUsePending,!1,!1),n.silent=!0,n}}}(),r}(),Yi={lineargradient:function(r){var e=parseInt(r.getAttribute("x1")||"0",10),t=parseInt(r.getAttribute("y1")||"0",10),a=parseInt(r.getAttribute("x2")||"10",10),n=parseInt(r.getAttribute("y2")||"0",10),i=new Js(e,t,a,n);return Zi(r,i),Xi(r,i),i},radialgradient:function(r){var e=parseInt(r.getAttribute("cx")||"0",10),t=parseInt(r.getAttribute("cy")||"0",10),a=parseInt(r.getAttribute("r")||"0",10),n=new bv(e,t,a);return Zi(r,n),Xi(r,n),n}};function Zi(r,e){var t=r.getAttribute("gradientUnits");t==="userSpaceOnUse"&&(e.global=!0)}function Xi(r,e){for(var t=r.firstChild;t;){if(t.nodeType===1&&t.nodeName.toLocaleLowerCase()==="stop"){var a=t.getAttribute("offset"),n=void 0;a&&a.indexOf("%")>0?n=parseInt(a,10)/100:a?n=parseFloat(a):n=0;var i={};Ul(t,i,i);var o=i.stopColor||t.getAttribute("stop-color")||"#000000";e.colorStops.push({offset:n,color:o})}t=t.nextSibling}}function Tt(r,e){r&&r.__inheritedStyle&&(e.__inheritedStyle||(e.__inheritedStyle={}),j(e.__inheritedStyle,r.__inheritedStyle))}function qi(r){for(var e=ga(r),t=[],a=0;a<e.length;a+=2){var n=parseFloat(e[a]),i=parseFloat(e[a+1]);t.push([n,i])}return t}function _t(r,e,t,a,n){var i=e,o=i.__inheritedStyle=i.__inheritedStyle||{},s={};r.nodeType===1&&(uf(r,e),Ul(r,o,s),a||vf(r,o,s)),i.style=i.style||{},o.fill!=null&&(i.style.fill=ji(i,"fill",o.fill,t)),o.stroke!=null&&(i.style.stroke=ji(i,"stroke",o.stroke,t)),M(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(l){o[l]!=null&&(i.style[l]=parseFloat(o[l]))}),M(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],function(l){o[l]!=null&&(i.style[l]=o[l])}),n&&(i.__selfStyle=s),o.lineDash&&(i.style.lineDash=F(ga(o.lineDash),function(l){return parseFloat(l)})),(o.visibility==="hidden"||o.visibility==="collapse")&&(i.invisible=!0),o.display==="none"&&(i.ignore=!0)}function af(r,e){var t=e.__selfStyle;if(t){var a=t.textBaseline,n=a;!a||a==="auto"||a==="baseline"?n="alphabetic":a==="before-edge"||a==="text-before-edge"?n="top":a==="after-edge"||a==="text-after-edge"?n="bottom":(a==="central"||a==="mathematical")&&(n="middle"),r.style.textBaseline=n}var i=e.__inheritedStyle;if(i){var o=i.textAlign,s=o;o&&(o==="middle"&&(s="center"),r.style.textAlign=s)}}var nf=/^url\(\s*#(.*?)\)/;function ji(r,e,t,a){var n=t&&t.match(nf);if(n){var i=wv(n[1]);a.push([r,e,i]);return}return t==="none"&&(t=null),t}function of(r,e){for(var t=0;t<e.length;t++){var a=e[t];a[0].style[a[1]]=r[a[2]]}}var sf=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function ga(r){return r.match(sf)||[]}var lf=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,Da=Math.PI/180;function uf(r,e){var t=r.getAttribute("transform");if(t){t=t.replace(/,/g," ");var a=[],n=null;t.replace(lf,function(c,h,f){return a.push(h,f),""});for(var i=a.length-1;i>0;i-=2){var o=a[i],s=a[i-1],l=ga(o);switch(n=n||Ar(),s){case"translate":pr(n,n,[parseFloat(l[0]),parseFloat(l[1]||"0")]);break;case"scale":Qs(n,n,[parseFloat(l[0]),parseFloat(l[1]||l[0])]);break;case"rotate":Cn(n,n,-parseFloat(l[0])*Da,[parseFloat(l[1]||"0"),parseFloat(l[2]||"0")]);break;case"skewX":var u=Math.tan(parseFloat(l[0])*Da);nn(n,[1,0,u,1,0,0],n);break;case"skewY":var v=Math.tan(parseFloat(l[0])*Da);nn(n,[1,v,0,1,0,0],n);break;case"matrix":n[0]=parseFloat(l[0]),n[1]=parseFloat(l[1]),n[2]=parseFloat(l[2]),n[3]=parseFloat(l[3]),n[4]=parseFloat(l[4]),n[5]=parseFloat(l[5]);break}}e.setLocalTransform(n)}}var Ki=/([^\s:;]+)\s*:\s*([^:;]+)/g;function Ul(r,e,t){var a=r.getAttribute("style");if(a){Ki.lastIndex=0;for(var n;(n=Ki.exec(a))!=null;){var i=n[1],o=xt(jr,i)?jr[i]:null;o&&(e[o]=n[2]);var s=xt(Kr,i)?Kr[i]:null;s&&(t[s]=n[2])}}}function vf(r,e,t){for(var a=0;a<Ui.length;a++){var n=Ui[a],i=r.getAttribute(n);i!=null&&(e[jr[n]]=i)}for(var a=0;a<$i.length;a++){var n=$i[a],i=r.getAttribute(n);i!=null&&(t[Kr[n]]=i)}}function $l(r,e){var t=e.width/r.width,a=e.height/r.height,n=Math.min(t,a);return{scale:n,x:-(r.x+r.width/2)*n+(e.x+e.width/2),y:-(r.y+r.height/2)*n+(e.y+e.height/2)}}function cf(r,e){var t=new rf;return t.parse(r,e)}var hf=J(["rect","circle","line","ellipse","polygon","polyline","path","text","tspan","g"]),ff=function(){function r(e,t){this.type="geoSVG",this._usedGraphicMap=J(),this._freedGraphics=[],this._mapName=e,this._parsedXML=Wl(t)}return r.prototype.load=function(){var e=this._firstGraphic;if(!e){e=this._firstGraphic=this._buildGraphic(this._parsedXML),this._freedGraphics.push(e),this._boundingRect=this._firstGraphic.boundingRect.clone();var t=df(e.named),a=t.regions,n=t.regionsMap;this._regions=a,this._regionsMap=n}return{boundingRect:this._boundingRect,regions:this._regions,regionsMap:this._regionsMap}},r.prototype._buildGraphic=function(e){var t,a;try{t=e&&cf(e,{ignoreViewBox:!0,ignoreRootClip:!0})||{},a=t.root,Ur(a!=null)}catch(g){throw new Error(`Invalid svg format
`+g.message)}var n=new X;n.add(a),n.isGeoSVGGraphicRoot=!0;var i=t.width,o=t.height,s=t.viewBoxRect,l=this._boundingRect;if(!l){var u=void 0,v=void 0,c=void 0,h=void 0;if(i!=null?(u=0,c=i):s&&(u=s.x,c=s.width),o!=null?(v=0,h=o):s&&(v=s.y,h=s.height),u==null||v==null){var f=a.getBoundingRect();u==null&&(u=f.x,c=f.width),v==null&&(v=f.y,h=f.height)}l=this._boundingRect=new ct(u,v,c,h)}if(s){var p=$l(s,l);a.scaleX=a.scaleY=p.scale,a.x=p.x,a.y=p.y}n.setClipPath(new At({shape:l.plain()}));var d=[];return M(t.named,function(g){hf.get(g.svgNodeTagLower)!=null&&(d.push(g),pf(g.el))}),{root:n,boundingRect:l,named:d}},r.prototype.useGraphic=function(e){var t=this._usedGraphicMap,a=t.get(e);return a||(a=this._freedGraphics.pop()||this._buildGraphic(this._parsedXML),t.set(e,a),a)},r.prototype.freeGraphic=function(e){var t=this._usedGraphicMap,a=t.get(e);a&&(t.removeKey(e),this._freedGraphics.push(a))},r}();function pf(r){r.silent=!1,r.isGroup&&r.traverse(function(e){e.silent=!1})}function df(r){var e=[],t=J();return M(r,function(a){if(a.namedFrom==null){var n=new Hh(a.name,a.el);e.push(n),t.set(a.name,n)}}),{regions:e,regionsMap:t}}var vn=[126,25],Ji="南海诸岛",xe=[[[0,3.5],[7,11.2],[15,11.9],[30,7],[42,.7],[52,.7],[56,7.7],[59,.7],[64,.7],[64,0],[5,0],[0,3.5]],[[13,16.1],[19,14.7],[16,21.7],[11,23.1],[13,16.1]],[[12,32.2],[14,38.5],[15,38.5],[13,32.2],[12,32.2]],[[16,47.6],[12,53.2],[13,53.2],[18,47.6],[16,47.6]],[[6,64.4],[8,70],[9,70],[8,64.4],[6,64.4]],[[23,82.6],[29,79.8],[30,79.8],[25,82.6],[23,82.6]],[[37,70.7],[43,62.3],[44,62.3],[39,70.7],[37,70.7]],[[48,51.1],[51,45.5],[53,45.5],[50,51.1],[48,51.1]],[[51,35],[51,28.7],[53,28.7],[53,35],[51,35]],[[52,22.4],[55,17.5],[56,17.5],[53,22.4],[52,22.4]],[[58,12.6],[62,7],[63,7],[60,12.6],[58,12.6]],[[0,3.5],[0,93.1],[64,93.1],[64,0],[63,0],[63,92.4],[1,92.4],[1,3.5],[0,3.5]]];for(var ye=0;ye<xe.length;ye++)for(var ke=0;ke<xe[ye].length;ke++)xe[ye][ke][0]/=10.5,xe[ye][ke][1]/=-10.5/.75,xe[ye][ke][0]+=vn[0],xe[ye][ke][1]+=vn[1];function gf(r,e){if(r==="china"){for(var t=0;t<e.length;t++)if(e[t].name===Ji)return;e.push(new Fl(Ji,F(xe,function(a){return{type:"polygon",exterior:a}}),vn))}}var yf={南海诸岛:[32,80],广东:[0,-10],香港:[10,5],澳门:[-10,10],天津:[5,5]};function mf(r,e){if(r==="china"){var t=yf[e.name];if(t){var a=e.getCenter();a[0]+=t[0]/10.5,a[1]+=-t[1]/(10.5/.75),e.setCenter(a)}}}var Sf=[[[123.45165252685547,25.73527164402261],[123.49731445312499,25.73527164402261],[123.49731445312499,25.750734064600884],[123.45165252685547,25.750734064600884],[123.45165252685547,25.73527164402261]]];function xf(r,e){r==="china"&&e.name==="台湾"&&e.geometries.push({type:"polygon",exterior:Sf[0]})}var bf="name",wf=function(){function r(e,t,a){this.type="geoJSON",this._parsedMap=J(),this._mapName=e,this._specialAreas=a,this._geoJSON=Af(t)}return r.prototype.load=function(e,t){t=t||bf;var a=this._parsedMap.get(t);if(!a){var n=this._parseToRegions(t);a=this._parsedMap.set(t,{regions:n,boundingRect:_f(n)})}var i=J(),o=[];return M(a.regions,function(s){var l=s.name;e&&xt(e,l)&&(s=s.cloneShallow(l=e[l])),o.push(s),i.set(l,s)}),{regions:o,boundingRect:a.boundingRect||new ct(0,0,0,0),regionsMap:i}},r.prototype._parseToRegions=function(e){var t=this._mapName,a=this._geoJSON,n;try{n=a?Uh(a,e):[]}catch(i){throw new Error(`Invalid geoJson format
`+i.message)}return gf(t,n),M(n,function(i){var o=i.name;mf(t,i),xf(t,i);var s=this._specialAreas&&this._specialAreas[o];s&&i.transformTo(s.left,s.top,s.width,s.height)},this),n},r.prototype.getMapForUser=function(){return{geoJson:this._geoJSON,geoJSON:this._geoJSON,specialAreas:this._specialAreas}},r}();function _f(r){for(var e,t=0;t<r.length;t++){var a=r[t].getBoundingRect();e=e||a.clone(),e.union(a)}return e}function Af(r){return ot(r)?typeof JSON<"u"&&JSON.parse?JSON.parse(r):new Function("return ("+r+");")():r}var er=J();const Kt={registerMap:function(r,e,t){if(e.svg){var a=new ff(r,e.svg);er.set(r,a)}else{var n=e.geoJson||e.geoJSON;n&&!e.features?t=e.specialAreas:n=e;var a=new wf(r,n,t);er.set(r,a)}},getGeoResource:function(r){return er.get(r)},getMapForUser:function(r){var e=er.get(r);return e&&e.type==="geoJSON"&&e.getMapForUser()},load:function(r,e,t){var a=er.get(r);if(a)return a.load(e,t)}};var Yn=["rect","circle","line","ellipse","polygon","polyline","path"],Tf=J(Yn),If=J(Yn.concat(["g"])),Df=J(Yn.concat(["g"])),Yl=fe();function Nr(r){var e=r.getItemStyle(),t=r.get("areaColor");return t!=null&&(e.fill=t),e}function Qi(r){var e=r.style;e&&(e.stroke=e.stroke||e.fill,e.fill=null)}var Zl=function(){function r(e){var t=new X;this.uid=_v("ec_map_draw"),this._controller=new fa(e.getZr()),this._controllerHost={target:t},this.group=t,t.add(this._regionsGroup=new X),t.add(this._svgGroup=new X)}return r.prototype.draw=function(e,t,a,n,i){var o=e.mainType==="geo",s=e.getData&&e.getData();o&&t.eachComponent({mainType:"series",subType:"map"},function(S){!s&&S.getHostGeoModel()===e&&(s=S.getData())});var l=e.coordinateSystem,u=this._regionsGroup,v=this.group,c=l.getTransformInfo(),h=c.raw,f=c.roam,p=!u.childAt(0)||i;p?(v.x=f.x,v.y=f.y,v.scaleX=f.scaleX,v.scaleY=f.scaleY,v.dirty()):ht(v,f,e);var d=s&&s.getVisual("visualMeta")&&s.getVisual("visualMeta").length>0,g={api:a,geo:l,mapOrGeoModel:e,data:s,isVisualEncodedByVisualMap:d,isGeo:o,transformInfoRaw:h};l.resourceType==="geoJSON"?this._buildGeoJSON(g):l.resourceType==="geoSVG"&&this._buildSVG(g),this._updateController(e,t,a),this._updateMapSelectHandler(e,u,a,n)},r.prototype._buildGeoJSON=function(e){var t=this._regionsGroupByName=J(),a=J(),n=this._regionsGroup,i=e.transformInfoRaw,o=e.mapOrGeoModel,s=e.data,l=e.geo.projection,u=l&&l.stream;function v(f,p){return p&&(f=p(f)),f&&[f[0]*i.scaleX+i.x,f[1]*i.scaleY+i.y]}function c(f){for(var p=[],d=!u&&l&&l.project,g=0;g<f.length;++g){var S=v(f[g],d);S&&p.push(S)}return p}function h(f){return{shape:{points:c(f)}}}n.removeAll(),M(e.geo.regions,function(f){var p=f.name,d=t.get(p),g=a.get(p)||{},S=g.dataIdx,m=g.regionModel;if(!d){d=t.set(p,new X),n.add(d),S=s?s.indexOfName(p):null,m=e.isGeo?o.getRegionModel(p):s?s.getItemModel(S):null;var y=m.get("silent",!0);y!=null&&(d.silent=y),a.set(p,{dataIdx:S,regionModel:m})}var w=[],x=[];M(f.geometries,function(T){if(T.type==="polygon"){var I=[T.exterior].concat(T.interiors||[]);u&&(I=io(I,u)),M(I,function(D){w.push(new Ee(h(D)))})}else{var A=T.points;u&&(A=io(A,u,!0)),M(A,function(D){x.push(new Re(h(D)))})}});var b=v(f.getCenter(),l&&l.project);function _(T,I){if(T.length){var A=new tl({culling:!0,segmentIgnoreThreshold:1,shape:{paths:T}});d.add(A),to(e,A,S,m),eo(e,A,p,m,o,S,b),I&&(Qi(A),M(A.states,Qi))}}_(w),_(x,!0)}),t.each(function(f,p){var d=a.get(p),g=d.dataIdx,S=d.regionModel;ro(e,f,p,S,o,g),ao(e,f,p,S,o),no(e,f,p,S,o)},this)},r.prototype._buildSVG=function(e){var t=e.geo.map,a=e.transformInfoRaw;this._svgGroup.x=a.x,this._svgGroup.y=a.y,this._svgGroup.scaleX=a.scaleX,this._svgGroup.scaleY=a.scaleY,this._svgResourceChanged(t)&&(this._freeSVG(),this._useSVG(t));var n=this._svgDispatcherMap=J(),i=!1;M(this._svgGraphicRecord.named,function(o){var s=o.name,l=e.mapOrGeoModel,u=e.data,v=o.svgNodeTagLower,c=o.el,h=u?u.indexOfName(s):null,f=l.getRegionModel(s);Tf.get(v)!=null&&c instanceof dr&&to(e,c,h,f),c instanceof dr&&(c.culling=!0);var p=f.get("silent",!0);if(p!=null&&(c.silent=p),c.z2EmphasisLift=0,!o.namedFrom&&(Df.get(v)!=null&&eo(e,c,s,f,l,h,null),ro(e,c,s,f,l,h),ao(e,c,s,f,l),If.get(v)!=null)){var d=no(e,c,s,f,l);d==="self"&&(i=!0);var g=n.get(s)||n.set(s,[]);g.push(c)}},this),this._enableBlurEntireSVG(i,e)},r.prototype._enableBlurEntireSVG=function(e,t){if(e&&t.isGeo){var a=t.mapOrGeoModel.getModel(["blur","itemStyle"]).getItemStyle(),n=a.opacity;this._svgGraphicRecord.root.traverse(function(i){if(!i.isGroup){Xe(i);var o=i.ensureState("blur").style||{};o.opacity==null&&n!=null&&(o.opacity=n),i.ensureState("emphasis")}})}},r.prototype.remove=function(){this._regionsGroup.removeAll(),this._regionsGroupByName=null,this._svgGroup.removeAll(),this._freeSVG(),this._controller.dispose(),this._controllerHost=null},r.prototype.findHighDownDispatchers=function(e,t){if(e==null)return[];var a=t.coordinateSystem;if(a.resourceType==="geoJSON"){var n=this._regionsGroupByName;if(n){var i=n.get(e);return i?[i]:[]}}else if(a.resourceType==="geoSVG")return this._svgDispatcherMap&&this._svgDispatcherMap.get(e)||[]},r.prototype._svgResourceChanged=function(e){return this._svgMapName!==e},r.prototype._useSVG=function(e){var t=Kt.getGeoResource(e);if(t&&t.type==="geoSVG"){var a=t.useGraphic(this.uid);this._svgGroup.add(a.root),this._svgGraphicRecord=a,this._svgMapName=e}},r.prototype._freeSVG=function(){var e=this._svgMapName;if(e!=null){var t=Kt.getGeoResource(e);t&&t.type==="geoSVG"&&t.freeGraphic(this.uid),this._svgGraphicRecord=null,this._svgDispatcherMap=null,this._svgGroup.removeAll(),this._svgMapName=null}},r.prototype._updateController=function(e,t,a){var n=e.coordinateSystem,i=this._controller,o=this._controllerHost;o.zoomLimit=e.get("scaleLimit"),o.zoom=n.getZoom(),i.enable(e.get("roam")||!1);var s=e.mainType;function l(){var u={type:"geoRoam",componentType:s};return u[s+"Id"]=e.id,u}i.off("pan").on("pan",function(u){this._mouseDownFlag=!1,Un(o,u.dx,u.dy),a.dispatchAction(W(l(),{dx:u.dx,dy:u.dy,animation:{duration:0}}))},this),i.off("zoom").on("zoom",function(u){this._mouseDownFlag=!1,$n(o,u.scale,u.originX,u.originY),a.dispatchAction(W(l(),{totalZoom:o.zoom,zoom:u.scale,originX:u.originX,originY:u.originY,animation:{duration:0}}))},this),i.setPointerChecker(function(u,v,c){return n.containPoint([v,c])&&!Bn(u,a,e)})},r.prototype.resetForLabelLayout=function(){this.group.traverse(function(e){var t=e.getTextContent();t&&(t.ignore=Yl(t).ignore)})},r.prototype._updateMapSelectHandler=function(e,t,a,n){var i=this;t.off("mousedown"),t.off("click"),e.get("selectedMode")&&(t.on("mousedown",function(){i._mouseDownFlag=!0}),t.on("click",function(o){i._mouseDownFlag&&(i._mouseDownFlag=!1)}))},r}();function to(r,e,t,a){var n=a.getModel("itemStyle"),i=a.getModel(["emphasis","itemStyle"]),o=a.getModel(["blur","itemStyle"]),s=a.getModel(["select","itemStyle"]),l=Nr(n),u=Nr(i),v=Nr(s),c=Nr(o),h=r.data;if(h){var f=h.getItemVisual(t,"style"),p=h.getItemVisual(t,"decal");r.isVisualEncodedByVisualMap&&f.fill&&(l.fill=f.fill),p&&(l.decal=Ln(p,r.api))}e.setStyle(l),e.style.strokeNoScale=!0,e.ensureState("emphasis").style=u,e.ensureState("select").style=v,e.ensureState("blur").style=c,Xe(e)}function eo(r,e,t,a,n,i,o){var s=r.data,l=r.isGeo,u=s&&isNaN(s.get(s.mapDimension("value"),i)),v=s&&s.getItemLayout(i);if(l||u||v&&v.showLabel){var c=l?t:i,h=void 0;(!s||i>=0)&&(h=n);var f=o?{normal:{align:"center",verticalAlign:"middle"}}:null;$t(e,Vt(a),{labelFetcher:h,labelDataIndex:c,defaultText:t},f);var p=e.getTextContent();if(p&&(Yl(p).ignore=p.ignore,e.textConfig&&o)){var d=e.getBoundingRect().clone();e.textConfig.layoutRect=d,e.textConfig.position=[(o[0]-d.x)/d.width*100+"%",(o[1]-d.y)/d.height*100+"%"]}e.disableLabelAnimation=!0}else e.removeTextContent(),e.removeTextConfig(),e.disableLabelAnimation=null}function ro(r,e,t,a,n,i){r.data?r.data.setItemGraphicEl(i,e):ft(e).eventData={componentType:"geo",componentIndex:n.componentIndex,geoIndex:n.componentIndex,name:t,region:a&&a.option||{}}}function ao(r,e,t,a,n){r.data||Av({el:e,componentModel:n,itemName:t,itemTooltipOption:a.get("tooltip")})}function no(r,e,t,a,n){e.highDownSilentOnTouch=!!n.get("selectedMode");var i=a.getModel("emphasis"),o=i.get("focus");return dt(e,o,i.get("blurScope"),i.get("disabled")),r.isGeo&&Tv(e,n,t),o}function io(r,e,t){var a=[],n;function i(){n=[]}function o(){n.length&&(a.push(n),n=[])}var s=e({polygonStart:i,polygonEnd:o,lineStart:i,lineEnd:o,point:function(l,u){isFinite(l)&&isFinite(u)&&n.push([l,u])},sphere:function(){}});return!t&&s.polygonStart(),M(r,function(l){s.lineStart();for(var u=0;u<l.length;u++)s.point(l[u][0],l[u][1]);s.lineEnd()}),!t&&s.polygonEnd(),a}var Cf=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n,i){if(!(i&&i.type==="mapToggleSelect"&&i.from===this.uid)){var o=this.group;if(o.removeAll(),!t.getHostGeoModel()){if(this._mapDraw&&i&&i.type==="geoRoam"&&this._mapDraw.resetForLabelLayout(),i&&i.type==="geoRoam"&&i.componentType==="series"&&i.seriesId===t.id){var s=this._mapDraw;s&&o.add(s.group)}else if(t.needsDrawMap){var s=this._mapDraw||new Zl(n);o.add(s.group),s.draw(t,a,n,this,i),this._mapDraw=s}else this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;t.get("showLegendSymbol")&&a.getComponent("legend")&&this._renderSymbols(t,a,n)}}},e.prototype.remove=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null,this.group.removeAll()},e.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null},e.prototype._renderSymbols=function(t,a,n){var i=t.originalData,o=this.group;i.each(i.mapDimension("value"),function(s,l){if(!isNaN(s)){var u=i.getItemLayout(l);if(!(!u||!u.point)){var v=u.point,c=u.offset,h=new _r({style:{fill:t.getData().getVisual("style").fill},shape:{cx:v[0]+c*9,cy:v[1],r:3},silent:!0,z2:8+(c?0:Tr+1)});if(!c){var f=t.mainSeries.getData(),p=i.getName(l),d=f.indexOfName(p),g=i.getItemModel(l),S=g.getModel("label"),m=f.getItemGraphicEl(d);$t(h,Vt(g),{labelFetcher:{getFormattedLabel:function(y,w){return t.getFormattedLabel(d,w)}},defaultText:p}),h.disableLabelAnimation=!0,S.get("position")||h.setTextConfig({position:"bottom"}),m.onHoverStateChange=function(y){el(h,y)}}o.add(h)}}})},e.type="map",e}(gt),Lf=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.needsDrawMap=!1,t.seriesGroup=[],t.getTooltipPosition=function(a){if(a!=null){var n=this.getData().getName(a),i=this.coordinateSystem,o=i.getRegion(n);return o&&i.dataToPoint(o.getCenter())}},t}return e.prototype.getInitialData=function(t){for(var a=la(this,{coordDimensions:["value"],encodeDefaulter:bt(rl,this)}),n=J(),i=[],o=0,s=a.count();o<s;o++){var l=a.getName(o);n.set(l,o)}var u=Kt.load(this.getMapType(),this.option.nameMap,this.option.nameProperty);return M(u.regions,function(v){var c=v.name,h=n.get(c),f=v.properties&&v.properties.echartsStyle,p;h==null?(p={name:c},i.push(p)):p=a.getRawDataItem(h),f&&qt(p,f)}),a.appendData(i),a},e.prototype.getHostGeoModel=function(){var t=this.option.geoIndex;return t!=null?this.ecModel.getComponent("geo",t):null},e.prototype.getMapType=function(){return(this.getHostGeoModel()||this).option.map},e.prototype.getRawValue=function(t){var a=this.getData();return a.get(a.mapDimension("value"),t)},e.prototype.getRegionModel=function(t){var a=this.getData();return a.getItemModel(a.indexOfName(t))},e.prototype.formatTooltip=function(t,a,n){for(var i=this.getData(),o=this.getRawValue(t),s=i.getName(t),l=this.seriesGroup,u=[],v=0;v<l.length;v++){var c=l[v].originalData.indexOfName(s),h=i.mapDimension("value");isNaN(l[v].originalData.get(h,c))||u.push(l[v].name)}return Nt("section",{header:u.join(", "),noHeader:!u.length,blocks:[Nt("nameValue",{name:s,value:o})]})},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.getLegendIcon=function(t){var a=t.icon||"roundRect",n=De(a,0,0,t.itemWidth,t.itemHeight,t.itemStyle.fill);return n.setStyle(t.itemStyle),n.style.stroke="none",a.indexOf("empty")>-1&&(n.style.stroke=n.style.fill,n.style.fill="#fff",n.style.lineWidth=2),n},e.type="series.map",e.dependencies=["geo"],e.layoutMode="box",e.defaultOption={z:2,coordinateSystem:"geo",map:"",left:"center",top:"center",aspectScale:null,showLegendSymbol:!0,boundingCoords:null,center:null,zoom:1,scaleLimit:null,selectedMode:!0,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444",areaColor:"#eee"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{areaColor:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},nameProperty:"name"},e}(St);function Pf(r,e){var t={};return M(r,function(a){a.each(a.mapDimension("value"),function(n,i){var o="ec-"+a.getName(i);t[o]=t[o]||[],isNaN(n)||t[o].push(n)})}),r[0].map(r[0].mapDimension("value"),function(a,n){for(var i="ec-"+r[0].getName(n),o=0,s=1/0,l=-1/0,u=t[i].length,v=0;v<u;v++)s=Math.min(s,t[i][v]),l=Math.max(l,t[i][v]),o+=t[i][v];var c;return e==="min"?c=s:e==="max"?c=l:e==="average"?c=o/u:c=o,u===0?NaN:c})}function Mf(r){var e={};r.eachSeriesByType("map",function(t){var a=t.getHostGeoModel(),n=a?"o"+a.id:"i"+t.getMapType();(e[n]=e[n]||[]).push(t)}),M(e,function(t,a){for(var n=Pf(F(t,function(o){return o.getData()}),t[0].get("mapValueCalculation")),i=0;i<t.length;i++)t[i].originalData=t[i].getData();for(var i=0;i<t.length;i++)t[i].seriesGroup=t,t[i].needsDrawMap=i===0&&!t[i].getHostGeoModel(),t[i].setData(n.cloneShallow()),t[i].mainSeries=t[0]})}function Ef(r){var e={};r.eachSeriesByType("map",function(t){var a=t.getMapType();if(!(t.getHostGeoModel()||e[a])){var n={};M(t.seriesGroup,function(o){var s=o.coordinateSystem,l=o.originalData;o.get("showLegendSymbol")&&r.getComponent("legend")&&l.each(l.mapDimension("value"),function(u,v){var c=l.getName(v),h=s.getRegion(c);if(!(!h||isNaN(u))){var f=n[c]||0,p=s.dataToPoint(h.getCenter());n[c]=f+1,l.setItemLayout(v,{point:p,offset:f})}})});var i=t.getData();i.each(function(o){var s=i.getName(o),l=i.getItemLayout(o)||{};l.showLabel=!n[s],i.setItemLayout(o,l)}),e[a]=!0}})}var oo=fr,Lr=function(r){G(e,r);function e(t){var a=r.call(this)||this;return a.type="view",a.dimensions=["x","y"],a._roamTransformable=new ir,a._rawTransformable=new ir,a.name=t,a}return e.prototype.setBoundingRect=function(t,a,n,i){return this._rect=new ct(t,a,n,i),this._rect},e.prototype.getBoundingRect=function(){return this._rect},e.prototype.setViewRect=function(t,a,n,i){this._transformTo(t,a,n,i),this._viewRect=new ct(t,a,n,i)},e.prototype._transformTo=function(t,a,n,i){var o=this.getBoundingRect(),s=this._rawTransformable;s.transform=o.calculateTransform(new ct(t,a,n,i));var l=s.parent;s.parent=null,s.decomposeTransform(),s.parent=l,this._updateTransform()},e.prototype.setCenter=function(t,a){t&&(this._center=[z(t[0],a.getWidth()),z(t[1],a.getHeight())],this._updateCenterAndZoom())},e.prototype.setZoom=function(t){t=t||1;var a=this.zoomLimit;a&&(a.max!=null&&(t=Math.min(a.max,t)),a.min!=null&&(t=Math.max(a.min,t))),this._zoom=t,this._updateCenterAndZoom()},e.prototype.getDefaultCenter=function(){var t=this.getBoundingRect(),a=t.x+t.width/2,n=t.y+t.height/2;return[a,n]},e.prototype.getCenter=function(){return this._center||this.getDefaultCenter()},e.prototype.getZoom=function(){return this._zoom||1},e.prototype.getRoamTransform=function(){return this._roamTransformable.getLocalTransform()},e.prototype._updateCenterAndZoom=function(){var t=this._rawTransformable.getLocalTransform(),a=this._roamTransformable,n=this.getDefaultCenter(),i=this.getCenter(),o=this.getZoom();i=fr([],i,t),n=fr([],n,t),a.originX=i[0],a.originY=i[1],a.x=n[0]-i[0],a.y=n[1]-i[1],a.scaleX=a.scaleY=o,this._updateTransform()},e.prototype._updateTransform=function(){var t=this._roamTransformable,a=this._rawTransformable;a.parent=t,t.updateTransform(),a.updateTransform(),Iv(this.transform||(this.transform=[]),a.transform||Ar()),this._rawTransform=a.getLocalTransform(),this.invTransform=this.invTransform||[],Ys(this.invTransform,this.transform),this.decomposeTransform()},e.prototype.getTransformInfo=function(){var t=this._rawTransformable,a=this._roamTransformable,n=new ir;return n.transform=a.transform,n.decomposeTransform(),{roam:{x:n.x,y:n.y,scaleX:n.scaleX,scaleY:n.scaleY},raw:{x:t.x,y:t.y,scaleX:t.scaleX,scaleY:t.scaleY}}},e.prototype.getViewRect=function(){return this._viewRect},e.prototype.getViewRectAfterRoam=function(){var t=this.getBoundingRect().clone();return t.applyTransform(this.transform),t},e.prototype.dataToPoint=function(t,a,n){var i=a?this._rawTransform:this.transform;return n=n||[],i?oo(n,t,i):mt(n,t)},e.prototype.pointToData=function(t){var a=this.invTransform;return a?oo([],t,a):[t[0],t[1]]},e.prototype.convertToPixel=function(t,a,n){var i=so(a);return i===this?i.dataToPoint(n):null},e.prototype.convertFromPixel=function(t,a,n){var i=so(a);return i===this?i.pointToData(n):null},e.prototype.containPoint=function(t){return this.getViewRectAfterRoam().contain(t[0],t[1])},e.dimensions=["x","y"],e}(ir);function so(r){var e=r.seriesModel;return e?e.coordinateSystem:null}var Rf={geoJSON:{aspectScale:.75,invertLongitute:!0},geoSVG:{aspectScale:1,invertLongitute:!1}},Xl=["lng","lat"],cn=function(r){G(e,r);function e(t,a,n){var i=r.call(this,t)||this;i.dimensions=Xl,i.type="geo",i._nameCoordMap=J(),i.map=a;var o=n.projection,s=Kt.load(a,n.nameMap,n.nameProperty),l=Kt.getGeoResource(a);i.resourceType=l?l.type:null;var u=i.regions=s.regions,v=Rf[l.type];i._regionsMap=s.regionsMap,i.regions=s.regions,i.projection=o;var c;if(o)for(var h=0;h<u.length;h++){var f=u[h].getBoundingRect(o);c=c||f.clone(),c.union(f)}else c=s.boundingRect;return i.setBoundingRect(c.x,c.y,c.width,c.height),i.aspectScale=o?1:oe(n.aspectScale,v.aspectScale),i._invertLongitute=o?!1:v.invertLongitute,i}return e.prototype._transformTo=function(t,a,n,i){var o=this.getBoundingRect(),s=this._invertLongitute;o=o.clone(),s&&(o.y=-o.y-o.height);var l=this._rawTransformable;l.transform=o.calculateTransform(new ct(t,a,n,i));var u=l.parent;l.parent=null,l.decomposeTransform(),l.parent=u,s&&(l.scaleY=-l.scaleY),this._updateTransform()},e.prototype.getRegion=function(t){return this._regionsMap.get(t)},e.prototype.getRegionByCoord=function(t){for(var a=this.regions,n=0;n<a.length;n++){var i=a[n];if(i.type==="geoJSON"&&i.contain(t))return a[n]}},e.prototype.addGeoCoord=function(t,a){this._nameCoordMap.set(t,a)},e.prototype.getGeoCoord=function(t){var a=this._regionsMap.get(t);return this._nameCoordMap.get(t)||a&&a.getCenter()},e.prototype.dataToPoint=function(t,a,n){if(ot(t)&&(t=this.getGeoCoord(t)),t){var i=this.projection;return i&&(t=i.project(t)),t&&this.projectedToPoint(t,a,n)}},e.prototype.pointToData=function(t){var a=this.projection;return a&&(t=a.unproject(t)),t&&this.pointToProjected(t)},e.prototype.pointToProjected=function(t){return r.prototype.pointToData.call(this,t)},e.prototype.projectedToPoint=function(t,a,n){return r.prototype.dataToPoint.call(this,t,a,n)},e.prototype.convertToPixel=function(t,a,n){var i=lo(a);return i===this?i.dataToPoint(n):null},e.prototype.convertFromPixel=function(t,a,n){var i=lo(a);return i===this?i.pointToData(n):null},e}(Lr);Ve(cn,Lr);function lo(r){var e=r.geoModel,t=r.seriesModel;return e?e.coordinateSystem:t?t.coordinateSystem||(t.getReferringComponents("geo",Ke).models[0]||{}).coordinateSystem:null}function uo(r,e){var t=r.get("boundingCoords");if(t!=null){var a=t[0],n=t[1];if(isFinite(a[0])&&isFinite(a[1])&&isFinite(n[0])&&isFinite(n[1])){var i=this.projection;if(i){var o=a[0],s=a[1],l=n[0],u=n[1];a=[1/0,1/0],n=[-1/0,-1/0];var v=function(b,_,T,I){for(var A=T-b,D=I-_,E=0;E<=100;E++){var P=E/100,C=i.project([b+A*P,_+D*P]);Zs(a,a,C),Xs(n,n,C)}};v(o,s,l,s),v(l,s,l,u),v(l,u,o,u),v(o,u,l,s)}this.setBoundingRect(a[0],a[1],n[0]-a[0],n[1]-a[1])}}var c=this.getBoundingRect(),h=r.get("layoutCenter"),f=r.get("layoutSize"),p=e.getWidth(),d=e.getHeight(),g=c.width/c.height*this.aspectScale,S=!1,m,y;h&&f&&(m=[z(h[0],p),z(h[1],d)],y=z(f,Math.min(p,d)),!isNaN(m[0])&&!isNaN(m[1])&&!isNaN(y)&&(S=!0));var w;if(S)w={},g>1?(w.width=y,w.height=y/g):(w.height=y,w.width=y*g),w.y=m[1]-w.height/2,w.x=m[0]-w.width/2;else{var x=r.getBoxLayoutParams();x.aspect=g,w=pe(x,{width:p,height:d})}this.setViewRect(w.x,w.y,w.width,w.height),this.setCenter(r.get("center"),e),this.setZoom(r.get("zoom"))}function Vf(r,e){M(e.get("geoCoord"),function(t,a){r.addGeoCoord(a,t)})}var Nf=function(){function r(){this.dimensions=Xl}return r.prototype.create=function(e,t){var a=[];function n(o){return{nameProperty:o.get("nameProperty"),aspectScale:o.get("aspectScale"),projection:o.get("projection")}}e.eachComponent("geo",function(o,s){var l=o.get("map"),u=new cn(l+s,l,W({nameMap:o.get("nameMap")},n(o)));u.zoomLimit=o.get("scaleLimit"),a.push(u),o.coordinateSystem=u,u.model=o,u.resize=uo,u.resize(o,t)}),e.eachSeries(function(o){var s=o.get("coordinateSystem");if(s==="geo"){var l=o.get("geoIndex")||0;o.coordinateSystem=a[l]}});var i={};return e.eachSeriesByType("map",function(o){if(!o.getHostGeoModel()){var s=o.getMapType();i[s]=i[s]||[],i[s].push(o)}}),M(i,function(o,s){var l=F(o,function(v){return v.get("nameMap")}),u=new cn(s,s,W({nameMap:al(l)},n(o[0])));u.zoomLimit=Le.apply(null,F(o,function(v){return v.get("scaleLimit")})),a.push(u),u.resize=uo,u.resize(o[0],t),M(o,function(v){v.coordinateSystem=u,Vf(u,v)})}),a},r.prototype.getFilledRegions=function(e,t,a,n){for(var i=(e||[]).slice(),o=J(),s=0;s<i.length;s++)o.set(i[s].name,i[s]);var l=Kt.load(t,a,n);return M(l.regions,function(u){var v=u.name,c=o.get(v),h=u.properties&&u.properties.echartsStyle;c||(c={name:v},i.push(c)),h&&qt(c,h)}),i},r}(),ql=new Nf,Gf=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,a,n){var i=Kt.getGeoResource(t.map);if(i&&i.type==="geoJSON"){var o=t.itemStyle=t.itemStyle||{};"color"in o||(o.color="#eee")}this.mergeDefaultAndTheme(t,n),Pn(t,"label",["show"])},e.prototype.optionUpdated=function(){var t=this,a=this.option;a.regions=ql.getFilledRegions(a.regions,a.map,a.nameMap,a.nameProperty);var n={};this._optionModelMap=Dv(a.regions||[],function(i,o){var s=o.name;return s&&(i.set(s,new Lt(o,t,t.ecModel)),o.selected&&(n[s]=!0)),i},J()),a.selectedMap||(a.selectedMap=n)},e.prototype.getRegionModel=function(t){return this._optionModelMap.get(t)||new Lt(null,this,this.ecModel)},e.prototype.getFormattedLabel=function(t,a){var n=this.getRegionModel(t),i=a==="normal"?n.get(["label","formatter"]):n.get(["emphasis","label","formatter"]),o={name:t};if(st(i))return o.status=a,i(o);if(ot(i))return i.replace("{a}",t??"")},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.select=function(t){var a=this.option,n=a.selectedMode;if(n){n!=="multiple"&&(a.selectedMap=null);var i=a.selectedMap||(a.selectedMap={});i[t]=!0}},e.prototype.unSelect=function(t){var a=this.option.selectedMap;a&&(a[t]=!1)},e.prototype.toggleSelected=function(t){this[this.isSelected(t)?"unSelect":"select"](t)},e.prototype.isSelected=function(t){var a=this.option.selectedMap;return!!(a&&a[t])},e.type="geo",e.layoutMode="box",e.defaultOption={z:0,show:!0,left:"center",top:"center",aspectScale:null,silent:!1,map:"",boundingCoords:null,center:null,zoom:1,scaleLimit:null,label:{show:!1,color:"#000"},itemStyle:{borderWidth:.5,borderColor:"#444"},emphasis:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},select:{label:{show:!0,color:"rgb(100,0,0)"},itemStyle:{color:"rgba(255,215,0,0.8)"}},regions:[]},e}(he);function vo(r,e){return r.pointToProjected?r.pointToProjected(e):r.pointToData(e)}function Zn(r,e,t,a){var n=r.getZoom(),i=r.getCenter(),o=e.zoom,s=r.projectedToPoint?r.projectedToPoint(i):r.dataToPoint(i);if(e.dx!=null&&e.dy!=null&&(s[0]-=e.dx,s[1]-=e.dy,r.setCenter(vo(r,s),a)),o!=null){if(t){var l=t.min||0,u=t.max||1/0;o=Math.max(Math.min(n*o,u),l)/n}r.scaleX*=o,r.scaleY*=o;var v=(e.originX-r.x)*(o-1),c=(e.originY-r.y)*(o-1);r.x-=v,r.y-=c,r.updateTransform(),r.setCenter(vo(r,s),a),r.setZoom(o*n)}return{center:r.getCenter(),zoom:r.getZoom()}}var kf=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.focusBlurEnabled=!0,t}return e.prototype.init=function(t,a){this._api=a},e.prototype.render=function(t,a,n,i){if(this._model=t,!t.get("show")){this._mapDraw&&this._mapDraw.remove(),this._mapDraw=null;return}this._mapDraw||(this._mapDraw=new Zl(n));var o=this._mapDraw;o.draw(t,a,n,this,i),o.group.on("click",this._handleRegionClick,this),o.group.silent=t.get("silent"),this.group.add(o.group),this.updateSelectStatus(t,a,n)},e.prototype._handleRegionClick=function(t){var a;Cv(t.target,function(n){return(a=ft(n).eventData)!=null},!0),a&&this._api.dispatchAction({type:"geoToggleSelect",geoId:this._model.id,name:a.name})},e.prototype.updateSelectStatus=function(t,a,n){var i=this;this._mapDraw.group.traverse(function(o){var s=ft(o).eventData;if(s)return i._model.isSelected(s.name)?n.enterSelect(o):n.leaveSelect(o),!0})},e.prototype.findHighDownDispatchers=function(t){return this._mapDraw&&this._mapDraw.findHighDownDispatchers(t,this._model)},e.prototype.dispose=function(){this._mapDraw&&this._mapDraw.remove()},e.type="geo",e}(Ne);function zf(r,e,t){Kt.registerMap(r,e,t)}function jl(r){r.registerCoordinateSystem("geo",ql),r.registerComponentModel(Gf),r.registerComponentView(kf),r.registerImpl("registerMap",zf),r.registerImpl("getMap",function(t){return Kt.getMapForUser(t)});function e(t,a){a.update="geo:updateSelectStatus",r.registerAction(a,function(n,i){var o={},s=[];return i.eachComponent({mainType:"geo",query:n},function(l){l[t](n.name);var u=l.coordinateSystem;M(u.regions,function(c){o[c.name]=l.isSelected(c.name)||!1});var v=[];M(o,function(c,h){o[h]&&v.push(h)}),s.push({geoIndex:l.componentIndex,name:v})}),{selected:o,allSelected:s,name:n.name}})}e("toggleSelected",{type:"geoToggleSelect",event:"geoselectchanged"}),e("select",{type:"geoSelect",event:"geoselected"}),e("unSelect",{type:"geoUnSelect",event:"geounselected"}),r.registerAction({type:"geoRoam",event:"geoRoam",update:"updateTransform"},function(t,a,n){var i=t.componentType||"series";a.eachComponent({mainType:i,query:t},function(o){var s=o.coordinateSystem;if(s.type==="geo"){var l=Zn(s,t,o.get("scaleLimit"),n);o.setCenter&&o.setCenter(l.center),o.setZoom&&o.setZoom(l.zoom),i==="series"&&M(o.seriesGroup,function(u){u.setCenter(l.center),u.setZoom(l.zoom)})}})})}function Of(r){$(jl),r.registerChartView(Cf),r.registerSeriesModel(Lf),r.registerLayout(Ef),r.registerProcessor(r.PRIORITY.PROCESSOR.STATISTIC,Mf),Lv("map",r.registerAction)}function Bf(r){var e=r;e.hierNode={defaultAncestor:null,ancestor:e,prelim:0,modifier:0,change:0,shift:0,i:0,thread:null};for(var t=[e],a,n;a=t.pop();)if(n=a.children,a.isExpand&&n.length)for(var i=n.length,o=i-1;o>=0;o--){var s=n[o];s.hierNode={defaultAncestor:null,ancestor:s,prelim:0,modifier:0,change:0,shift:0,i:o,thread:null},t.push(s)}}function Ff(r,e){var t=r.isExpand?r.children:[],a=r.parentNode.children,n=r.hierNode.i?a[r.hierNode.i-1]:null;if(t.length){Uf(r);var i=(t[0].hierNode.prelim+t[t.length-1].hierNode.prelim)/2;n?(r.hierNode.prelim=n.hierNode.prelim+e(r,n),r.hierNode.modifier=r.hierNode.prelim-i):r.hierNode.prelim=i}else n&&(r.hierNode.prelim=n.hierNode.prelim+e(r,n));r.parentNode.hierNode.defaultAncestor=$f(r,n,r.parentNode.hierNode.defaultAncestor||a[0],e)}function Hf(r){var e=r.hierNode.prelim+r.parentNode.hierNode.modifier;r.setLayout({x:e},!0),r.hierNode.modifier+=r.parentNode.hierNode.modifier}function co(r){return arguments.length?r:Xf}function lr(r,e){return r-=Math.PI/2,{x:e*Math.cos(r),y:e*Math.sin(r)}}function Wf(r,e){return pe(r.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function Uf(r){for(var e=r.children,t=e.length,a=0,n=0;--t>=0;){var i=e[t];i.hierNode.prelim+=a,i.hierNode.modifier+=a,n+=i.hierNode.change,a+=i.hierNode.shift+n}}function $f(r,e,t,a){if(e){for(var n=r,i=r,o=i.parentNode.children[0],s=e,l=n.hierNode.modifier,u=i.hierNode.modifier,v=o.hierNode.modifier,c=s.hierNode.modifier;s=Ca(s),i=La(i),s&&i;){n=Ca(n),o=La(o),n.hierNode.ancestor=r;var h=s.hierNode.prelim+c-i.hierNode.prelim-u+a(s,i);h>0&&(Zf(Yf(s,r,t),r,h),u+=h,l+=h),c+=s.hierNode.modifier,u+=i.hierNode.modifier,l+=n.hierNode.modifier,v+=o.hierNode.modifier}s&&!Ca(n)&&(n.hierNode.thread=s,n.hierNode.modifier+=c-l),i&&!La(o)&&(o.hierNode.thread=i,o.hierNode.modifier+=u-v,t=r)}return t}function Ca(r){var e=r.children;return e.length&&r.isExpand?e[e.length-1]:r.hierNode.thread}function La(r){var e=r.children;return e.length&&r.isExpand?e[0]:r.hierNode.thread}function Yf(r,e,t){return r.hierNode.ancestor.parentNode===e.parentNode?r.hierNode.ancestor:t}function Zf(r,e,t){var a=t/(e.hierNode.i-r.hierNode.i);e.hierNode.change-=a,e.hierNode.shift+=t,e.hierNode.modifier+=t,e.hierNode.prelim+=t,r.hierNode.change+=a}function Xf(r,e){return r.parentNode===e.parentNode?1:2}var qf=function(){function r(){this.parentPoint=[],this.childPoints=[]}return r}(),jf=function(r){G(e,r);function e(t){return r.call(this,t)||this}return e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new qf},e.prototype.buildPath=function(t,a){var n=a.childPoints,i=n.length,o=a.parentPoint,s=n[0],l=n[i-1];if(i===1){t.moveTo(o[0],o[1]),t.lineTo(s[0],s[1]);return}var u=a.orient,v=u==="TB"||u==="BT"?0:1,c=1-v,h=z(a.forkPosition,1),f=[];f[v]=o[v],f[c]=o[c]+(l[c]-o[c])*h,t.moveTo(o[0],o[1]),t.lineTo(f[0],f[1]),t.moveTo(s[0],s[1]),f[v]=s[v],t.lineTo(f[0],f[1]),f[v]=l[v],t.lineTo(f[0],f[1]),t.lineTo(l[0],l[1]);for(var p=1;p<i-1;p++){var d=n[p];t.moveTo(d[0],d[1]),f[v]=d[v],t.lineTo(f[0],f[1])}},e}(Gt),Kf=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t._mainGroup=new X,t}return e.prototype.init=function(t,a){this._controller=new fa(a.getZr()),this._controllerHost={target:this.group},this.group.add(this._mainGroup)},e.prototype.render=function(t,a,n){var i=t.getData(),o=t.layoutInfo,s=this._mainGroup,l=t.get("layout");l==="radial"?(s.x=o.x+o.width/2,s.y=o.y+o.height/2):(s.x=o.x,s.y=o.y),this._updateViewCoordSys(t,n),this._updateController(t,a,n);var u=this._data;i.diff(u).add(function(v){ho(i,v)&&fo(i,v,null,s,t)}).update(function(v,c){var h=u.getItemGraphicEl(c);if(!ho(i,v)){h&&go(u,c,h,s,t);return}fo(i,v,h,s,t)}).remove(function(v){var c=u.getItemGraphicEl(v);c&&go(u,v,c,s,t)}).execute(),this._nodeScaleRatio=t.get("nodeScaleRatio"),this._updateNodeAndLinkScale(t),t.get("expandAndCollapse")===!0&&i.eachItemGraphicEl(function(v,c){v.off("click").on("click",function(){n.dispatchAction({type:"treeExpandAndCollapse",seriesId:t.id,dataIndex:c})})}),this._data=i},e.prototype._updateViewCoordSys=function(t,a){var n=t.getData(),i=[];n.each(function(c){var h=n.getItemLayout(c);h&&!isNaN(h.x)&&!isNaN(h.y)&&i.push([+h.x,+h.y])});var o=[],s=[];ua(i,o,s);var l=this._min,u=this._max;s[0]-o[0]===0&&(o[0]=l?l[0]:o[0]-1,s[0]=u?u[0]:s[0]+1),s[1]-o[1]===0&&(o[1]=l?l[1]:o[1]-1,s[1]=u?u[1]:s[1]+1);var v=t.coordinateSystem=new Lr;v.zoomLimit=t.get("scaleLimit"),v.setBoundingRect(o[0],o[1],s[0]-o[0],s[1]-o[1]),v.setCenter(t.get("center"),a),v.setZoom(t.get("zoom")),this.group.attr({x:v.x,y:v.y,scaleX:v.scaleX,scaleY:v.scaleY}),this._min=o,this._max=s},e.prototype._updateController=function(t,a,n){var i=this,o=this._controller,s=this._controllerHost,l=this.group;o.setPointerChecker(function(u,v,c){var h=l.getBoundingRect();return h.applyTransform(l.transform),h.contain(v,c)&&!Bn(u,n,t)}),o.enable(t.get("roam")),s.zoomLimit=t.get("scaleLimit"),s.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",function(u){Un(s,u.dx,u.dy),n.dispatchAction({seriesId:t.id,type:"treeRoam",dx:u.dx,dy:u.dy})}).on("zoom",function(u){$n(s,u.scale,u.originX,u.originY),n.dispatchAction({seriesId:t.id,type:"treeRoam",zoom:u.scale,originX:u.originX,originY:u.originY}),i._updateNodeAndLinkScale(t),n.updateLabelLayout()})},e.prototype._updateNodeAndLinkScale=function(t){var a=t.getData(),n=this._getNodeGlobalScale(t);a.eachItemGraphicEl(function(i,o){i.setSymbolScale(n)})},e.prototype._getNodeGlobalScale=function(t){var a=t.coordinateSystem;if(a.type!=="view")return 1;var n=this._nodeScaleRatio,i=a.scaleX||1,o=a.getZoom(),s=(o-1)*n+1;return s/i},e.prototype.dispose=function(){this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype.remove=function(){this._mainGroup.removeAll(),this._data=null},e.type="tree",e}(gt);function ho(r,e){var t=r.getItemLayout(e);return t&&!isNaN(t.x)&&!isNaN(t.y)}function fo(r,e,t,a,n){var i=!t,o=r.tree.getNodeByDataIndex(e),s=o.getModel(),l=o.getVisual("style").fill,u=o.isExpand===!1&&o.children.length!==0?l:"#fff",v=r.tree.root,c=o.parentNode===v?o:o.parentNode||o,h=r.getItemGraphicEl(c.dataIndex),f=c.getLayout(),p=h?{x:h.__oldX,y:h.__oldY,rawX:h.__radialOldRawX,rawY:h.__radialOldRawY}:f,d=o.getLayout();i?(t=new Cl(r,e,null,{symbolInnerColor:u,useNameLabel:!0}),t.x=p.x,t.y=p.y):t.updateData(r,e,null,{symbolInnerColor:u,useNameLabel:!0}),t.__radialOldRawX=t.__radialRawX,t.__radialOldRawY=t.__radialRawY,t.__radialRawX=d.rawX,t.__radialRawY=d.rawY,a.add(t),r.setItemGraphicEl(e,t),t.__oldX=t.x,t.__oldY=t.y,ht(t,{x:d.x,y:d.y},n);var g=t.getSymbolPath();if(n.get("layout")==="radial"){var S=v.children[0],m=S.getLayout(),y=S.children.length,w=void 0,x=void 0;if(d.x===m.x&&o.isExpand===!0&&S.children.length){var b={x:(S.children[0].getLayout().x+S.children[y-1].getLayout().x)/2,y:(S.children[0].getLayout().y+S.children[y-1].getLayout().y)/2};w=Math.atan2(b.y-m.y,b.x-m.x),w<0&&(w=Math.PI*2+w),x=b.x<m.x,x&&(w=w-Math.PI)}else w=Math.atan2(d.y-m.y,d.x-m.x),w<0&&(w=Math.PI*2+w),o.children.length===0||o.children.length!==0&&o.isExpand===!1?(x=d.x<m.x,x&&(w=w-Math.PI)):(x=d.x>m.x,x||(w=w-Math.PI));var _=x?"left":"right",T=s.getModel("label"),I=T.get("rotate"),A=I*(Math.PI/180),D=g.getTextContent();D&&(g.setTextConfig({position:T.get("position")||_,rotation:I==null?-w:A,origin:"center"}),D.setStyle("verticalAlign","middle"))}var E=s.get(["emphasis","focus"]),P=E==="relative"?$r(o.getAncestorsIndices(),o.getDescendantIndices()):E==="ancestor"?o.getAncestorsIndices():E==="descendant"?o.getDescendantIndices():null;P&&(ft(t).focus=P),Jf(n,o,v,t,p,f,d,a),t.__edge&&(t.onHoverStateChange=function(C){if(C!=="blur"){var L=o.parentNode&&r.getItemGraphicEl(o.parentNode.dataIndex);L&&L.hoverState===Pv||el(t.__edge,C)}})}function Jf(r,e,t,a,n,i,o,s){var l=e.getModel(),u=r.get("edgeShape"),v=r.get("layout"),c=r.getOrient(),h=r.get(["lineStyle","curveness"]),f=r.get("edgeForkPosition"),p=l.getModel("lineStyle").getLineStyle(),d=a.__edge;if(u==="curve")e.parentNode&&e.parentNode!==t&&(d||(d=a.__edge=new Mv({shape:hn(v,c,h,n,n)})),ht(d,{shape:hn(v,c,h,i,o)},r));else if(u==="polyline"&&v==="orthogonal"&&e!==t&&e.children&&e.children.length!==0&&e.isExpand===!0){for(var g=e.children,S=[],m=0;m<g.length;m++){var y=g[m].getLayout();S.push([y.x,y.y])}d||(d=a.__edge=new jf({shape:{parentPoint:[o.x,o.y],childPoints:[[o.x,o.y]],orient:c,forkPosition:f}})),ht(d,{shape:{parentPoint:[o.x,o.y],childPoints:S}},r)}d&&!(u==="polyline"&&!e.isExpand)&&(d.useStyle(j({strokeNoScale:!0,fill:null},p)),Wt(d,l,"lineStyle"),Xe(d),s.add(d))}function po(r,e,t,a,n){var i=e.tree.root,o=Kl(i,r),s=o.source,l=o.sourceLayout,u=e.getItemGraphicEl(r.dataIndex);if(u){var v=e.getItemGraphicEl(s.dataIndex),c=v.__edge,h=u.__edge||(s.isExpand===!1||s.children.length===1?c:void 0),f=a.get("edgeShape"),p=a.get("layout"),d=a.get("orient"),g=a.get(["lineStyle","curveness"]);h&&(f==="curve"?Yr(h,{shape:hn(p,d,g,l,l),style:{opacity:0}},a,{cb:function(){t.remove(h)},removeOpt:n}):f==="polyline"&&a.get("layout")==="orthogonal"&&Yr(h,{shape:{parentPoint:[l.x,l.y],childPoints:[[l.x,l.y]]},style:{opacity:0}},a,{cb:function(){t.remove(h)},removeOpt:n}))}}function Kl(r,e){for(var t=e.parentNode===r?e:e.parentNode||e,a;a=t.getLayout(),a==null;)t=t.parentNode===r?t:t.parentNode||t;return{source:t,sourceLayout:a}}function go(r,e,t,a,n){var i=r.tree.getNodeByDataIndex(e),o=r.tree.root,s=Kl(o,i).sourceLayout,l={duration:n.get("animationDurationUpdate"),easing:n.get("animationEasingUpdate")};Yr(t,{x:s.x+1,y:s.y+1},n,{cb:function(){a.remove(t),r.setItemGraphicEl(e,null)},removeOpt:l}),t.fadeOut(null,r.hostModel,{fadeLabel:!0,animation:l}),i.children.forEach(function(u){po(u,r,a,n,l)}),po(i,r,a,n,l)}function hn(r,e,t,a,n){var i,o,s,l,u,v,c,h;if(r==="radial"){u=a.rawX,c=a.rawY,v=n.rawX,h=n.rawY;var f=lr(u,c),p=lr(u,c+(h-c)*t),d=lr(v,h+(c-h)*t),g=lr(v,h);return{x1:f.x||0,y1:f.y||0,x2:g.x||0,y2:g.y||0,cpx1:p.x||0,cpy1:p.y||0,cpx2:d.x||0,cpy2:d.y||0}}else u=a.x,c=a.y,v=n.x,h=n.y,(e==="LR"||e==="RL")&&(i=u+(v-u)*t,o=c,s=v+(u-v)*t,l=h),(e==="TB"||e==="BT")&&(i=u,o=c+(h-c)*t,s=v,l=h+(c-h)*t);return{x1:u,y1:c,x2:v,y2:h,cpx1:i,cpy1:o,cpx2:s,cpy2:l}}var Ct=fe();function Jl(r){var e=r.mainData,t=r.datas;t||(t={main:e},r.datasAttr={main:"data"}),r.datas=r.mainData=null,Ql(e,t,r),M(t,function(a){M(e.TRANSFERABLE_METHODS,function(n){a.wrapMethod(n,bt(Qf,r))})}),e.wrapMethod("cloneShallow",bt(ep,r)),M(e.CHANGABLE_METHODS,function(a){e.wrapMethod(a,bt(tp,r))}),Ur(t[e.dataType]===e)}function Qf(r,e){if(np(this)){var t=W({},Ct(this).datas);t[this.dataType]=e,Ql(e,t,r)}else Xn(e,this.dataType,Ct(this).mainData,r);return e}function tp(r,e){return r.struct&&r.struct.update(),e}function ep(r,e){return M(Ct(e).datas,function(t,a){t!==e&&Xn(t.cloneShallow(),a,e,r)}),e}function rp(r){var e=Ct(this).mainData;return r==null||e==null?e:Ct(e).datas[r]}function ap(){var r=Ct(this).mainData;return r==null?[{data:r}]:F(Dt(Ct(r).datas),function(e){return{type:e,data:Ct(r).datas[e]}})}function np(r){return Ct(r).mainData===r}function Ql(r,e,t){Ct(r).datas={},M(e,function(a,n){Xn(a,n,r,t)})}function Xn(r,e,t,a){Ct(t).datas[e]=r,Ct(r).mainData=t,r.dataType=e,a.struct&&(r[a.structAttr]=a.struct,a.struct[a.datasAttr[e]]=r),r.getLinkedData=rp,r.getLinkedDataAll=ap}var ip=function(){function r(e,t){this.depth=0,this.height=0,this.dataIndex=-1,this.children=[],this.viewChildren=[],this.isExpand=!1,this.name=e||"",this.hostTree=t}return r.prototype.isRemoved=function(){return this.dataIndex<0},r.prototype.eachNode=function(e,t,a){st(e)&&(a=t,t=e,e=null),e=e||{},ot(e)&&(e={order:e});var n=e.order||"preorder",i=this[e.attr||"children"],o;n==="preorder"&&(o=t.call(a,this));for(var s=0;!o&&s<i.length;s++)i[s].eachNode(e,t,a);n==="postorder"&&t.call(a,this)},r.prototype.updateDepthAndHeight=function(e){var t=0;this.depth=e;for(var a=0;a<this.children.length;a++){var n=this.children[a];n.updateDepthAndHeight(e+1),n.height>t&&(t=n.height)}this.height=t+1},r.prototype.getNodeById=function(e){if(this.getId()===e)return this;for(var t=0,a=this.children,n=a.length;t<n;t++){var i=a[t].getNodeById(e);if(i)return i}},r.prototype.contains=function(e){if(e===this)return!0;for(var t=0,a=this.children,n=a.length;t<n;t++){var i=a[t].contains(e);if(i)return i}},r.prototype.getAncestors=function(e){for(var t=[],a=e?this:this.parentNode;a;)t.push(a),a=a.parentNode;return t.reverse(),t},r.prototype.getAncestorsIndices=function(){for(var e=[],t=this;t;)e.push(t.dataIndex),t=t.parentNode;return e.reverse(),e},r.prototype.getDescendantIndices=function(){var e=[];return this.eachNode(function(t){e.push(t.dataIndex)}),e},r.prototype.getValue=function(e){var t=this.hostTree.data;return t.getStore().get(t.getDimensionIndex(e||"value"),this.dataIndex)},r.prototype.setLayout=function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemLayout(this.dataIndex,e,t)},r.prototype.getLayout=function(){return this.hostTree.data.getItemLayout(this.dataIndex)},r.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostTree,a=t.data.getItemModel(this.dataIndex);return a.getModel(e)}},r.prototype.getLevelModel=function(){return(this.hostTree.levelModels||[])[this.depth]},r.prototype.setVisual=function(e,t){this.dataIndex>=0&&this.hostTree.data.setItemVisual(this.dataIndex,e,t)},r.prototype.getVisual=function(e){return this.hostTree.data.getItemVisual(this.dataIndex,e)},r.prototype.getRawIndex=function(){return this.hostTree.data.getRawIndex(this.dataIndex)},r.prototype.getId=function(){return this.hostTree.data.getId(this.dataIndex)},r.prototype.getChildIndex=function(){if(this.parentNode){for(var e=this.parentNode.children,t=0;t<e.length;++t)if(e[t]===this)return t;return-1}return-1},r.prototype.isAncestorOf=function(e){for(var t=e.parentNode;t;){if(t===this)return!0;t=t.parentNode}return!1},r.prototype.isDescendantOf=function(e){return e!==this&&e.isAncestorOf(this)},r}(),qn=function(){function r(e){this.type="tree",this._nodes=[],this.hostModel=e}return r.prototype.eachNode=function(e,t,a){this.root.eachNode(e,t,a)},r.prototype.getNodeByDataIndex=function(e){var t=this.data.getRawIndex(e);return this._nodes[t]},r.prototype.getNodeById=function(e){return this.root.getNodeById(e)},r.prototype.update=function(){for(var e=this.data,t=this._nodes,a=0,n=t.length;a<n;a++)t[a].dataIndex=-1;for(var a=0,n=e.count();a<n;a++)t[e.getRawIndex(a)].dataIndex=a},r.prototype.clearLayouts=function(){this.data.clearItemLayouts()},r.createTree=function(e,t,a){var n=new r(t),i=[],o=1;s(e);function s(v,c){var h=v.value;o=Math.max(o,q(h)?h.length:1),i.push(v);var f=new ip(va(v.name,""),n);c?op(f,c):n.root=f,n._nodes.push(f);var p=v.children;if(p)for(var d=0;d<p.length;d++)s(p[d],f)}n.root.updateDepthAndHeight(0);var l=Mn(i,{coordDimensions:["value"],dimensionsCount:o}).dimensions,u=new Pe(l,t);return u.initData(i),a&&a(u),Jl({mainData:u,struct:n,structAttr:"tree"}),n.update(),n},r}();function op(r,e){var t=e.children;r.parentNode!==e&&(t.push(r),r.parentNode=e)}function yr(r,e,t){if(r&&Ge(e,r.type)>=0){var a=t.getData().tree.root,n=r.targetNode;if(ot(n)&&(n=a.getNodeById(n)),n&&a.contains(n))return{node:n};var i=r.targetNodeId;if(i!=null&&(n=a.getNodeById(i)))return{node:n}}}function tu(r){for(var e=[];r;)r=r.parentNode,r&&e.push(r);return e.reverse()}function jn(r,e){var t=tu(r);return Ge(t,e)>=0}function ya(r,e){for(var t=[];r;){var a=r.dataIndex;t.push({name:r.name,dataIndex:a,value:e.getRawValue(a)}),r=r.parentNode}return t.reverse(),t}var sp=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.hasSymbolVisual=!0,t.ignoreStyleOnData=!0,t}return e.prototype.getInitialData=function(t){var a={name:t.name,children:t.data},n=t.leaves||{},i=new Lt(n,this,this.ecModel),o=qn.createTree(a,this,s);function s(c){c.wrapMethod("getItemModel",function(h,f){var p=o.getNodeByDataIndex(f);return p&&p.children.length&&p.isExpand||(h.parentModel=i),h})}var l=0;o.eachNode("preorder",function(c){c.depth>l&&(l=c.depth)});var u=t.expandAndCollapse,v=u&&t.initialTreeDepth>=0?t.initialTreeDepth:l;return o.root.eachNode("preorder",function(c){var h=c.hostTree.data.getRawDataItem(c.dataIndex);c.isExpand=h&&h.collapsed!=null?!h.collapsed:c.depth<=v}),o.data},e.prototype.getOrient=function(){var t=this.get("orient");return t==="horizontal"?t="LR":t==="vertical"&&(t="TB"),t},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.formatTooltip=function(t,a,n){for(var i=this.getData().tree,o=i.root.children[0],s=i.getNodeByDataIndex(t),l=s.getValue(),u=s.name;s&&s!==o;)u=s.parentNode.name+"."+u,s=s.parentNode;return Nt("nameValue",{name:u,value:l,noValue:isNaN(l)||l==null})},e.prototype.getDataParams=function(t){var a=r.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return a.treeAncestors=ya(n,this),a.collapsed=!n.isExpand,a},e.type="series.tree",e.layoutMode="box",e.defaultOption={z:2,coordinateSystem:"view",left:"12%",top:"12%",right:"12%",bottom:"12%",layout:"orthogonal",edgeShape:"curve",edgeForkPosition:"50%",roam:!1,nodeScaleRatio:.4,center:null,zoom:1,orient:"LR",symbol:"emptyCircle",symbolSize:7,expandAndCollapse:!0,initialTreeDepth:2,lineStyle:{color:"#ccc",width:1.5,curveness:.5},itemStyle:{color:"lightsteelblue",borderWidth:1.5},label:{show:!0},animationEasing:"linear",animationDuration:700,animationDurationUpdate:500},e}(St);function lp(r,e,t){for(var a=[r],n=[],i;i=a.pop();)if(n.push(i),i.isExpand){var o=i.children;if(o.length)for(var s=0;s<o.length;s++)a.push(o[s])}for(;i=n.pop();)e(i,t)}function rr(r,e){for(var t=[r],a;a=t.pop();)if(e(a),a.isExpand){var n=a.children;if(n.length)for(var i=n.length-1;i>=0;i--)t.push(n[i])}}function up(r,e){r.eachSeriesByType("tree",function(t){vp(t,e)})}function vp(r,e){var t=Wf(r,e);r.layoutInfo=t;var a=r.get("layout"),n=0,i=0,o=null;a==="radial"?(n=2*Math.PI,i=Math.min(t.height,t.width)/2,o=co(function(y,w){return(y.parentNode===w.parentNode?1:2)/y.depth})):(n=t.width,i=t.height,o=co());var s=r.getData().tree.root,l=s.children[0];if(l){Bf(s),lp(l,Ff,o),s.hierNode.modifier=-l.hierNode.prelim,rr(l,Hf);var u=l,v=l,c=l;rr(l,function(y){var w=y.getLayout().x;w<u.getLayout().x&&(u=y),w>v.getLayout().x&&(v=y),y.depth>c.depth&&(c=y)});var h=u===v?1:o(u,v)/2,f=h-u.getLayout().x,p=0,d=0,g=0,S=0;if(a==="radial")p=n/(v.getLayout().x+h+f),d=i/(c.depth-1||1),rr(l,function(y){g=(y.getLayout().x+f)*p,S=(y.depth-1)*d;var w=lr(g,S);y.setLayout({x:w.x,y:w.y,rawX:g,rawY:S},!0)});else{var m=r.getOrient();m==="RL"||m==="LR"?(d=i/(v.getLayout().x+h+f),p=n/(c.depth-1||1),rr(l,function(y){S=(y.getLayout().x+f)*d,g=m==="LR"?(y.depth-1)*p:n-(y.depth-1)*p,y.setLayout({x:g,y:S},!0)})):(m==="TB"||m==="BT")&&(p=n/(v.getLayout().x+h+f),d=i/(c.depth-1||1),rr(l,function(y){g=(y.getLayout().x+f)*p,S=m==="TB"?(y.depth-1)*d:i-(y.depth-1)*d,y.setLayout({x:g,y:S},!0)}))}}}function cp(r){r.eachSeriesByType("tree",function(e){var t=e.getData(),a=t.tree;a.eachNode(function(n){var i=n.getModel(),o=i.getModel("itemStyle").getItemStyle(),s=t.ensureUniqueItemVisual(n.dataIndex,"style");W(s,o)})})}function hp(r){r.registerAction({type:"treeExpandAndCollapse",event:"treeExpandAndCollapse",update:"update"},function(e,t){t.eachComponent({mainType:"series",subType:"tree",query:e},function(a){var n=e.dataIndex,i=a.getData().tree,o=i.getNodeByDataIndex(n);o.isExpand=!o.isExpand})}),r.registerAction({type:"treeRoam",event:"treeRoam",update:"none"},function(e,t,a){t.eachComponent({mainType:"series",subType:"tree",query:e},function(n){var i=n.coordinateSystem,o=Zn(i,e,void 0,a);n.setCenter&&n.setCenter(o.center),n.setZoom&&n.setZoom(o.zoom)})})}function fp(r){r.registerChartView(Kf),r.registerSeriesModel(sp),r.registerLayout(up),r.registerVisual(cp),hp(r)}var yo=["treemapZoomToNode","treemapRender","treemapMove"];function pp(r){for(var e=0;e<yo.length;e++)r.registerAction({type:yo[e],update:"updateView"},qe);r.registerAction({type:"treemapRootToNode",update:"updateView"},function(t,a){a.eachComponent({mainType:"series",subType:"treemap",query:t},n);function n(i,o){var s=["treemapZoomToNode","treemapRootToNode"],l=yr(t,s,i);if(l){var u=i.getViewRoot();u&&(t.direction=jn(u,l.node)?"rollUp":"drillDown"),i.resetViewRoot(l.node)}}})}function eu(r){var e=r.getData(),t=e.tree,a={};t.eachNode(function(n){for(var i=n;i&&i.depth>1;)i=i.parentNode;var o=on(r.ecModel,i.name||i.dataIndex+"",a);n.setVisual("decal",o)})}var dp=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.preventUsingHoverLayer=!0,t}return e.prototype.getInitialData=function(t,a){var n={name:t.name,children:t.data};ru(n);var i=t.levels||[],o=this.designatedVisualItemStyle={},s=new Lt({itemStyle:o},this,a);i=t.levels=gp(i,a);var l=F(i||[],function(c){return new Lt(c,s,a)},this),u=qn.createTree(n,this,v);function v(c){c.wrapMethod("getItemModel",function(h,f){var p=u.getNodeByDataIndex(f),d=p?l[p.depth]:null;return h.parentModel=d||s,h})}return u.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.formatTooltip=function(t,a,n){var i=this.getData(),o=this.getRawValue(t),s=i.getName(t);return Nt("nameValue",{name:s,value:o})},e.prototype.getDataParams=function(t){var a=r.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return a.treeAncestors=ya(n,this),a.treePathInfo=a.treeAncestors,a},e.prototype.setLayoutInfo=function(t){this.layoutInfo=this.layoutInfo||{},W(this.layoutInfo,t)},e.prototype.mapIdToIndex=function(t){var a=this._idIndexMap;a||(a=this._idIndexMap=J(),this._idIndexMapCount=0);var n=a.get(t);return n==null&&a.set(t,n=this._idIndexMapCount++),n},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var a=this.getRawData().tree.root;(!t||t!==a&&!a.contains(t))&&(this._viewRoot=a)},e.prototype.enableAriaDecal=function(){eu(this)},e.type="series.treemap",e.layoutMode="box",e.defaultOption={progressive:0,left:"center",top:"middle",width:"80%",height:"80%",sort:!0,clipWindow:"origin",squareRatio:.5*(1+Math.sqrt(5)),leafDepth:null,drillDownIcon:"▶",zoomToNodeRatio:.32*.32,scaleLimit:null,roam:!0,nodeClick:"zoomToNode",animation:!0,animationDurationUpdate:900,animationEasing:"quinticInOut",breadcrumb:{show:!0,height:22,left:"center",top:"bottom",emptyItemWidth:25,itemStyle:{color:"rgba(0,0,0,0.7)",textStyle:{color:"#fff"}},emphasis:{itemStyle:{color:"rgba(0,0,0,0.9)"}}},label:{show:!0,distance:0,padding:5,position:"inside",color:"#fff",overflow:"truncate"},upperLabel:{show:!1,position:[0,"50%"],height:20,overflow:"truncate",verticalAlign:"middle"},itemStyle:{color:null,colorAlpha:null,colorSaturation:null,borderWidth:0,gapWidth:0,borderColor:"#fff",borderColorSaturation:null},emphasis:{upperLabel:{show:!0,position:[0,"50%"],overflow:"truncate",verticalAlign:"middle"}},visualDimension:0,visualMin:null,visualMax:null,color:[],colorAlpha:null,colorSaturation:null,colorMappingBy:"index",visibleMin:10,childrenVisibleMin:null,levels:[]},e}(St);function ru(r){var e=0;M(r.children,function(a){ru(a);var n=a.value;q(n)&&(n=n[0]),e+=n});var t=r.value;q(t)&&(t=t[0]),(t==null||isNaN(t))&&(t=e),t<0&&(t=0),q(r.value)?r.value[0]=t:r.value=t}function gp(r,e){var t=Ft(e.get("color")),a=Ft(e.get(["aria","decal","decals"]));if(t){r=r||[];var n,i;M(r,function(s){var l=new Lt(s),u=l.get("color"),v=l.get("decal");(l.get(["itemStyle","color"])||u&&u!=="none")&&(n=!0),(l.get(["itemStyle","decal"])||v&&v!=="none")&&(i=!0)});var o=r[0]||(r[0]={});return n||(o.color=t.slice()),!i&&a&&(o.decal=a.slice()),r}}var yp=8,mo=8,Pa=5,mp=function(){function r(e){this.group=new X,e.add(this.group)}return r.prototype.render=function(e,t,a,n){var i=e.getModel("breadcrumb"),o=this.group;if(o.removeAll(),!(!i.get("show")||!a)){var s=i.getModel("itemStyle"),l=i.getModel("emphasis"),u=s.getModel("textStyle"),v=l.getModel(["itemStyle","textStyle"]),c={pos:{left:i.get("left"),right:i.get("right"),top:i.get("top"),bottom:i.get("bottom")},box:{width:t.getWidth(),height:t.getHeight()},emptyItemWidth:i.get("emptyItemWidth"),totalWidth:0,renderList:[]};this._prepare(a,c,u),this._renderContent(e,c,s,l,u,v,n),Ev(o,c.pos,c.box)}},r.prototype._prepare=function(e,t,a){for(var n=e;n;n=n.parentNode){var i=va(n.getModel().get("name"),""),o=a.getTextRect(i),s=Math.max(o.width+yp*2,t.emptyItemWidth);t.totalWidth+=s+mo,t.renderList.push({node:n,text:i,width:s})}},r.prototype._renderContent=function(e,t,a,n,i,o,s){for(var l=0,u=t.emptyItemWidth,v=e.get(["breadcrumb","height"]),c=Rv(t.pos,t.box),h=t.totalWidth,f=t.renderList,p=n.getModel("itemStyle").getItemStyle(),d=f.length-1;d>=0;d--){var g=f[d],S=g.node,m=g.width,y=g.text;h>c.width&&(h-=m-u,m=u,y=null);var w=new Ee({shape:{points:Sp(l,0,m,v,d===f.length-1,d===0)},style:j(a.getItemStyle(),{lineJoin:"bevel"}),textContent:new Yt({style:Bt(i,{text:y})}),textConfig:{position:"inside"},z2:Tr*1e4,onclick:bt(s,S)});w.disableLabelAnimation=!0,w.getTextContent().ensureState("emphasis").style=Bt(o,{text:y}),w.ensureState("emphasis").style=p,dt(w,n.get("focus"),n.get("blurScope"),n.get("disabled")),this.group.add(w),xp(w,e,S),l+=m+mo}},r.prototype.remove=function(){this.group.removeAll()},r}();function Sp(r,e,t,a,n,i){var o=[[n?r:r-Pa,e],[r+t,e],[r+t,e+a],[n?r:r-Pa,e+a]];return!i&&o.splice(2,0,[r+t+Pa,e+a/2]),!n&&o.push([r,e+a/2]),o}function xp(r,e,t){ft(r).eventData={componentType:"series",componentSubType:"treemap",componentIndex:e.componentIndex,seriesIndex:e.seriesIndex,seriesName:e.name,seriesType:"treemap",selfType:"breadcrumb",nodeData:{dataIndex:t&&t.dataIndex,name:t&&t.name},treePathInfo:t&&ya(t,e)}}var bp=function(){function r(){this._storage=[],this._elExistsMap={}}return r.prototype.add=function(e,t,a,n,i){return this._elExistsMap[e.id]?!1:(this._elExistsMap[e.id]=!0,this._storage.push({el:e,target:t,duration:a,delay:n,easing:i}),!0)},r.prototype.finished=function(e){return this._finishedCallback=e,this},r.prototype.start=function(){for(var e=this,t=this._storage.length,a=function(){t--,t<=0&&(e._storage.length=0,e._elExistsMap={},e._finishedCallback&&e._finishedCallback())},n=0,i=this._storage.length;n<i;n++){var o=this._storage[n];o.el.animateTo(o.target,{duration:o.duration,delay:o.delay,easing:o.easing,setToFinal:!0,done:a,aborted:a})}return this},r}();function wp(){return new bp}var fn=X,So=At,xo=3,bo="label",wo="upperLabel",_p=Tr*10,Ap=Tr*2,Tp=Tr*3,be=il([["fill","color"],["stroke","strokeColor"],["lineWidth","strokeWidth"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"]]),_o=function(r){var e=be(r);return e.stroke=e.fill=e.lineWidth=null,e},Jr=fe(),Ip=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t._state="ready",t._storage=ar(),t}return e.prototype.render=function(t,a,n,i){var o=a.findComponents({mainType:"series",subType:"treemap",query:i});if(!(Ge(o,t)<0)){this.seriesModel=t,this.api=n,this.ecModel=a;var s=["treemapZoomToNode","treemapRootToNode"],l=yr(i,s,t),u=i&&i.type,v=t.layoutInfo,c=!this._oldTree,h=this._storage,f=u==="treemapRootToNode"&&l&&h?{rootNodeGroup:h.nodeGroup[l.node.getRawIndex()],direction:i.direction}:null,p=this._giveContainerGroup(v),d=t.get("animation"),g=this._doRender(p,t,f);d&&!c&&(!u||u==="treemapZoomToNode"||u==="treemapRootToNode")?this._doAnimation(p,g,t,f):g.renderFinally(),this._resetController(n),this._renderBreadcrumb(t,n,l)}},e.prototype._giveContainerGroup=function(t){var a=this._containerGroup;return a||(a=this._containerGroup=new fn,this._initEvents(a),this.group.add(a)),a.x=t.x,a.y=t.y,a},e.prototype._doRender=function(t,a,n){var i=a.getData().tree,o=this._oldTree,s=ar(),l=ar(),u=this._storage,v=[];function c(m,y,w,x){return Dp(a,l,u,n,s,v,m,y,w,x)}d(i.root?[i.root]:[],o&&o.root?[o.root]:[],t,i===o||!o,0);var h=g(u);if(this._oldTree=i,this._storage=l,this._controllerHost){var f=this.seriesModel.layoutInfo,p=i.root.getLayout();p.width===f.width&&p.height===f.height&&(this._controllerHost.zoom=1)}return{lastsForAnimation:s,willDeleteEls:h,renderFinally:S};function d(m,y,w,x,b){x?(y=m,M(m,function(I,A){!I.isRemoved()&&T(A,A)})):new je(y,m,_,_).add(T).update(T).remove(bt(T,null)).execute();function _(I){return I.getId()}function T(I,A){var D=I!=null?m[I]:null,E=A!=null?y[A]:null,P=c(D,E,w,b);P&&d(D&&D.viewChildren||[],E&&E.viewChildren||[],P,x,b+1)}}function g(m){var y=ar();return m&&M(m,function(w,x){var b=y[x];M(w,function(_){_&&(b.push(_),Jr(_).willDelete=!0)})}),y}function S(){M(h,function(m){M(m,function(y){y.parent&&y.parent.remove(y)})}),M(v,function(m){m.invisible=!0,m.dirty()})}},e.prototype._doAnimation=function(t,a,n,i){var o=n.get("animationDurationUpdate"),s=n.get("animationEasing"),l=(st(o)?0:o)||0,u=(st(s)?null:s)||"cubicOut",v=wp();M(a.willDeleteEls,function(c,h){M(c,function(f,p){if(!f.invisible){var d=f.parent,g,S=Jr(d);if(i&&i.direction==="drillDown")g=d===i.rootNodeGroup?{shape:{x:0,y:0,width:S.nodeWidth,height:S.nodeHeight},style:{opacity:0}}:{style:{opacity:0}};else{var m=0,y=0;S.willDelete||(m=S.nodeWidth/2,y=S.nodeHeight/2),g=h==="nodeGroup"?{x:m,y,style:{opacity:0}}:{shape:{x:m,y,width:0,height:0},style:{opacity:0}}}g&&v.add(f,g,l,0,u)}})}),M(this._storage,function(c,h){M(c,function(f,p){var d=a.lastsForAnimation[h][p],g={};d&&(f instanceof X?d.oldX!=null&&(g.x=f.x,g.y=f.y,f.x=d.oldX,f.y=d.oldY):(d.oldShape&&(g.shape=W({},f.shape),f.setShape(d.oldShape)),d.fadein?(f.setStyle("opacity",0),g.style={opacity:1}):f.style.opacity!==1&&(g.style={opacity:1})),v.add(f,g,l,0,u))})},this),this._state="animating",v.finished(it(function(){this._state="ready",a.renderFinally()},this)).start()},e.prototype._resetController=function(t){var a=this._controller,n=this._controllerHost;n||(this._controllerHost={target:this.group},n=this._controllerHost),a||(a=this._controller=new fa(t.getZr()),a.enable(this.seriesModel.get("roam")),n.zoomLimit=this.seriesModel.get("scaleLimit"),n.zoom=this.seriesModel.get("zoom"),a.on("pan",it(this._onPan,this)),a.on("zoom",it(this._onZoom,this)));var i=new ct(0,0,t.getWidth(),t.getHeight());a.setPointerChecker(function(o,s,l){return i.contain(s,l)})},e.prototype._clearController=function(){var t=this._controller;this._controllerHost=null,t&&(t.dispose(),t=null)},e.prototype._onPan=function(t){if(this._state!=="animating"&&(Math.abs(t.dx)>xo||Math.abs(t.dy)>xo)){var a=this.seriesModel.getData().tree.root;if(!a)return;var n=a.getLayout();if(!n)return;this.api.dispatchAction({type:"treemapMove",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:n.x+t.dx,y:n.y+t.dy,width:n.width,height:n.height}})}},e.prototype._onZoom=function(t){var a=t.originX,n=t.originY,i=t.scale;if(this._state!=="animating"){var o=this.seriesModel.getData().tree.root;if(!o)return;var s=o.getLayout();if(!s)return;var l=new ct(s.x,s.y,s.width,s.height),u=null,v=this._controllerHost;u=v.zoomLimit;var c=v.zoom=v.zoom||1;if(c*=i,u){var h=u.min||0,f=u.max||1/0;c=Math.max(Math.min(f,c),h)}var p=c/v.zoom;v.zoom=c;var d=this.seriesModel.layoutInfo;a-=d.x,n-=d.y;var g=Ar();pr(g,g,[-a,-n]),Qs(g,g,[p,p]),pr(g,g,[a,n]),l.applyTransform(g),this.api.dispatchAction({type:"treemapRender",from:this.uid,seriesId:this.seriesModel.id,rootRect:{x:l.x,y:l.y,width:l.width,height:l.height}})}},e.prototype._initEvents=function(t){var a=this;t.on("click",function(n){if(a._state==="ready"){var i=a.seriesModel.get("nodeClick",!0);if(i){var o=a.findTarget(n.offsetX,n.offsetY);if(o){var s=o.node;if(s.getLayout().isLeafRoot)a._rootToNode(o);else if(i==="zoomToNode")a._zoomToNode(o);else if(i==="link"){var l=s.hostTree.data.getItemModel(s.dataIndex),u=l.get("link",!0),v=l.get("target",!0)||"blank";u&&nl(u,v)}}}}},this)},e.prototype._renderBreadcrumb=function(t,a,n){var i=this;n||(n=t.get("leafDepth",!0)!=null?{node:t.getViewRoot()}:this.findTarget(a.getWidth()/2,a.getHeight()/2),n||(n={node:t.getData().tree.root})),(this._breadcrumb||(this._breadcrumb=new mp(this.group))).render(t,a,n.node,function(o){i._state!=="animating"&&(jn(t.getViewRoot(),o)?i._rootToNode({node:o}):i._zoomToNode({node:o}))})},e.prototype.remove=function(){this._clearController(),this._containerGroup&&this._containerGroup.removeAll(),this._storage=ar(),this._state="ready",this._breadcrumb&&this._breadcrumb.remove()},e.prototype.dispose=function(){this._clearController()},e.prototype._zoomToNode=function(t){this.api.dispatchAction({type:"treemapZoomToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},e.prototype._rootToNode=function(t){this.api.dispatchAction({type:"treemapRootToNode",from:this.uid,seriesId:this.seriesModel.id,targetNode:t.node})},e.prototype.findTarget=function(t,a){var n,i=this.seriesModel.getViewRoot();return i.eachNode({attr:"viewChildren",order:"preorder"},function(o){var s=this._storage.background[o.getRawIndex()];if(s){var l=s.transformCoordToLocal(t,a),u=s.shape;if(u.x<=l[0]&&l[0]<=u.x+u.width&&u.y<=l[1]&&l[1]<=u.y+u.height)n={node:o,offsetX:l[0],offsetY:l[1]};else return!1}},this),n},e.type="treemap",e}(gt);function ar(){return{nodeGroup:[],background:[],content:[]}}function Dp(r,e,t,a,n,i,o,s,l,u){if(!o)return;var v=o.getLayout(),c=r.getData(),h=o.getModel();if(c.setItemGraphicEl(o.dataIndex,null),!v||!v.isInView)return;var f=v.width,p=v.height,d=v.borderWidth,g=v.invisible,S=o.getRawIndex(),m=s&&s.getRawIndex(),y=o.viewChildren,w=v.upperHeight,x=y&&y.length,b=h.getModel("itemStyle"),_=h.getModel(["emphasis","itemStyle"]),T=h.getModel(["blur","itemStyle"]),I=h.getModel(["select","itemStyle"]),A=b.get("borderRadius")||0,D=et("nodeGroup",fn);if(!D)return;if(l.add(D),D.x=v.x||0,D.y=v.y||0,D.markRedraw(),Jr(D).nodeWidth=f,Jr(D).nodeHeight=p,v.isAboveViewRoot)return D;var E=et("background",So,u,Ap);E&&O(D,E,x&&v.upperLabelHeight);var P=h.getModel("emphasis"),C=P.get("focus"),L=P.get("blurScope"),R=P.get("disabled"),V=C==="ancestor"?o.getAncestorsIndices():C==="descendant"?o.getDescendantIndices():C;if(x)wi(D)&&Mr(D,!1),E&&(Mr(E,!R),c.setItemGraphicEl(o.dataIndex,E),_i(E,V,L));else{var N=et("content",So,u,Tp);N&&H(D,N),E.disableMorphing=!0,E&&wi(E)&&Mr(E,!1),Mr(D,!R),c.setItemGraphicEl(o.dataIndex,D);var k=h.getShallow("cursor");k&&N.attr("cursor",k),_i(D,V,L)}return D;function O(U,B,Q){var Z=ft(B);if(Z.dataIndex=o.dataIndex,Z.seriesIndex=r.seriesIndex,B.setShape({x:0,y:0,width:f,height:p,r:A}),g)Y(B);else{B.invisible=!1;var rt=o.getVisual("style"),ut=rt.stroke,yt=_o(b);yt.fill=ut;var at=be(_);at.fill=_.get("borderColor");var pt=be(T);pt.fill=T.get("borderColor");var wt=be(I);if(wt.fill=I.get("borderColor"),Q){var Pt=f-2*d;tt(B,ut,rt.opacity,{x:d,y:0,width:Pt,height:w})}else B.removeTextContent();B.setStyle(yt),B.ensureState("emphasis").style=at,B.ensureState("blur").style=pt,B.ensureState("select").style=wt,Xe(B)}U.add(B)}function H(U,B){var Q=ft(B);Q.dataIndex=o.dataIndex,Q.seriesIndex=r.seriesIndex;var Z=Math.max(f-2*d,0),rt=Math.max(p-2*d,0);if(B.culling=!0,B.setShape({x:d,y:d,width:Z,height:rt,r:A}),g)Y(B);else{B.invisible=!1;var ut=o.getVisual("style"),yt=ut.fill,at=_o(b);at.fill=yt,at.decal=ut.decal;var pt=be(_),wt=be(T),Pt=be(I);tt(B,yt,ut.opacity,null),B.setStyle(at),B.ensureState("emphasis").style=pt,B.ensureState("blur").style=wt,B.ensureState("select").style=Pt,Xe(B)}U.add(B)}function Y(U){!U.invisible&&i.push(U)}function tt(U,B,Q,Z){var rt=h.getModel(Z?wo:bo),ut=va(h.get("name"),null),yt=rt.getShallow("show");$t(U,Vt(h,Z?wo:bo),{defaultText:yt?ut:null,inheritColor:B,defaultOpacity:Q,labelFetcher:r,labelDataIndex:o.dataIndex});var at=U.getTextContent();if(at){var pt=at.style,wt=Vv(pt.padding||0);Z&&(U.setTextConfig({layoutRect:Z}),at.disableLabelLayout=!0),at.beforeUpdate=function(){var de=Math.max((Z?Z.width:U.shape.width)-wt[1]-wt[3],0),Je=Math.max((Z?Z.height:U.shape.height)-wt[0]-wt[2],0);(pt.width!==de||pt.height!==Je)&&at.setStyle({width:de,height:Je})},pt.truncateMinChar=2,pt.lineOverflow="truncate",K(pt,Z,v);var Pt=at.getState("emphasis");K(Pt?Pt.style:null,Z,v)}}function K(U,B,Q){var Z=U?U.text:null;if(!B&&Q.isLeafRoot&&Z!=null){var rt=r.get("drillDownIcon",!0);U.text=rt?rt+" "+Z:Z}}function et(U,B,Q,Z){var rt=m!=null&&t[U][m],ut=n[U];return rt?(t[U][m]=null,vt(ut,rt)):g||(rt=new B,rt instanceof dr&&(rt.z2=Cp(Q,Z)),kt(ut,rt)),e[U][S]=rt}function vt(U,B){var Q=U[S]={};B instanceof fn?(Q.oldX=B.x,Q.oldY=B.y):Q.oldShape=W({},B.shape)}function kt(U,B){var Q=U[S]={},Z=o.parentNode,rt=B instanceof X;if(Z&&(!a||a.direction==="drillDown")){var ut=0,yt=0,at=n.background[Z.getRawIndex()];!a&&at&&at.oldShape&&(ut=at.oldShape.width,yt=at.oldShape.height),rt?(Q.oldX=0,Q.oldY=yt):Q.oldShape={x:ut,y:yt,width:0,height:0}}Q.fadein=!rt}}function Cp(r,e){return r*_p+e}var Lp="itemStyle",au=fe();const Pp={seriesType:"treemap",reset:function(r){var e=r.getData().tree,t=e.root;t.isRemoved()||nu(t,{},r.getViewRoot().getAncestors(),r)}};function nu(r,e,t,a){var n=r.getModel(),i=r.getLayout(),o=r.hostTree.data;if(!(!i||i.invisible||!i.isInView)){var s=n.getModel(Lp),l=Mp(s,e,a),u=o.ensureUniqueItemVisual(r.dataIndex,"style"),v=s.get("borderColor"),c=s.get("borderColorSaturation"),h;c!=null&&(h=Ao(l),v=Ep(c,h)),u.stroke=v;var f=r.viewChildren;if(!f||!f.length)h=Ao(l),u.fill=h;else{var p=Rp(r,n,i,s,l,f);M(f,function(d,g){if(d.depth>=t.length||d===t[d.depth]){var S=Vp(n,l,d,g,p,a);nu(d,S,t,a)}})}}}function Mp(r,e,t){var a=W({},e),n=t.designatedVisualItemStyle;return M(["color","colorAlpha","colorSaturation"],function(i){n[i]=e[i];var o=r.get(i);n[i]=null,o!=null&&(a[i]=o)}),a}function Ao(r){var e=Ma(r,"color");if(e){var t=Ma(r,"colorAlpha"),a=Ma(r,"colorSaturation");return a&&(e=ol(e,null,null,a)),t&&(e=Nv(e,t)),e}}function Ep(r,e){return e!=null?ol(e,null,null,r):null}function Ma(r,e){var t=r[e];if(t!=null&&t!=="none")return t}function Rp(r,e,t,a,n,i){if(!(!i||!i.length)){var o=Ea(e,"color")||n.color!=null&&n.color!=="none"&&(Ea(e,"colorAlpha")||Ea(e,"colorSaturation"));if(o){var s=e.get("visualMin"),l=e.get("visualMax"),u=t.dataExtent.slice();s!=null&&s<u[0]&&(u[0]=s),l!=null&&l>u[1]&&(u[1]=l);var v=e.get("colorMappingBy"),c={type:o.name,dataExtent:u,visual:o.range};c.type==="color"&&(v==="index"||v==="id")?(c.mappingMethod="category",c.loop=!0):c.mappingMethod="linear";var h=new Ll(c);return au(h).drColorMappingBy=v,h}}}function Ea(r,e){var t=r.get(e);return q(t)&&t.length?{name:e,range:t}:null}function Vp(r,e,t,a,n,i){var o=W({},e);if(n){var s=n.type,l=s==="color"&&au(n).drColorMappingBy,u=l==="index"?a:l==="id"?i.mapIdToIndex(t.getId()):t.getValue(r.get("visualDimension"));o[s]=n.mapValueToVisual(u)}return o}var mr=Math.max,Qr=Math.min,To=Le,Kn=M,iu=["itemStyle","borderWidth"],Np=["itemStyle","gapWidth"],Gp=["upperLabel","show"],kp=["upperLabel","height"];const zp={seriesType:"treemap",reset:function(r,e,t,a){var n=t.getWidth(),i=t.getHeight(),o=r.option,s=pe(r.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),l=o.size||[],u=z(To(s.width,l[0]),n),v=z(To(s.height,l[1]),i),c=a&&a.type,h=["treemapZoomToNode","treemapRootToNode"],f=yr(a,h,r),p=c==="treemapRender"||c==="treemapMove"?a.rootRect:null,d=r.getViewRoot(),g=tu(d);if(c!=="treemapMove"){var S=c==="treemapZoomToNode"?Up(r,f,d,u,v):p?[p.width,p.height]:[u,v],m=o.sort;m&&m!=="asc"&&m!=="desc"&&(m="desc");var y={squareRatio:o.squareRatio,sort:m,leafDepth:o.leafDepth};d.hostTree.clearLayouts();var w={x:0,y:0,width:S[0],height:S[1],area:S[0]*S[1]};d.setLayout(w),ou(d,y,!1,0),w=d.getLayout(),Kn(g,function(b,_){var T=(g[_+1]||d).getValue();b.setLayout(W({dataExtent:[T,T],borderWidth:0,upperHeight:0},w))})}var x=r.getData().tree.root;x.setLayout($p(s,p,f),!0),r.setLayoutInfo(s),su(x,new ct(-s.x,-s.y,n,i),g,d,0)}};function ou(r,e,t,a){var n,i;if(!r.isRemoved()){var o=r.getLayout();n=o.width,i=o.height;var s=r.getModel(),l=s.get(iu),u=s.get(Np)/2,v=lu(s),c=Math.max(l,v),h=l-u,f=c-u;r.setLayout({borderWidth:l,upperHeight:c,upperLabelHeight:v},!0),n=mr(n-2*h,0),i=mr(i-h-f,0);var p=n*i,d=Op(r,s,p,e,t,a);if(d.length){var g={x:h,y:f,width:n,height:i},S=Qr(n,i),m=1/0,y=[];y.area=0;for(var w=0,x=d.length;w<x;){var b=d[w];y.push(b),y.area+=b.getLayout().area;var _=Wp(y,S,e.squareRatio);_<=m?(w++,m=_):(y.area-=y.pop().getLayout().area,Io(y,S,g,u,!1),S=Qr(g.width,g.height),y.length=y.area=0,m=1/0)}if(y.length&&Io(y,S,g,u,!0),!t){var T=s.get("childrenVisibleMin");T!=null&&p<T&&(t=!0)}for(var w=0,x=d.length;w<x;w++)ou(d[w],e,t,a+1)}}}function Op(r,e,t,a,n,i){var o=r.children||[],s=a.sort;s!=="asc"&&s!=="desc"&&(s=null);var l=a.leafDepth!=null&&a.leafDepth<=i;if(n&&!l)return r.viewChildren=[];o=Rt(o,function(f){return!f.isRemoved()}),Fp(o,s);var u=Hp(e,o,s);if(u.sum===0)return r.viewChildren=[];if(u.sum=Bp(e,t,u.sum,s,o),u.sum===0)return r.viewChildren=[];for(var v=0,c=o.length;v<c;v++){var h=o[v].getValue()/u.sum*t;o[v].setLayout({area:h})}return l&&(o.length&&r.setLayout({isLeafRoot:!0},!0),o.length=0),r.viewChildren=o,r.setLayout({dataExtent:u.dataExtent},!0),o}function Bp(r,e,t,a,n){if(!a)return t;for(var i=r.get("visibleMin"),o=n.length,s=o,l=o-1;l>=0;l--){var u=n[a==="asc"?o-l-1:l].getValue();u/t*e<i&&(s=l,t-=u)}return a==="asc"?n.splice(0,o-s):n.splice(s,o-s),t}function Fp(r,e){return e&&r.sort(function(t,a){var n=e==="asc"?t.getValue()-a.getValue():a.getValue()-t.getValue();return n===0?e==="asc"?t.dataIndex-a.dataIndex:a.dataIndex-t.dataIndex:n}),r}function Hp(r,e,t){for(var a=0,n=0,i=e.length;n<i;n++)a+=e[n].getValue();var o=r.get("visualDimension"),s;return!e||!e.length?s=[NaN,NaN]:o==="value"&&t?(s=[e[e.length-1].getValue(),e[0].getValue()],t==="asc"&&s.reverse()):(s=[1/0,-1/0],Kn(e,function(l){var u=l.getValue(o);u<s[0]&&(s[0]=u),u>s[1]&&(s[1]=u)})),{sum:a,dataExtent:s}}function Wp(r,e,t){for(var a=0,n=1/0,i=0,o=void 0,s=r.length;i<s;i++)o=r[i].getLayout().area,o&&(o<n&&(n=o),o>a&&(a=o));var l=r.area*r.area,u=e*e*t;return l?mr(u*a/l,l/(u*n)):1/0}function Io(r,e,t,a,n){var i=e===t.width?0:1,o=1-i,s=["x","y"],l=["width","height"],u=t[s[i]],v=e?r.area/e:0;(n||v>t[l[o]])&&(v=t[l[o]]);for(var c=0,h=r.length;c<h;c++){var f=r[c],p={},d=v?f.getLayout().area/v:0,g=p[l[o]]=mr(v-2*a,0),S=t[s[i]]+t[l[i]]-u,m=c===h-1||S<d?S:d,y=p[l[i]]=mr(m-2*a,0);p[s[o]]=t[s[o]]+Qr(a,g/2),p[s[i]]=u+Qr(a,y/2),u+=m,f.setLayout(p,!0)}t[s[o]]+=v,t[l[o]]-=v}function Up(r,e,t,a,n){var i=(e||{}).node,o=[a,n];if(!i||i===t)return o;for(var s,l=a*n,u=l*r.option.zoomToNodeRatio;s=i.parentNode;){for(var v=0,c=s.children,h=0,f=c.length;h<f;h++)v+=c[h].getValue();var p=i.getValue();if(p===0)return o;u*=v/p;var d=s.getModel(),g=d.get(iu),S=Math.max(g,lu(d));u+=4*g*g+(3*g+S)*Math.pow(u,.5),u>Ai&&(u=Ai),i=s}u<l&&(u=l);var m=Math.pow(u/l,.5);return[a*m,n*m]}function $p(r,e,t){if(e)return{x:e.x,y:e.y};var a={x:0,y:0};if(!t)return a;var n=t.node,i=n.getLayout();if(!i)return a;for(var o=[i.width/2,i.height/2],s=n;s;){var l=s.getLayout();o[0]+=l.x,o[1]+=l.y,s=s.parentNode}return{x:r.width/2-o[0],y:r.height/2-o[1]}}function su(r,e,t,a,n){var i=r.getLayout(),o=t[n],s=o&&o===r;if(!(o&&!s||n===t.length&&r!==a)){r.setLayout({isInView:!0,invisible:!s&&!e.intersect(i),isAboveViewRoot:s},!0);var l=new ct(e.x-i.x,e.y-i.y,e.width,e.height);Kn(r.viewChildren||[],function(u){su(u,l,t,a,n+1)})}}function lu(r){return r.get(Gp)?r.get(kp):0}function Yp(r){r.registerSeriesModel(dp),r.registerChartView(Ip),r.registerVisual(Pp),r.registerLayout(zp),pp(r)}function Zp(r){var e=r.findComponents({mainType:"legend"});!e||!e.length||r.eachSeriesByType("graph",function(t){var a=t.getCategoriesData(),n=t.getGraph(),i=n.data,o=a.mapArray(a.getName);i.filterSelf(function(s){var l=i.getItemModel(s),u=l.getShallow("category");if(u!=null){jt(u)&&(u=o[u]);for(var v=0;v<e.length;v++)if(!e[v].isSelected(u))return!1}return!0})})}function Xp(r){var e={};r.eachSeriesByType("graph",function(t){var a=t.getCategoriesData(),n=t.getData(),i={};a.each(function(o){var s=a.getName(o);i["ec-"+s]=o;var l=a.getItemModel(o),u=l.getModel("itemStyle").getItemStyle();u.fill||(u.fill=t.getColorFromPalette(s,e)),a.setItemVisual(o,"style",u);for(var v=["symbol","symbolSize","symbolKeepAspect"],c=0;c<v.length;c++){var h=l.getShallow(v[c],!0);h!=null&&a.setItemVisual(o,v[c],h)}}),a.count()&&n.each(function(o){var s=n.getItemModel(o),l=s.getShallow("category");if(l!=null){ot(l)&&(l=i["ec-"+l]);var u=a.getItemVisual(l,"style"),v=n.ensureUniqueItemVisual(o,"style");W(v,u);for(var c=["symbol","symbolSize","symbolKeepAspect"],h=0;h<c.length;h++)n.setItemVisual(o,c[h],a.getItemVisual(l,c[h]))}})})}function Gr(r){return r instanceof Array||(r=[r,r]),r}function qp(r){r.eachSeriesByType("graph",function(e){var t=e.getGraph(),a=e.getEdgeData(),n=Gr(e.get("edgeSymbol")),i=Gr(e.get("edgeSymbolSize"));a.setVisual("fromSymbol",n&&n[0]),a.setVisual("toSymbol",n&&n[1]),a.setVisual("fromSymbolSize",i&&i[0]),a.setVisual("toSymbolSize",i&&i[1]),a.setVisual("style",e.getModel("lineStyle").getLineStyle()),a.each(function(o){var s=a.getItemModel(o),l=t.getEdgeByIndex(o),u=Gr(s.getShallow("symbol",!0)),v=Gr(s.getShallow("symbolSize",!0)),c=s.getModel("lineStyle").getLineStyle(),h=a.ensureUniqueItemVisual(o,"style");switch(W(h,c),h.stroke){case"source":{var f=l.node1.getVisual("style");h.stroke=f&&f.fill;break}case"target":{var f=l.node2.getVisual("style");h.stroke=f&&f.fill;break}}u[0]&&l.setVisual("fromSymbol",u[0]),u[1]&&l.setVisual("toSymbol",u[1]),v[0]&&l.setVisual("fromSymbolSize",v[0]),v[1]&&l.setVisual("toSymbolSize",v[1])})})}var pn="-->",ma=function(r){return r.get("autoCurveness")||null},uu=function(r,e){var t=ma(r),a=20,n=[];if(jt(t))a=t;else if(q(t)){r.__curvenessList=t;return}e>a&&(a=e);var i=a%2?a+2:a+3;n=[];for(var o=0;o<i;o++)n.push((o%2?o+1:o)/10*(o%2?-1:1));r.__curvenessList=n},Sr=function(r,e,t){var a=[r.id,r.dataIndex].join("."),n=[e.id,e.dataIndex].join(".");return[t.uid,a,n].join(pn)},vu=function(r){var e=r.split(pn);return[e[0],e[2],e[1]].join(pn)},jp=function(r,e){var t=Sr(r.node1,r.node2,e);return e.__edgeMap[t]},Kp=function(r,e){var t=dn(Sr(r.node1,r.node2,e),e),a=dn(Sr(r.node2,r.node1,e),e);return t+a},dn=function(r,e){var t=e.__edgeMap;return t[r]?t[r].length:0};function Jp(r){ma(r)&&(r.__curvenessList=[],r.__edgeMap={},uu(r))}function Qp(r,e,t,a){if(ma(t)){var n=Sr(r,e,t),i=t.__edgeMap,o=i[vu(n)];i[n]&&!o?i[n].isForward=!0:o&&i[n]&&(o.isForward=!0,i[n].isForward=!1),i[n]=i[n]||[],i[n].push(a)}}function Jn(r,e,t,a){var n=ma(e),i=q(n);if(!n)return null;var o=jp(r,e);if(!o)return null;for(var s=-1,l=0;l<o.length;l++)if(o[l]===t){s=l;break}var u=Kp(r,e);uu(e,u),r.lineStyle=r.lineStyle||{};var v=Sr(r.node1,r.node2,e),c=e.__curvenessList,h=i||u%2?0:1;if(o.isForward)return c[h+s];var f=vu(v),p=dn(f,e),d=c[s+p+h];return a?i?n&&n[0]===0?(p+h)%2?d:-d:((p%2?0:1)+h)%2?d:-d:(p+h)%2?d:-d:c[s+p+h]}function cu(r){var e=r.coordinateSystem;if(!(e&&e.type!=="view")){var t=r.getGraph();t.eachNode(function(a){var n=a.getModel();a.setLayout([+n.get("x"),+n.get("y")])}),Qn(t,r)}}function Qn(r,e){r.eachEdge(function(t,a){var n=Ir(t.getModel().get(["lineStyle","curveness"]),-Jn(t,e,a,!0),0),i=se(t.node1.getLayout()),o=se(t.node2.getLayout()),s=[i,o];+n&&s.push([(i[0]+o[0])/2-(i[1]-o[1])*n,(i[1]+o[1])/2-(o[0]-i[0])*n]),t.setLayout(s)})}function td(r,e){r.eachSeriesByType("graph",function(t){var a=t.get("layout"),n=t.coordinateSystem;if(n&&n.type!=="view"){var i=t.getData(),o=[];M(n.dimensions,function(h){o=o.concat(i.mapDimensionsAll(h))});for(var s=0;s<i.count();s++){for(var l=[],u=!1,v=0;v<o.length;v++){var c=i.get(o[v],s);isNaN(c)||(u=!0),l.push(c)}u?i.setItemLayout(s,n.dataToPoint(l)):i.setItemLayout(s,[NaN,NaN])}Qn(i.graph,t)}else(!a||a==="none")&&cu(t)})}function ur(r){var e=r.coordinateSystem;if(e.type!=="view")return 1;var t=r.option.nodeScaleRatio,a=e.scaleX,n=e.getZoom(),i=(n-1)*t+1;return i/a}function vr(r){var e=r.getVisual("symbolSize");return e instanceof Array&&(e=(e[0]+e[1])/2),+e}var Do=Math.PI,Ra=[];function ti(r,e,t,a){var n=r.coordinateSystem;if(!(n&&n.type!=="view")){var i=n.getBoundingRect(),o=r.getData(),s=o.graph,l=i.width/2+i.x,u=i.height/2+i.y,v=Math.min(i.width,i.height)/2,c=o.count();if(o.setLayout({cx:l,cy:u}),!!c){if(t){var h=n.pointToData(a),f=h[0],p=h[1],d=[f-l,p-u];En(d,d),Gv(d,d,v),t.setLayout([l+d[0],u+d[1]],!0);var g=r.get(["circular","rotateLabel"]);hu(t,g,l,u)}ed[e](r,s,o,v,l,u,c),s.eachEdge(function(S,m){var y=Ir(S.getModel().get(["lineStyle","curveness"]),Jn(S,r,m),0),w=se(S.node1.getLayout()),x=se(S.node2.getLayout()),b,_=(w[0]+x[0])/2,T=(w[1]+x[1])/2;+y&&(y*=3,b=[l*y+_*(1-y),u*y+T*(1-y)]),S.setLayout([w,x,b])})}}}var ed={value:function(r,e,t,a,n,i,o){var s=0,l=t.getSum("value"),u=Math.PI*2/(l||o);e.eachNode(function(v){var c=v.getValue("value"),h=u*(l?c:1)/2;s+=h,v.setLayout([a*Math.cos(s)+n,a*Math.sin(s)+i]),s+=h})},symbolSize:function(r,e,t,a,n,i,o){var s=0;Ra.length=o;var l=ur(r);e.eachNode(function(c){var h=vr(c);isNaN(h)&&(h=2),h<0&&(h=0),h*=l;var f=Math.asin(h/2/a);isNaN(f)&&(f=Do/2),Ra[c.dataIndex]=f,s+=f*2});var u=(2*Do-s)/o/2,v=0;e.eachNode(function(c){var h=u+Ra[c.dataIndex];v+=h,(!c.getLayout()||!c.getLayout().fixed)&&c.setLayout([a*Math.cos(v)+n,a*Math.sin(v)+i]),v+=h})}};function hu(r,e,t,a){var n=r.getGraphicEl();if(n){var i=r.getModel(),o=i.get(["label","rotate"])||0,s=n.getSymbolPath();if(e){var l=r.getLayout(),u=Math.atan2(l[1]-a,l[0]-t);u<0&&(u=Math.PI*2+u);var v=l[0]<t;v&&(u=u-Math.PI);var c=v?"left":"right";s.setTextConfig({rotation:-u,position:c,origin:"center"});var h=s.ensureState("emphasis");W(h.textConfig||(h.textConfig={}),{position:c})}else s.setTextConfig({rotation:o*=Math.PI/180})}}function rd(r){r.eachSeriesByType("graph",function(e){e.get("layout")==="circular"&&ti(e,"symbolSize")})}var ze=sn;function ad(r,e,t){for(var a=r,n=e,i=t.rect,o=i.width,s=i.height,l=[i.x+o/2,i.y+s/2],u=t.gravity==null?.1:t.gravity,v=0;v<a.length;v++){var c=a[v];c.p||(c.p=kv(o*(Math.random()-.5)+l[0],s*(Math.random()-.5)+l[1])),c.pp=se(c.p),c.edges=null}var h=t.friction==null?.6:t.friction,f=h,p,d;return{warmUp:function(){f=h*.8},setFixed:function(g){a[g].fixed=!0},setUnfixed:function(g){a[g].fixed=!1},beforeStep:function(g){p=g},afterStep:function(g){d=g},step:function(g){p&&p(a,n);for(var S=[],m=a.length,y=0;y<n.length;y++){var w=n[y];if(!w.ignoreForceLayout){var x=w.n1,b=w.n2;or(S,b.p,x.p);var _=Ti(S)-w.d,T=b.w/(x.w+b.w);isNaN(T)&&(T=0),En(S,S),!x.fixed&&ze(x.p,x.p,S,T*_*f),!b.fixed&&ze(b.p,b.p,S,-(1-T)*_*f)}}for(var y=0;y<m;y++){var I=a[y];I.fixed||(or(S,l,I.p),ze(I.p,I.p,S,u*f))}for(var y=0;y<m;y++)for(var x=a[y],A=y+1;A<m;A++){var b=a[A];or(S,b.p,x.p);var _=Ti(S);_===0&&(zv(S,Math.random()-.5,Math.random()-.5),_=1);var D=(x.rep+b.rep)/_/_;!x.fixed&&ze(x.pp,x.pp,S,D),!b.fixed&&ze(b.pp,b.pp,S,-D)}for(var E=[],y=0;y<m;y++){var I=a[y];I.fixed||(or(E,I.p,I.pp),ze(I.p,I.p,E,f),mt(I.pp,I.p))}f=f*.992;var P=f<.01;d&&d(a,n,P),g&&g(P)}}}function nd(r){r.eachSeriesByType("graph",function(e){var t=e.coordinateSystem;if(!(t&&t.type!=="view"))if(e.get("layout")==="force"){var a=e.preservedPoints||{},n=e.getGraph(),i=n.data,o=n.edgeData,s=e.getModel("force"),l=s.get("initLayout");e.preservedPoints?i.each(function(y){var w=i.getId(y);i.setItemLayout(y,a[w]||[NaN,NaN])}):!l||l==="none"?cu(e):l==="circular"&&ti(e,"value");var u=i.getDataExtent("value"),v=o.getDataExtent("value"),c=s.get("repulsion"),h=s.get("edgeLength"),f=q(c)?c:[c,c],p=q(h)?h:[h,h];p=[p[1],p[0]];var d=i.mapArray("value",function(y,w){var x=i.getItemLayout(w),b=Mt(y,u,f);return isNaN(b)&&(b=(f[0]+f[1])/2),{w:b,rep:b,fixed:i.getItemModel(w).get("fixed"),p:!x||isNaN(x[0])||isNaN(x[1])?null:x}}),g=o.mapArray("value",function(y,w){var x=n.getEdgeByIndex(w),b=Mt(y,v,p);isNaN(b)&&(b=(p[0]+p[1])/2);var _=x.getModel(),T=Ir(x.getModel().get(["lineStyle","curveness"]),-Jn(x,e,w,!0),0);return{n1:d[x.node1.dataIndex],n2:d[x.node2.dataIndex],d:b,curveness:T,ignoreForceLayout:_.get("ignoreForceLayout")}}),S=t.getBoundingRect(),m=ad(d,g,{rect:S,gravity:s.get("gravity"),friction:s.get("friction")});m.beforeStep(function(y,w){for(var x=0,b=y.length;x<b;x++)y[x].fixed&&mt(y[x].p,n.getNodeByIndex(x).getLayout())}),m.afterStep(function(y,w,x){for(var b=0,_=y.length;b<_;b++)y[b].fixed||n.getNodeByIndex(b).setLayout(y[b].p),a[i.getId(b)]=y[b].p;for(var b=0,_=w.length;b<_;b++){var T=w[b],I=n.getEdgeByIndex(b),A=T.n1.p,D=T.n2.p,E=I.getLayout();E=E?E.slice():[],E[0]=E[0]||[],E[1]=E[1]||[],mt(E[0],A),mt(E[1],D),+T.curveness&&(E[2]=[(A[0]+D[0])/2-(A[1]-D[1])*T.curveness,(A[1]+D[1])/2-(D[0]-A[0])*T.curveness]),I.setLayout(E)}}),e.forceLayout=m,e.preservedPoints=a,m.step()}else e.forceLayout=null})}function id(r,e,t){var a=W(r.getBoxLayoutParams(),{aspect:t});return pe(a,{width:e.getWidth(),height:e.getHeight()})}function od(r,e){var t=[];return r.eachSeriesByType("graph",function(a){var n=a.get("coordinateSystem");if(!n||n==="view"){var i=a.getData(),o=i.mapArray(function(g){var S=i.getItemModel(g);return[+S.get("x"),+S.get("y")]}),s=[],l=[];ua(o,s,l),l[0]-s[0]===0&&(l[0]+=1,s[0]-=1),l[1]-s[1]===0&&(l[1]+=1,s[1]-=1);var u=(l[0]-s[0])/(l[1]-s[1]),v=id(a,e,u);isNaN(u)&&(s=[v.x,v.y],l=[v.x+v.width,v.y+v.height]);var c=l[0]-s[0],h=l[1]-s[1],f=v.width,p=v.height,d=a.coordinateSystem=new Lr;d.zoomLimit=a.get("scaleLimit"),d.setBoundingRect(s[0],s[1],c,h),d.setViewRect(v.x,v.y,f,p),d.setCenter(a.get("center"),e),d.setZoom(a.get("zoom")),t.push(d)}}),t}var Va=[],Na=[],Ga=[],Oe=sl,ka=Bv,Co=Math.abs;function Lo(r,e,t){for(var a=r[0],n=r[1],i=r[2],o=1/0,s,l=t*t,u=.1,v=.1;v<=.9;v+=.1){Va[0]=Oe(a[0],n[0],i[0],v),Va[1]=Oe(a[1],n[1],i[1],v);var c=Co(ka(Va,e)-l);c<o&&(o=c,s=v)}for(var h=0;h<32;h++){var f=s+u;Na[0]=Oe(a[0],n[0],i[0],s),Na[1]=Oe(a[1],n[1],i[1],s),Ga[0]=Oe(a[0],n[0],i[0],f),Ga[1]=Oe(a[1],n[1],i[1],f);var c=ka(Na,e)-l;if(Co(c)<.01)break;var p=ka(Ga,e)-l;u/=2,c<0?p>=0?s=s+u:s=s-u:p>=0?s=s-u:s=s+u}return s}function za(r,e){var t=[],a=Ov,n=[[],[],[]],i=[[],[]],o=[];e/=2,r.eachEdge(function(s,l){var u=s.getLayout(),v=s.getVisual("fromSymbol"),c=s.getVisual("toSymbol");u.__original||(u.__original=[se(u[0]),se(u[1])],u[2]&&u.__original.push(se(u[2])));var h=u.__original;if(u[2]!=null){if(mt(n[0],h[0]),mt(n[1],h[2]),mt(n[2],h[1]),v&&v!=="none"){var f=vr(s.node1),p=Lo(n,h[0],f*e);a(n[0][0],n[1][0],n[2][0],p,t),n[0][0]=t[3],n[1][0]=t[4],a(n[0][1],n[1][1],n[2][1],p,t),n[0][1]=t[3],n[1][1]=t[4]}if(c&&c!=="none"){var f=vr(s.node2),p=Lo(n,h[1],f*e);a(n[0][0],n[1][0],n[2][0],p,t),n[1][0]=t[1],n[2][0]=t[2],a(n[0][1],n[1][1],n[2][1],p,t),n[1][1]=t[1],n[2][1]=t[2]}mt(u[0],n[0]),mt(u[1],n[2]),mt(u[2],n[1])}else{if(mt(i[0],h[0]),mt(i[1],h[1]),or(o,i[1],i[0]),En(o,o),v&&v!=="none"){var f=vr(s.node1);sn(i[0],i[0],o,f*e)}if(c&&c!=="none"){var f=vr(s.node2);sn(i[1],i[1],o,-f*e)}mt(u[0],i[0]),mt(u[1],i[1])}})}function Po(r){return r.type==="view"}var sd=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,a){var n=new Fn,i=new Pl,o=this.group;this._controller=new fa(a.getZr()),this._controllerHost={target:o},o.add(n.group),o.add(i.group),this._symbolDraw=n,this._lineDraw=i,this._firstRender=!0},e.prototype.render=function(t,a,n){var i=this,o=t.coordinateSystem;this._model=t;var s=this._symbolDraw,l=this._lineDraw,u=this.group;if(Po(o)){var v={x:o.x,y:o.y,scaleX:o.scaleX,scaleY:o.scaleY};this._firstRender?u.attr(v):ht(u,v,t)}za(t.getGraph(),ur(t));var c=t.getData();s.updateData(c);var h=t.getEdgeData();l.updateData(h),this._updateNodeAndLinkScale(),this._updateController(t,a,n),clearTimeout(this._layoutTimeout);var f=t.forceLayout,p=t.get(["force","layoutAnimation"]);f&&this._startForceLayoutIteration(f,p);var d=t.get("layout");c.graph.eachNode(function(y){var w=y.dataIndex,x=y.getGraphicEl(),b=y.getModel();if(x){x.off("drag").off("dragend");var _=b.get("draggable");_&&x.on("drag",function(I){switch(d){case"force":f.warmUp(),!i._layouting&&i._startForceLayoutIteration(f,p),f.setFixed(w),c.setItemLayout(w,[x.x,x.y]);break;case"circular":c.setItemLayout(w,[x.x,x.y]),y.setLayout({fixed:!0},!0),ti(t,"symbolSize",y,[I.offsetX,I.offsetY]),i.updateLayout(t);break;case"none":default:c.setItemLayout(w,[x.x,x.y]),Qn(t.getGraph(),t),i.updateLayout(t);break}}).on("dragend",function(){f&&f.setUnfixed(w)}),x.setDraggable(_,!!b.get("cursor"));var T=b.get(["emphasis","focus"]);T==="adjacency"&&(ft(x).focus=y.getAdjacentDataIndices())}}),c.graph.eachEdge(function(y){var w=y.getGraphicEl(),x=y.getModel().get(["emphasis","focus"]);w&&x==="adjacency"&&(ft(w).focus={edge:[y.dataIndex],node:[y.node1.dataIndex,y.node2.dataIndex]})});var g=t.get("layout")==="circular"&&t.get(["circular","rotateLabel"]),S=c.getLayout("cx"),m=c.getLayout("cy");c.graph.eachNode(function(y){hu(y,g,S,m)}),this._firstRender=!1},e.prototype.dispose=function(){this.remove(),this._controller&&this._controller.dispose(),this._controllerHost=null},e.prototype._startForceLayoutIteration=function(t,a){var n=this;(function i(){t.step(function(o){n.updateLayout(n._model),(n._layouting=!o)&&(a?n._layoutTimeout=setTimeout(i,16):i())})})()},e.prototype._updateController=function(t,a,n){var i=this,o=this._controller,s=this._controllerHost,l=this.group;if(o.setPointerChecker(function(u,v,c){var h=l.getBoundingRect();return h.applyTransform(l.transform),h.contain(v,c)&&!Bn(u,n,t)}),!Po(t.coordinateSystem)){o.disable();return}o.enable(t.get("roam")),s.zoomLimit=t.get("scaleLimit"),s.zoom=t.coordinateSystem.getZoom(),o.off("pan").off("zoom").on("pan",function(u){Un(s,u.dx,u.dy),n.dispatchAction({seriesId:t.id,type:"graphRoam",dx:u.dx,dy:u.dy})}).on("zoom",function(u){$n(s,u.scale,u.originX,u.originY),n.dispatchAction({seriesId:t.id,type:"graphRoam",zoom:u.scale,originX:u.originX,originY:u.originY}),i._updateNodeAndLinkScale(),za(t.getGraph(),ur(t)),i._lineDraw.updateLayout(),n.updateLabelLayout()})},e.prototype._updateNodeAndLinkScale=function(){var t=this._model,a=t.getData(),n=ur(t);a.eachItemGraphicEl(function(i,o){i&&i.setSymbolScale(n)})},e.prototype.updateLayout=function(t){za(t.getGraph(),ur(t)),this._symbolDraw.updateLayout(),this._lineDraw.updateLayout()},e.prototype.remove=function(){clearTimeout(this._layoutTimeout),this._layouting=!1,this._layoutTimeout=null,this._symbolDraw&&this._symbolDraw.remove(),this._lineDraw&&this._lineDraw.remove()},e.type="graph",e}(gt);function Be(r){return"_EC_"+r}var ld=function(){function r(e){this.type="graph",this.nodes=[],this.edges=[],this._nodesMap={},this._edgesMap={},this._directed=e||!1}return r.prototype.isDirected=function(){return this._directed},r.prototype.addNode=function(e,t){e=e==null?""+t:""+e;var a=this._nodesMap;if(!a[Be(e)]){var n=new we(e,t);return n.hostGraph=this,this.nodes.push(n),a[Be(e)]=n,n}},r.prototype.getNodeByIndex=function(e){var t=this.data.getRawIndex(e);return this.nodes[t]},r.prototype.getNodeById=function(e){return this._nodesMap[Be(e)]},r.prototype.addEdge=function(e,t,a){var n=this._nodesMap,i=this._edgesMap;if(jt(e)&&(e=this.nodes[e]),jt(t)&&(t=this.nodes[t]),e instanceof we||(e=n[Be(e)]),t instanceof we||(t=n[Be(t)]),!(!e||!t)){var o=e.id+"-"+t.id,s=new fu(e,t,a);return s.hostGraph=this,this._directed&&(e.outEdges.push(s),t.inEdges.push(s)),e.edges.push(s),e!==t&&t.edges.push(s),this.edges.push(s),i[o]=s,s}},r.prototype.getEdgeByIndex=function(e){var t=this.edgeData.getRawIndex(e);return this.edges[t]},r.prototype.getEdge=function(e,t){e instanceof we&&(e=e.id),t instanceof we&&(t=t.id);var a=this._edgesMap;return this._directed?a[e+"-"+t]:a[e+"-"+t]||a[t+"-"+e]},r.prototype.eachNode=function(e,t){for(var a=this.nodes,n=a.length,i=0;i<n;i++)a[i].dataIndex>=0&&e.call(t,a[i],i)},r.prototype.eachEdge=function(e,t){for(var a=this.edges,n=a.length,i=0;i<n;i++)a[i].dataIndex>=0&&a[i].node1.dataIndex>=0&&a[i].node2.dataIndex>=0&&e.call(t,a[i],i)},r.prototype.breadthFirstTraverse=function(e,t,a,n){if(t instanceof we||(t=this._nodesMap[Be(t)]),!!t){for(var i=a==="out"?"outEdges":a==="in"?"inEdges":"edges",o=0;o<this.nodes.length;o++)this.nodes[o].__visited=!1;if(!e.call(n,t,null))for(var s=[t];s.length;)for(var l=s.shift(),u=l[i],o=0;o<u.length;o++){var v=u[o],c=v.node1===l?v.node2:v.node1;if(!c.__visited){if(e.call(n,c,l))return;s.push(c),c.__visited=!0}}}},r.prototype.update=function(){for(var e=this.data,t=this.edgeData,a=this.nodes,n=this.edges,i=0,o=a.length;i<o;i++)a[i].dataIndex=-1;for(var i=0,o=e.count();i<o;i++)a[e.getRawIndex(i)].dataIndex=i;t.filterSelf(function(s){var l=n[t.getRawIndex(s)];return l.node1.dataIndex>=0&&l.node2.dataIndex>=0});for(var i=0,o=n.length;i<o;i++)n[i].dataIndex=-1;for(var i=0,o=t.count();i<o;i++)n[t.getRawIndex(i)].dataIndex=i},r.prototype.clone=function(){for(var e=new r(this._directed),t=this.nodes,a=this.edges,n=0;n<t.length;n++)e.addNode(t[n].id,t[n].dataIndex);for(var n=0;n<a.length;n++){var i=a[n];e.addEdge(i.node1.id,i.node2.id,i.dataIndex)}return e},r}(),we=function(){function r(e,t){this.inEdges=[],this.outEdges=[],this.edges=[],this.dataIndex=-1,this.id=e??"",this.dataIndex=t??-1}return r.prototype.degree=function(){return this.edges.length},r.prototype.inDegree=function(){return this.inEdges.length},r.prototype.outDegree=function(){return this.outEdges.length},r.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,a=t.data.getItemModel(this.dataIndex);return a.getModel(e)}},r.prototype.getAdjacentDataIndices=function(){for(var e={edge:[],node:[]},t=0;t<this.edges.length;t++){var a=this.edges[t];a.dataIndex<0||(e.edge.push(a.dataIndex),e.node.push(a.node1.dataIndex,a.node2.dataIndex))}return e},r.prototype.getTrajectoryDataIndices=function(){for(var e=J(),t=J(),a=0;a<this.edges.length;a++){var n=this.edges[a];if(!(n.dataIndex<0)){e.set(n.dataIndex,!0);for(var i=[n.node1],o=[n.node2],s=0;s<i.length;){var l=i[s];s++,t.set(l.dataIndex,!0);for(var u=0;u<l.inEdges.length;u++)e.set(l.inEdges[u].dataIndex,!0),i.push(l.inEdges[u].node1)}for(s=0;s<o.length;){var v=o[s];s++,t.set(v.dataIndex,!0);for(var u=0;u<v.outEdges.length;u++)e.set(v.outEdges[u].dataIndex,!0),o.push(v.outEdges[u].node2)}}}return{edge:e.keys(),node:t.keys()}},r}(),fu=function(){function r(e,t,a){this.dataIndex=-1,this.node1=e,this.node2=t,this.dataIndex=a??-1}return r.prototype.getModel=function(e){if(!(this.dataIndex<0)){var t=this.hostGraph,a=t.edgeData.getItemModel(this.dataIndex);return a.getModel(e)}},r.prototype.getAdjacentDataIndices=function(){return{edge:[this.dataIndex],node:[this.node1.dataIndex,this.node2.dataIndex]}},r.prototype.getTrajectoryDataIndices=function(){var e=J(),t=J();e.set(this.dataIndex,!0);for(var a=[this.node1],n=[this.node2],i=0;i<a.length;){var o=a[i];i++,t.set(o.dataIndex,!0);for(var s=0;s<o.inEdges.length;s++)e.set(o.inEdges[s].dataIndex,!0),a.push(o.inEdges[s].node1)}for(i=0;i<n.length;){var l=n[i];i++,t.set(l.dataIndex,!0);for(var s=0;s<l.outEdges.length;s++)e.set(l.outEdges[s].dataIndex,!0),n.push(l.outEdges[s].node2)}return{edge:e.keys(),node:t.keys()}},r}();function pu(r,e){return{getValue:function(t){var a=this[r][e];return a.getStore().get(a.getDimensionIndex(t||"value"),this.dataIndex)},setVisual:function(t,a){this.dataIndex>=0&&this[r][e].setItemVisual(this.dataIndex,t,a)},getVisual:function(t){return this[r][e].getItemVisual(this.dataIndex,t)},setLayout:function(t,a){this.dataIndex>=0&&this[r][e].setItemLayout(this.dataIndex,t,a)},getLayout:function(){return this[r][e].getItemLayout(this.dataIndex)},getGraphicEl:function(){return this[r][e].getItemGraphicEl(this.dataIndex)},getRawIndex:function(){return this[r][e].getRawIndex(this.dataIndex)}}}Ve(we,pu("hostGraph","data"));Ve(fu,pu("hostGraph","edgeData"));function du(r,e,t,a,n){for(var i=new ld(a),o=0;o<r.length;o++)i.addNode(Le(r[o].id,r[o].name,o),o);for(var s=[],l=[],u=0,o=0;o<e.length;o++){var v=e[o],c=v.source,h=v.target;i.addEdge(c,h,u)&&(l.push(v),s.push(Le(va(v.id,null),c+" > "+h)),u++)}var f=t.get("coordinateSystem"),p;if(f==="cartesian2d"||f==="polar")p=Cr(r,t);else{var d=ll.get(f),g=d?d.dimensions||[]:[];Ge(g,"value")<0&&g.concat(["value"]);var S=Mn(r,{coordDimensions:g,encodeDefine:t.getEncode()}).dimensions;p=new Pe(S,t),p.initData(r)}var m=new Pe(["value"],t);return m.initData(l,s),n&&n(p,m),Jl({mainData:p,struct:i,structAttr:"graph",datas:{node:p,edge:m},datasAttr:{node:"data",edge:"edgeData"}}),i.update(),i}var ud=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.init=function(t){r.prototype.init.apply(this,arguments);var a=this;function n(){return a._categoriesData}this.legendVisualProvider=new pa(n,n),this.fillDataTextStyle(t.edges||t.links),this._updateCategoriesData()},e.prototype.mergeOption=function(t){r.prototype.mergeOption.apply(this,arguments),this.fillDataTextStyle(t.edges||t.links),this._updateCategoriesData()},e.prototype.mergeDefaultAndTheme=function(t){r.prototype.mergeDefaultAndTheme.apply(this,arguments),Pn(t,"edgeLabel",["show"])},e.prototype.getInitialData=function(t,a){var n=t.edges||t.links||[],i=t.data||t.nodes||[],o=this;if(i&&n){Jp(this);var s=du(i,n,this,!0,l);return M(s.edges,function(u){Qp(u.node1,u.node2,this,u.dataIndex)},this),s.data}function l(u,v){u.wrapMethod("getItemModel",function(p){var d=o._categoriesModels,g=p.getShallow("category"),S=d[g];return S&&(S.parentModel=p.parentModel,p.parentModel=S),p});var c=Lt.prototype.getModel;function h(p,d){var g=c.call(this,p,d);return g.resolveParentPath=f,g}v.wrapMethod("getItemModel",function(p){return p.resolveParentPath=f,p.getModel=h,p});function f(p){if(p&&(p[0]==="label"||p[1]==="label")){var d=p.slice();return p[0]==="label"?d[0]="edgeLabel":p[1]==="label"&&(d[1]="edgeLabel"),d}return p}}},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.getCategoriesData=function(){return this._categoriesData},e.prototype.formatTooltip=function(t,a,n){if(n==="edge"){var i=this.getData(),o=this.getDataParams(t,n),s=i.graph.getEdgeByIndex(t),l=i.getName(s.node1.dataIndex),u=i.getName(s.node2.dataIndex),v=[];return l!=null&&v.push(l),u!=null&&v.push(u),Nt("nameValue",{name:v.join(" > "),value:o.value,noValue:o.value==null})}var c=Fv({series:this,dataIndex:t,multipleSeries:a});return c},e.prototype._updateCategoriesData=function(){var t=F(this.option.categories||[],function(n){return n.value!=null?n:W({value:0},n)}),a=new Pe(["value"],this);a.initData(t),this._categoriesData=a,this._categoriesModels=a.mapArray(function(n){return a.getItemModel(n)})},e.prototype.setZoom=function(t){this.option.zoom=t},e.prototype.setCenter=function(t){this.option.center=t},e.prototype.isAnimationEnabled=function(){return r.prototype.isAnimationEnabled.call(this)&&!(this.get("layout")==="force"&&this.get(["force","layoutAnimation"]))},e.type="series.graph",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={z:2,coordinateSystem:"view",legendHoverLink:!0,layout:null,circular:{rotateLabel:!1},force:{initLayout:null,repulsion:[0,50],gravity:.1,friction:.6,edgeLength:30,layoutAnimation:!0},left:"center",top:"center",symbol:"circle",symbolSize:10,edgeSymbol:["none","none"],edgeSymbolSize:10,edgeLabel:{position:"middle",distance:5},draggable:!1,roam:!1,center:null,zoom:1,nodeScaleRatio:.6,label:{show:!1,formatter:"{b}"},itemStyle:{},lineStyle:{color:"#aaa",width:1,opacity:.5},emphasis:{scale:!0,label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(St),vd={type:"graphRoam",event:"graphRoam",update:"none"};function cd(r){r.registerChartView(sd),r.registerSeriesModel(ud),r.registerProcessor(Zp),r.registerVisual(Xp),r.registerVisual(qp),r.registerLayout(td),r.registerLayout(r.PRIORITY.VISUAL.POST_CHART_LAYOUT,rd),r.registerLayout(nd),r.registerCoordinateSystem("graphView",{dimensions:Lr.dimensions,create:od}),r.registerAction({type:"focusNodeAdjacency",event:"focusNodeAdjacency",update:"series:focusNodeAdjacency"},qe),r.registerAction({type:"unfocusNodeAdjacency",event:"unfocusNodeAdjacency",update:"series:unfocusNodeAdjacency"},qe),r.registerAction(vd,function(e,t,a){t.eachComponent({mainType:"series",query:e},function(n){var i=n.coordinateSystem,o=Zn(i,e,void 0,a);n.setCenter&&n.setCenter(o.center),n.setZoom&&n.setZoom(o.zoom)})})}var hd=function(){function r(){this.angle=0,this.width=10,this.r=10,this.x=0,this.y=0}return r}(),fd=function(r){G(e,r);function e(t){var a=r.call(this,t)||this;return a.type="pointer",a}return e.prototype.getDefaultShape=function(){return new hd},e.prototype.buildPath=function(t,a){var n=Math.cos,i=Math.sin,o=a.r,s=a.width,l=a.angle,u=a.x-n(l)*s*(s>=o/3?1:2),v=a.y-i(l)*s*(s>=o/3?1:2);l=a.angle-Math.PI/2,t.moveTo(u,v),t.lineTo(a.x+n(l)*s,a.y+i(l)*s),t.lineTo(a.x+n(a.angle)*o,a.y+i(a.angle)*o),t.lineTo(a.x-n(l)*s,a.y-i(l)*s),t.lineTo(u,v)},e}(Gt);function pd(r,e){var t=r.get("center"),a=e.getWidth(),n=e.getHeight(),i=Math.min(a,n),o=z(t[0],e.getWidth()),s=z(t[1],e.getHeight()),l=z(r.get("radius"),i/2);return{cx:o,cy:s,r:l}}function kr(r,e){var t=r==null?"":r+"";return e&&(ot(e)?t=e.replace("{value}",t):st(e)&&(t=e(r))),t}var dd=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n){this.group.removeAll();var i=t.get(["axisLine","lineStyle","color"]),o=pd(t,n);this._renderMain(t,a,n,i,o),this._data=t.getData()},e.prototype.dispose=function(){},e.prototype._renderMain=function(t,a,n,i,o){var s=this.group,l=t.get("clockwise"),u=-t.get("startAngle")/180*Math.PI,v=-t.get("endAngle")/180*Math.PI,c=t.getModel("axisLine"),h=c.get("roundCap"),f=h?Ni:Me,p=c.get("show"),d=c.getModel("lineStyle"),g=d.get("width"),S=[u,v];Hv(S,!l),u=S[0],v=S[1];for(var m=v-u,y=u,w=[],x=0;p&&x<i.length;x++){var b=Math.min(Math.max(i[x][0],0),1);v=u+m*b;var _=new f({shape:{startAngle:y,endAngle:v,cx:o.cx,cy:o.cy,clockwise:l,r0:o.r-g,r:o.r},silent:!0});_.setStyle({fill:i[x][1]}),_.setStyle(d.getLineStyle(["color","width"])),w.push(_),y=v}w.reverse(),M(w,function(I){return s.add(I)});var T=function(I){if(I<=0)return i[0][1];var A;for(A=0;A<i.length;A++)if(i[A][0]>=I&&(A===0?0:i[A-1][0])<I)return i[A][1];return i[A-1][1]};this._renderTicks(t,a,n,T,o,u,v,l,g),this._renderTitleAndDetail(t,a,n,T,o),this._renderAnchor(t,o),this._renderPointer(t,a,n,T,o,u,v,l,g)},e.prototype._renderTicks=function(t,a,n,i,o,s,l,u,v){for(var c=this.group,h=o.cx,f=o.cy,p=o.r,d=+t.get("min"),g=+t.get("max"),S=t.getModel("splitLine"),m=t.getModel("axisTick"),y=t.getModel("axisLabel"),w=t.get("splitNumber"),x=m.get("splitNumber"),b=z(S.get("length"),p),_=z(m.get("length"),p),T=s,I=(l-s)/w,A=I/x,D=S.getModel("lineStyle").getLineStyle(),E=m.getModel("lineStyle").getLineStyle(),P=S.get("distance"),C,L,R=0;R<=w;R++){if(C=Math.cos(T),L=Math.sin(T),S.get("show")){var V=P?P+v:v,N=new re({shape:{x1:C*(p-V)+h,y1:L*(p-V)+f,x2:C*(p-b-V)+h,y2:L*(p-b-V)+f},style:D,silent:!0});D.stroke==="auto"&&N.setStyle({stroke:i(R/w)}),c.add(N)}if(y.get("show")){var V=y.get("distance")+P,k=kr(ul(R/w*(g-d)+d),y.get("formatter")),O=i(R/w),H=C*(p-b-V)+h,Y=L*(p-b-V)+f,tt=y.get("rotate"),K=0;tt==="radial"?(K=-T+2*Math.PI,K>Math.PI/2&&(K+=Math.PI)):tt==="tangential"?K=-T-Math.PI/2:jt(tt)&&(K=tt*Math.PI/180),K===0?c.add(new Yt({style:Bt(y,{text:k,x:H,y:Y,verticalAlign:L<-.8?"top":L>.8?"bottom":"middle",align:C<-.4?"left":C>.4?"right":"center"},{inheritColor:O}),silent:!0})):c.add(new Yt({style:Bt(y,{text:k,x:H,y:Y,verticalAlign:"middle",align:"center"},{inheritColor:O}),silent:!0,originX:H,originY:Y,rotation:K}))}if(m.get("show")&&R!==w){var V=m.get("distance");V=V?V+v:v;for(var et=0;et<=x;et++){C=Math.cos(T),L=Math.sin(T);var vt=new re({shape:{x1:C*(p-V)+h,y1:L*(p-V)+f,x2:C*(p-_-V)+h,y2:L*(p-_-V)+f},silent:!0,style:E});E.stroke==="auto"&&vt.setStyle({stroke:i((R+et/x)/w)}),c.add(vt),T+=A}T-=A}else T+=I}},e.prototype._renderPointer=function(t,a,n,i,o,s,l,u,v){var c=this.group,h=this._data,f=this._progressEls,p=[],d=t.get(["pointer","show"]),g=t.getModel("progress"),S=g.get("show"),m=t.getData(),y=m.mapDimension("value"),w=+t.get("min"),x=+t.get("max"),b=[w,x],_=[s,l];function T(A,D){var E=m.getItemModel(A),P=E.getModel("pointer"),C=z(P.get("width"),o.r),L=z(P.get("length"),o.r),R=t.get(["pointer","icon"]),V=P.get("offsetCenter"),N=z(V[0],o.r),k=z(V[1],o.r),O=P.get("keepAspect"),H;return R?H=De(R,N-C/2,k-L,C,L,null,O):H=new fd({shape:{angle:-Math.PI/2,width:C,r:L,x:N,y:k}}),H.rotation=-(D+Math.PI/2),H.x=o.cx,H.y=o.cy,H}function I(A,D){var E=g.get("roundCap"),P=E?Ni:Me,C=g.get("overlap"),L=C?g.get("width"):v/m.count(),R=C?o.r-L:o.r-(A+1)*L,V=C?o.r:o.r-A*L,N=new P({shape:{startAngle:s,endAngle:D,cx:o.cx,cy:o.cy,clockwise:u,r0:R,r:V}});return C&&(N.z2=Mt(m.get(y,A),[w,x],[100,0],!0)),N}(S||d)&&(m.diff(h).add(function(A){var D=m.get(y,A);if(d){var E=T(A,s);Ht(E,{rotation:-((isNaN(+D)?_[0]:Mt(D,b,_,!0))+Math.PI/2)},t),c.add(E),m.setItemGraphicEl(A,E)}if(S){var P=I(A,s),C=g.get("clip");Ht(P,{shape:{endAngle:Mt(D,b,_,C)}},t),c.add(P),Ii(t.seriesIndex,m.dataType,A,P),p[A]=P}}).update(function(A,D){var E=m.get(y,A);if(d){var P=h.getItemGraphicEl(D),C=P?P.rotation:s,L=T(A,C);L.rotation=C,ht(L,{rotation:-((isNaN(+E)?_[0]:Mt(E,b,_,!0))+Math.PI/2)},t),c.add(L),m.setItemGraphicEl(A,L)}if(S){var R=f[D],V=R?R.shape.endAngle:s,N=I(A,V),k=g.get("clip");ht(N,{shape:{endAngle:Mt(E,b,_,k)}},t),c.add(N),Ii(t.seriesIndex,m.dataType,A,N),p[A]=N}}).execute(),m.each(function(A){var D=m.getItemModel(A),E=D.getModel("emphasis"),P=E.get("focus"),C=E.get("blurScope"),L=E.get("disabled");if(d){var R=m.getItemGraphicEl(A),V=m.getItemVisual(A,"style"),N=V.fill;if(R instanceof ce){var k=R.style;R.useStyle(W({image:k.image,x:k.x,y:k.y,width:k.width,height:k.height},V))}else R.useStyle(V),R.type!=="pointer"&&R.setColor(N);R.setStyle(D.getModel(["pointer","itemStyle"]).getItemStyle()),R.style.fill==="auto"&&R.setStyle("fill",i(Mt(m.get(y,A),b,[0,1],!0))),R.z2EmphasisLift=0,Wt(R,D),dt(R,P,C,L)}if(S){var O=p[A];O.useStyle(m.getItemVisual(A,"style")),O.setStyle(D.getModel(["progress","itemStyle"]).getItemStyle()),O.z2EmphasisLift=0,Wt(O,D),dt(O,P,C,L)}}),this._progressEls=p)},e.prototype._renderAnchor=function(t,a){var n=t.getModel("anchor"),i=n.get("show");if(i){var o=n.get("size"),s=n.get("icon"),l=n.get("offsetCenter"),u=n.get("keepAspect"),v=De(s,a.cx-o/2+z(l[0],a.r),a.cy-o/2+z(l[1],a.r),o,o,null,u);v.z2=n.get("showAbove")?1:0,v.setStyle(n.getModel("itemStyle").getItemStyle()),this.group.add(v)}},e.prototype._renderTitleAndDetail=function(t,a,n,i,o){var s=this,l=t.getData(),u=l.mapDimension("value"),v=+t.get("min"),c=+t.get("max"),h=new X,f=[],p=[],d=t.isAnimationEnabled(),g=t.get(["pointer","showAbove"]);l.diff(this._data).add(function(S){f[S]=new Yt({silent:!0}),p[S]=new Yt({silent:!0})}).update(function(S,m){f[S]=s._titleEls[m],p[S]=s._detailEls[m]}).execute(),l.each(function(S){var m=l.getItemModel(S),y=l.get(u,S),w=new X,x=i(Mt(y,[v,c],[0,1],!0)),b=m.getModel("title");if(b.get("show")){var _=b.get("offsetCenter"),T=o.cx+z(_[0],o.r),I=o.cy+z(_[1],o.r),A=f[S];A.attr({z2:g?0:2,style:Bt(b,{x:T,y:I,text:l.getName(S),align:"center",verticalAlign:"middle"},{inheritColor:x})}),w.add(A)}var D=m.getModel("detail");if(D.get("show")){var E=D.get("offsetCenter"),P=o.cx+z(E[0],o.r),C=o.cy+z(E[1],o.r),L=z(D.get("width"),o.r),R=z(D.get("height"),o.r),V=t.get(["progress","show"])?l.getItemVisual(S,"style").fill:x,A=p[S],N=D.get("formatter");A.attr({z2:g?0:2,style:Bt(D,{x:P,y:C,text:kr(y,N),width:isNaN(L)?null:L,height:isNaN(R)?null:R,align:"center",verticalAlign:"middle"},{inheritColor:V})}),Wv(A,{normal:D},y,function(O){return kr(O,N)}),d&&Uv(A,S,l,t,{getFormattedLabel:function(O,H,Y,tt,K,et){return kr(et?et.interpolatedValue:y,N)}}),w.add(A)}h.add(w)}),this.group.add(h),this._titleEls=f,this._detailEls=p},e.type="gauge",e}(gt),gd=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="itemStyle",t}return e.prototype.getInitialData=function(t,a){return la(this,["value"])},e.type="series.gauge",e.defaultOption={z:2,colorBy:"data",center:["50%","50%"],legendHoverLink:!0,radius:"75%",startAngle:225,endAngle:-45,clockwise:!0,min:0,max:100,splitNumber:10,axisLine:{show:!0,roundCap:!1,lineStyle:{color:[[1,"#E6EBF8"]],width:10}},progress:{show:!1,overlap:!0,width:10,roundCap:!1,clip:!0},splitLine:{show:!0,length:10,distance:10,lineStyle:{color:"#63677A",width:3,type:"solid"}},axisTick:{show:!0,splitNumber:5,length:6,distance:10,lineStyle:{color:"#63677A",width:1,type:"solid"}},axisLabel:{show:!0,distance:15,color:"#464646",fontSize:12,rotate:0},pointer:{icon:null,offsetCenter:[0,0],show:!0,showAbove:!0,length:"60%",width:6,keepAspect:!1},anchor:{show:!1,showAbove:!1,size:6,icon:"circle",offsetCenter:[0,0],keepAspect:!1,itemStyle:{color:"#fff",borderWidth:0,borderColor:"#5470c6"}},title:{show:!0,offsetCenter:[0,"20%"],color:"#464646",fontSize:16,valueAnimation:!1},detail:{show:!0,backgroundColor:"rgba(0,0,0,0)",borderWidth:0,borderColor:"#ccc",width:100,height:null,padding:[5,10],offsetCenter:[0,"40%"],color:"#464646",fontSize:30,fontWeight:"bold",lineHeight:30,valueAnimation:!1}},e}(St);function yd(r){r.registerChartView(dd),r.registerSeriesModel(gd)}var md=["itemStyle","opacity"],Sd=function(r){G(e,r);function e(t,a){var n=r.call(this)||this,i=n,o=new Re,s=new Yt;return i.setTextContent(s),n.setTextGuideLine(o),n.updateData(t,a,!0),n}return e.prototype.updateData=function(t,a,n){var i=this,o=t.hostModel,s=t.getItemModel(a),l=t.getItemLayout(a),u=s.getModel("emphasis"),v=s.get(md);v=v??1,n||Ze(i),i.useStyle(t.getItemVisual(a,"style")),i.style.lineJoin="round",n?(i.setShape({points:l.points}),i.style.opacity=0,Ht(i,{style:{opacity:v}},o,a)):ht(i,{style:{opacity:v},shape:{points:l.points}},o,a),Wt(i,s),this._updateLabel(t,a),dt(this,u.get("focus"),u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t,a){var n=this,i=this.getTextGuideLine(),o=n.getTextContent(),s=t.hostModel,l=t.getItemModel(a),u=t.getItemLayout(a),v=u.label,c=t.getItemVisual(a,"style"),h=c.fill;$t(o,Vt(l),{labelFetcher:t.hostModel,labelDataIndex:a,defaultOpacity:c.opacity,defaultText:t.getName(a)},{normal:{align:v.textAlign,verticalAlign:v.verticalAlign}}),n.setTextConfig({local:!0,inside:!!v.inside,insideStroke:h,outsideFill:h});var f=v.linePoints;i.setShape({points:f}),n.textGuideLineConfig={anchor:f?new ae(f[0][0],f[0][1]):null},ht(o,{style:{x:v.x,y:v.y}},s,a),o.attr({rotation:v.rotation,originX:v.x,originY:v.y,z2:10}),Rh(n,Vh(l),{stroke:h})},e}(Ee),xd=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.ignoreLabelLineUpdate=!0,t}return e.prototype.render=function(t,a,n){var i=t.getData(),o=this._data,s=this.group;i.diff(o).add(function(l){var u=new Sd(i,l);i.setItemGraphicEl(l,u),s.add(u)}).update(function(l,u){var v=o.getItemGraphicEl(u);v.updateData(i,l),s.add(v),i.setItemGraphicEl(l,v)}).remove(function(l){var u=o.getItemGraphicEl(l);$v(u,t,l)}).execute(),this._data=i},e.prototype.remove=function(){this.group.removeAll(),this._data=null},e.prototype.dispose=function(){},e.type="funnel",e}(gt),bd=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t){r.prototype.init.apply(this,arguments),this.legendVisualProvider=new pa(it(this.getData,this),it(this.getRawData,this)),this._defaultLabelLine(t)},e.prototype.getInitialData=function(t,a){return la(this,{coordDimensions:["value"],encodeDefaulter:bt(rl,this)})},e.prototype._defaultLabelLine=function(t){Pn(t,"labelLine",["show"]);var a=t.labelLine,n=t.emphasis.labelLine;a.show=a.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},e.prototype.getDataParams=function(t){var a=this.getData(),n=r.prototype.getDataParams.call(this,t),i=a.mapDimension("value"),o=a.getSum(i);return n.percent=o?+(a.get(i,t)/o*100).toFixed(2):0,n.$vars.push("percent"),n},e.type="series.funnel",e.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",left:80,top:60,right:80,bottom:60,minSize:"0%",maxSize:"100%",sort:"descending",orient:"vertical",gap:0,funnelAlign:"center",label:{show:!0,position:"outer"},labelLine:{show:!0,length:20,lineStyle:{width:1}},itemStyle:{borderColor:"#fff",borderWidth:1},emphasis:{label:{show:!0}},select:{itemStyle:{borderColor:"#212121"}}},e}(St);function wd(r,e){return pe(r.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function _d(r,e){for(var t=r.mapDimension("value"),a=r.mapArray(t,function(l){return l}),n=[],i=e==="ascending",o=0,s=r.count();o<s;o++)n[o]=o;return st(e)?n.sort(e):e!=="none"&&n.sort(function(l,u){return i?a[l]-a[u]:a[u]-a[l]}),n}function Ad(r){var e=r.hostModel,t=e.get("orient");r.each(function(a){var n=r.getItemModel(a),i=n.getModel("label"),o=i.get("position"),s=n.getModel("labelLine"),l=r.getItemLayout(a),u=l.points,v=o==="inner"||o==="inside"||o==="center"||o==="insideLeft"||o==="insideRight",c,h,f,p;if(v)o==="insideLeft"?(h=(u[0][0]+u[3][0])/2+5,f=(u[0][1]+u[3][1])/2,c="left"):o==="insideRight"?(h=(u[1][0]+u[2][0])/2-5,f=(u[1][1]+u[2][1])/2,c="right"):(h=(u[0][0]+u[1][0]+u[2][0]+u[3][0])/4,f=(u[0][1]+u[1][1]+u[2][1]+u[3][1])/4,c="center"),p=[[h,f],[h,f]];else{var d=void 0,g=void 0,S=void 0,m=void 0,y=s.get("length");o==="left"?(d=(u[3][0]+u[0][0])/2,g=(u[3][1]+u[0][1])/2,S=d-y,h=S-5,c="right"):o==="right"?(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,S=d+y,h=S+5,c="left"):o==="top"?(d=(u[3][0]+u[0][0])/2,g=(u[3][1]+u[0][1])/2,m=g-y,f=m-5,c="center"):o==="bottom"?(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,m=g+y,f=m+5,c="center"):o==="rightTop"?(d=t==="horizontal"?u[3][0]:u[1][0],g=t==="horizontal"?u[3][1]:u[1][1],t==="horizontal"?(m=g-y,f=m-5,c="center"):(S=d+y,h=S+5,c="top")):o==="rightBottom"?(d=u[2][0],g=u[2][1],t==="horizontal"?(m=g+y,f=m+5,c="center"):(S=d+y,h=S+5,c="bottom")):o==="leftTop"?(d=u[0][0],g=t==="horizontal"?u[0][1]:u[1][1],t==="horizontal"?(m=g-y,f=m-5,c="center"):(S=d-y,h=S-5,c="right")):o==="leftBottom"?(d=t==="horizontal"?u[1][0]:u[3][0],g=t==="horizontal"?u[1][1]:u[2][1],t==="horizontal"?(m=g+y,f=m+5,c="center"):(S=d-y,h=S-5,c="right")):(d=(u[1][0]+u[2][0])/2,g=(u[1][1]+u[2][1])/2,t==="horizontal"?(m=g+y,f=m+5,c="center"):(S=d+y,h=S+5,c="left")),t==="horizontal"?(S=d,h=S):(m=g,f=m),p=[[d,g],[S,m]]}l.label={linePoints:p,x:h,y:f,verticalAlign:"middle",textAlign:c,inside:v}})}function Td(r,e){r.eachSeriesByType("funnel",function(t){var a=t.getData(),n=a.mapDimension("value"),i=t.get("sort"),o=wd(t,e),s=t.get("orient"),l=o.width,u=o.height,v=_d(a,i),c=o.x,h=o.y,f=s==="horizontal"?[z(t.get("minSize"),u),z(t.get("maxSize"),u)]:[z(t.get("minSize"),l),z(t.get("maxSize"),l)],p=a.getDataExtent(n),d=t.get("min"),g=t.get("max");d==null&&(d=Math.min(p[0],0)),g==null&&(g=p[1]);var S=t.get("funnelAlign"),m=t.get("gap"),y=s==="horizontal"?l:u,w=(y-m*(a.count()-1))/a.count(),x=function(C,L){if(s==="horizontal"){var R=a.get(n,C)||0,V=Mt(R,[d,g],f,!0),N=void 0;switch(S){case"top":N=h;break;case"center":N=h+(u-V)/2;break;case"bottom":N=h+(u-V);break}return[[L,N],[L,N+V]]}var k=a.get(n,C)||0,O=Mt(k,[d,g],f,!0),H;switch(S){case"left":H=c;break;case"center":H=c+(l-O)/2;break;case"right":H=c+l-O;break}return[[H,L],[H+O,L]]};i==="ascending"&&(w=-w,m=-m,s==="horizontal"?c+=l:h+=u,v=v.reverse());for(var b=0;b<v.length;b++){var _=v[b],T=v[b+1],I=a.getItemModel(_);if(s==="horizontal"){var A=I.get(["itemStyle","width"]);A==null?A=w:(A=z(A,l),i==="ascending"&&(A=-A));var D=x(_,c),E=x(T,c+A);c+=A+m,a.setItemLayout(_,{points:D.concat(E.slice().reverse())})}else{var P=I.get(["itemStyle","height"]);P==null?P=w:(P=z(P,u),i==="ascending"&&(P=-P));var D=x(_,h),E=x(T,h+P);h+=P+m,a.setItemLayout(_,{points:D.concat(E.slice().reverse())})}}Ad(a)})}function Id(r){r.registerChartView(xd),r.registerSeriesModel(bd),r.registerLayout(Td),r.registerProcessor(da("funnel"))}var Dd=.3,Cd=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t._dataGroup=new X,t._initialized=!1,t}return e.prototype.init=function(){this.group.add(this._dataGroup)},e.prototype.render=function(t,a,n,i){this._progressiveEls=null;var o=this._dataGroup,s=t.getData(),l=this._data,u=t.coordinateSystem,v=u.dimensions,c=Eo(t);s.diff(l).add(h).update(f).remove(p).execute();function h(g){var S=Mo(s,o,g,v,u);Oa(S,s,g,c)}function f(g,S){var m=l.getItemGraphicEl(S),y=gu(s,g,v,u);s.setItemGraphicEl(g,m),ht(m,{shape:{points:y}},t,g),Ze(m),Oa(m,s,g,c)}function p(g){var S=l.getItemGraphicEl(g);o.remove(S)}if(!this._initialized){this._initialized=!0;var d=Ld(u,t,function(){setTimeout(function(){o.removeClipPath()})});o.setClipPath(d)}this._data=s},e.prototype.incrementalPrepareRender=function(t,a,n){this._initialized=!0,this._data=null,this._dataGroup.removeAll()},e.prototype.incrementalRender=function(t,a,n){for(var i=a.getData(),o=a.coordinateSystem,s=o.dimensions,l=Eo(a),u=this._progressiveEls=[],v=t.start;v<t.end;v++){var c=Mo(i,this._dataGroup,v,s,o);c.incremental=!0,Oa(c,i,v,l),u.push(c)}},e.prototype.remove=function(){this._dataGroup&&this._dataGroup.removeAll(),this._data=null},e.type="parallel",e}(gt);function Ld(r,e,t){var a=r.model,n=r.getRect(),i=new At({shape:{x:n.x,y:n.y,width:n.width,height:n.height}}),o=a.get("layout")==="horizontal"?"width":"height";return i.setShape(o,0),Ht(i,{shape:{width:n.width,height:n.height}},e,t),i}function gu(r,e,t,a){for(var n=[],i=0;i<t.length;i++){var o=t[i],s=r.get(r.mapDimension(o),e);Pd(s,a.getAxis(o).type)||n.push(a.dataToPoint(s,o))}return n}function Mo(r,e,t,a,n){var i=gu(r,t,a,n),o=new Re({shape:{points:i},z2:10});return e.add(o),r.setItemGraphicEl(t,o),o}function Eo(r){var e=r.get("smooth",!0);return e===!0&&(e=Dd),e=Yv(e),Zv(e)&&(e=0),{smooth:e}}function Oa(r,e,t,a){r.useStyle(e.getItemVisual(t,"style")),r.style.fill=null,r.setShape("smooth",a.smooth);var n=e.getItemModel(t),i=n.getModel("emphasis");Wt(r,n,"lineStyle"),dt(r,i.get("focus"),i.get("blurScope"),i.get("disabled"))}function Pd(r,e){return e==="category"?r==null:r==null||isNaN(r)}var Md=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="lineStyle",t.visualDrawType="stroke",t}return e.prototype.getInitialData=function(t,a){return Cr(null,this,{useEncodeDefaulter:it(Ed,null,this)})},e.prototype.getRawIndicesByActiveState=function(t){var a=this.coordinateSystem,n=this.getData(),i=[];return a.eachActiveState(n,function(o,s){t===o&&i.push(n.getRawIndex(s))}),i},e.type="series.parallel",e.dependencies=["parallel"],e.defaultOption={z:2,coordinateSystem:"parallel",parallelIndex:0,label:{show:!1},inactiveOpacity:.05,activeOpacity:1,lineStyle:{width:1,opacity:.45,type:"solid"},emphasis:{label:{show:!1}},progressive:500,smooth:!1,animationEasing:"linear"},e}(St);function Ed(r){var e=r.ecModel.getComponent("parallel",r.get("parallelIndex"));if(e){var t={};return M(e.dimensions,function(a){var n=Rd(a);t[a]=n}),t}}function Rd(r){return+r.replace("dim","")}var Vd=["lineStyle","opacity"],Nd={seriesType:"parallel",reset:function(r,e){var t=r.coordinateSystem,a={normal:r.get(["lineStyle","opacity"]),active:r.get("activeOpacity"),inactive:r.get("inactiveOpacity")};return{progress:function(n,i){t.eachActiveState(i,function(o,s){var l=a[o];if(o==="normal"&&i.hasItemOption){var u=i.getItemModel(s).get(Vd,!0);u!=null&&(l=u)}var v=i.ensureUniqueItemVisual(s,"style");v.opacity=l},n.start,n.end)}}}};function Gd(r){kd(r),zd(r)}function kd(r){if(!r.parallel){var e=!1;M(r.series,function(t){t&&t.type==="parallel"&&(e=!0)}),e&&(r.parallel=[{}])}}function zd(r){var e=Ft(r.parallelAxis);M(e,function(t){if(Rn(t)){var a=t.parallelIndex||0,n=Ft(r.parallel)[a];n&&n.parallelAxisDefault&&qt(t,n.parallelAxisDefault,!1)}})}var Od=5,Bd=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n){this._model=t,this._api=n,this._handlers||(this._handlers={},M(Fd,function(i,o){n.getZr().on(o,this._handlers[o]=it(i,this))},this)),vl(this,"_throttledDispatchExpand",t.get("axisExpandRate"),"fixRate")},e.prototype.dispose=function(t,a){Xv(this,"_throttledDispatchExpand"),M(this._handlers,function(n,i){a.getZr().off(i,n)}),this._handlers=null},e.prototype._throttledDispatchExpand=function(t){this._dispatchExpand(t)},e.prototype._dispatchExpand=function(t){t&&this._api.dispatchAction(W({type:"parallelAxisExpand"},t))},e.type="parallel",e}(Ne),Fd={mousedown:function(r){Ba(this,"click")&&(this._mouseDownPoint=[r.offsetX,r.offsetY])},mouseup:function(r){var e=this._mouseDownPoint;if(Ba(this,"click")&&e){var t=[r.offsetX,r.offsetY],a=Math.pow(e[0]-t[0],2)+Math.pow(e[1]-t[1],2);if(a>Od)return;var n=this._model.coordinateSystem.getSlidedAxisExpandWindow([r.offsetX,r.offsetY]);n.behavior!=="none"&&this._dispatchExpand({axisExpandWindow:n.axisExpandWindow})}this._mouseDownPoint=null},mousemove:function(r){if(!(this._mouseDownPoint||!Ba(this,"mousemove"))){var e=this._model,t=e.coordinateSystem.getSlidedAxisExpandWindow([r.offsetX,r.offsetY]),a=t.behavior;a==="jump"&&this._throttledDispatchExpand.debounceNextCall(e.get("axisExpandDebounce")),this._throttledDispatchExpand(a==="none"?null:{axisExpandWindow:t.axisExpandWindow,animation:a==="jump"?null:{duration:0}})}}};function Ba(r,e){var t=r._model;return t.get("axisExpandable")&&t.get("axisExpandTriggerOn")===e}var Hd=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){r.prototype.init.apply(this,arguments),this.mergeOption({})},e.prototype.mergeOption=function(t){var a=this.option;t&&qt(a,t,!0),this._initDimensions()},e.prototype.contains=function(t,a){var n=t.get("parallelIndex");return n!=null&&a.getComponent("parallel",n)===this},e.prototype.setAxisExpand=function(t){M(["axisExpandable","axisExpandCenter","axisExpandCount","axisExpandWidth","axisExpandWindow"],function(a){t.hasOwnProperty(a)&&(this.option[a]=t[a])},this)},e.prototype._initDimensions=function(){var t=this.dimensions=[],a=this.parallelAxisIndex=[],n=Rt(this.ecModel.queryComponents({mainType:"parallelAxis"}),function(i){return(i.get("parallelIndex")||0)===this.componentIndex},this);M(n,function(i){t.push("dim"+i.get("dim")),a.push(i.componentIndex)})},e.type="parallel",e.dependencies=["parallelAxis"],e.layoutMode="box",e.defaultOption={z:0,left:80,top:60,right:80,bottom:60,layout:"horizontal",axisExpandable:!1,axisExpandCenter:null,axisExpandCount:0,axisExpandWidth:50,axisExpandRate:17,axisExpandDebounce:50,axisExpandSlideTriggerArea:[-.15,.05,.4],axisExpandTriggerOn:"click",parallelAxisDefault:null},e}(he),Wd=function(r){G(e,r);function e(t,a,n,i,o){var s=r.call(this,t,a,n)||this;return s.type=i||"value",s.axisIndex=o,s}return e.prototype.isHorizontal=function(){return this.coordinateSystem.getModel().get("layout")!=="horizontal"},e}(Jt),Fa=M,yu=Math.min,mu=Math.max,Ro=Math.floor,Ud=Math.ceil,Vo=ul,$d=Math.PI,Yd=function(){function r(e,t,a){this.type="parallel",this._axesMap=J(),this._axesLayout={},this.dimensions=e.dimensions,this._model=e,this._init(e,t,a)}return r.prototype._init=function(e,t,a){var n=e.dimensions,i=e.parallelAxisIndex;Fa(n,function(o,s){var l=i[s],u=t.getComponent("parallelAxis",l),v=this._axesMap.set(o,new Wd(o,Vn(u),[0,0],u.get("type"),l)),c=v.type==="category";v.onBand=c&&u.get("boundaryGap"),v.inverse=u.get("inverse"),u.axis=v,v.model=u,v.coordinateSystem=u.coordinateSystem=this},this)},r.prototype.update=function(e,t){this._updateAxesFromSeries(this._model,e)},r.prototype.containPoint=function(e){var t=this._makeLayoutInfo(),a=t.axisBase,n=t.layoutBase,i=t.pixelDimIndex,o=e[1-i],s=e[i];return o>=a&&o<=a+t.axisLength&&s>=n&&s<=n+t.layoutLength},r.prototype.getModel=function(){return this._model},r.prototype._updateAxesFromSeries=function(e,t){t.eachSeries(function(a){if(e.contains(a,t)){var n=a.getData();Fa(this.dimensions,function(i){var o=this._axesMap.get(i);o.scale.unionExtentFromData(n,n.mapDimension(i)),Zr(o.scale,o.model)},this)}},this)},r.prototype.resize=function(e,t){this._rect=pe(e.getBoxLayoutParams(),{width:t.getWidth(),height:t.getHeight()}),this._layoutAxes()},r.prototype.getRect=function(){return this._rect},r.prototype._makeLayoutInfo=function(){var e=this._model,t=this._rect,a=["x","y"],n=["width","height"],i=e.get("layout"),o=i==="horizontal"?0:1,s=t[n[o]],l=[0,s],u=this.dimensions.length,v=zr(e.get("axisExpandWidth"),l),c=zr(e.get("axisExpandCount")||0,[0,u]),h=e.get("axisExpandable")&&u>3&&u>c&&c>1&&v>0&&s>0,f=e.get("axisExpandWindow"),p;if(f)p=zr(f[1]-f[0],l),f[1]=f[0]+p;else{p=zr(v*(c-1),l);var d=e.get("axisExpandCenter")||Ro(u/2);f=[v*d-p/2],f[1]=f[0]+p}var g=(s-p)/(u-c);g<3&&(g=0);var S=[Ro(Vo(f[0]/v,1))+1,Ud(Vo(f[1]/v,1))-1],m=g/v*f[0];return{layout:i,pixelDimIndex:o,layoutBase:t[a[o]],layoutLength:s,axisBase:t[a[1-o]],axisLength:t[n[1-o]],axisExpandable:h,axisExpandWidth:v,axisCollapseWidth:g,axisExpandWindow:f,axisCount:u,winInnerIndices:S,axisExpandWindow0Pos:m}},r.prototype._layoutAxes=function(){var e=this._rect,t=this._axesMap,a=this.dimensions,n=this._makeLayoutInfo(),i=n.layout;t.each(function(o){var s=[0,n.axisLength],l=o.inverse?1:0;o.setExtent(s[l],s[1-l])}),Fa(a,function(o,s){var l=(n.axisExpandable?Xd:Zd)(s,n),u={horizontal:{x:l.position,y:n.axisLength},vertical:{x:0,y:l.position}},v={horizontal:$d/2,vertical:0},c=[u[i].x+e.x,u[i].y+e.y],h=v[i],f=Ar();Cn(f,f,h),pr(f,f,c),this._axesLayout[o]={position:c,rotation:h,transform:f,axisNameAvailableWidth:l.axisNameAvailableWidth,axisLabelShow:l.axisLabelShow,nameTruncateMaxWidth:l.nameTruncateMaxWidth,tickDirection:1,labelDirection:1}},this)},r.prototype.getAxis=function(e){return this._axesMap.get(e)},r.prototype.dataToPoint=function(e,t){return this.axisCoordToPoint(this._axesMap.get(t).dataToCoord(e),t)},r.prototype.eachActiveState=function(e,t,a,n){a==null&&(a=0),n==null&&(n=e.count());var i=this._axesMap,o=this.dimensions,s=[],l=[];M(o,function(g){s.push(e.mapDimension(g)),l.push(i.get(g).model)});for(var u=this.hasAxisBrushed(),v=a;v<n;v++){var c=void 0;if(!u)c="normal";else{c="active";for(var h=e.getValues(s,v),f=0,p=o.length;f<p;f++){var d=l[f].getActiveState(h[f]);if(d==="inactive"){c="inactive";break}}}t(c,v)}},r.prototype.hasAxisBrushed=function(){for(var e=this.dimensions,t=this._axesMap,a=!1,n=0,i=e.length;n<i;n++)t.get(e[n]).model.getActiveState()!=="normal"&&(a=!0);return a},r.prototype.axisCoordToPoint=function(e,t){var a=this._axesLayout[t];return cl([e,0],a.transform)},r.prototype.getAxisLayout=function(e){return Ut(this._axesLayout[e])},r.prototype.getSlidedAxisExpandWindow=function(e){var t=this._makeLayoutInfo(),a=t.pixelDimIndex,n=t.axisExpandWindow.slice(),i=n[1]-n[0],o=[0,t.axisExpandWidth*(t.axisCount-1)];if(!this.containPoint(e))return{behavior:"none",axisExpandWindow:n};var s=e[a]-t.layoutBase-t.axisExpandWindow0Pos,l,u="slide",v=t.axisCollapseWidth,c=this._model.get("axisExpandSlideTriggerArea"),h=c[0]!=null;if(v)h&&v&&s<i*c[0]?(u="jump",l=s-i*c[2]):h&&v&&s>i*(1-c[0])?(u="jump",l=s-i*(1-c[2])):(l=s-i*c[1])>=0&&(l=s-i*(1-c[1]))<=0&&(l=0),l*=t.axisExpandWidth/v,l?jc(l,n,o,"all"):u="none";else{var f=n[1]-n[0],p=o[1]*s/f;n=[mu(0,p-f/2)],n[1]=yu(o[1],n[0]+f),n[0]=n[1]-f}return{axisExpandWindow:n,behavior:u}},r}();function zr(r,e){return yu(mu(r,e[0]),e[1])}function Zd(r,e){var t=e.layoutLength/(e.axisCount-1);return{position:t*r,axisNameAvailableWidth:t,axisLabelShow:!0}}function Xd(r,e){var t=e.layoutLength,a=e.axisExpandWidth,n=e.axisCount,i=e.axisCollapseWidth,o=e.winInnerIndices,s,l=i,u=!1,v;return r<o[0]?(s=r*i,v=i):r<=o[1]?(s=e.axisExpandWindow0Pos+r*a-e.axisExpandWindow[0],l=a,u=!0):(s=t-(n-1-r)*i,v=i),{position:s,axisNameAvailableWidth:l,axisLabelShow:u,nameTruncateMaxWidth:v}}function qd(r,e){var t=[];return r.eachComponent("parallel",function(a,n){var i=new Yd(a,r,e);i.name="parallel_"+n,i.resize(a,e),a.coordinateSystem=i,i.model=a,t.push(i)}),r.eachSeries(function(a){if(a.get("coordinateSystem")==="parallel"){var n=a.getReferringComponents("parallel",Ke).models[0];a.coordinateSystem=n.coordinateSystem}}),t}var jd={create:qd},gn=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.activeIntervals=[],t}return e.prototype.getAreaSelectStyle=function(){return il([["fill","color"],["lineWidth","borderWidth"],["stroke","borderColor"],["width","width"],["opacity","opacity"]])(this.getModel("areaSelectStyle"))},e.prototype.setActiveIntervals=function(t){var a=this.activeIntervals=Ut(t);if(a)for(var n=a.length-1;n>=0;n--)qv(a[n])},e.prototype.getActiveState=function(t){var a=this.activeIntervals;if(!a.length)return"normal";if(t==null||isNaN(+t))return"inactive";if(a.length===1){var n=a[0];if(n[0]<=t&&t<=n[1])return"active"}else for(var i=0,o=a.length;i<o;i++)if(a[i][0]<=t&&t<=a[i][1])return"active";return"inactive"},e}(he);Ve(gn,ha);var Kd=["axisLine","axisTickLabel","axisName"],Jd=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,a){r.prototype.init.apply(this,arguments),(this._brushController=new Ml(a.getZr())).on("brush",it(this._onBrush,this))},e.prototype.render=function(t,a,n,i){if(!Qd(t,a,i)){this.axisModel=t,this.api=n,this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new X,this.group.add(this._axisGroup),!!t.get("show")){var s=eg(t,a),l=s.coordinateSystem,u=t.getAreaSelectStyle(),v=u.width,c=t.axis.dim,h=l.getAxisLayout(c),f=W({strokeContainThreshold:v},h),p=new Ce(t,f);M(Kd,p.add,p),this._axisGroup.add(p.getGroup()),this._refreshBrushController(f,u,t,s,v,n),Nn(o,this._axisGroup,t)}}},e.prototype._refreshBrushController=function(t,a,n,i,o,s){var l=n.axis.getExtent(),u=l[1]-l[0],v=Math.min(30,Math.abs(u)*.1),c=ct.create({x:l[0],y:-o/2,width:u,height:o});c.x-=v,c.width+=2*v,this._brushController.mount({enableGlobalPan:!0,rotation:t.rotation,x:t.position[0],y:t.position[1]}).setPanels([{panelId:"pl",clipPath:Qc(c),isTargetByCursor:Jc(c,s,i),getLinearBrushOtherExtent:Kc(c,0)}]).enableBrush({brushType:"lineX",brushStyle:a,removeOnClick:!0}).updateCovers(tg(n))},e.prototype._onBrush=function(t){var a=t.areas,n=this.axisModel,i=n.axis,o=F(a,function(s){return[i.coordToData(s.range[0],!0),i.coordToData(s.range[1],!0)]});(!n.option.realtime===t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"axisAreaSelect",parallelAxisId:n.id,intervals:o})},e.prototype.dispose=function(){this._brushController.dispose()},e.type="parallelAxis",e}(Ne);function Qd(r,e,t){return t&&t.type==="axisAreaSelect"&&e.findComponents({mainType:"parallelAxis",query:t})[0]===r}function tg(r){var e=r.axis;return F(r.activeIntervals,function(t){return{brushType:"lineX",panelId:"pl",range:[e.dataToCoord(t[0],!0),e.dataToCoord(t[1],!0)]}})}function eg(r,e){return e.getComponent("parallel",r.get("parallelIndex"))}var rg={type:"axisAreaSelect",event:"axisAreaSelected"};function ag(r){r.registerAction(rg,function(e,t){t.eachComponent({mainType:"parallelAxis",query:e},function(a){a.axis.model.setActiveIntervals(e.intervals)})}),r.registerAction("parallelAxisExpand",function(e,t){t.eachComponent({mainType:"parallel",query:e},function(a){a.setAxisExpand(e)})})}var ng={type:"value",areaSelectStyle:{width:20,borderWidth:1,borderColor:"rgba(160,197,232)",color:"rgba(160,197,232)",opacity:.3},realtime:!0,z:10};function Su(r){r.registerComponentView(Bd),r.registerComponentModel(Hd),r.registerCoordinateSystem("parallel",jd),r.registerPreprocessor(Gd),r.registerComponentModel(gn),r.registerComponentView(Jd),qr(r,"parallel",gn,ng),ag(r)}function ig(r){$(Su),r.registerChartView(Cd),r.registerSeriesModel(Md),r.registerVisual(r.PRIORITY.VISUAL.BRUSH,Nd)}var og=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.cpx2=0,this.cpy2=0,this.extent=0}return r}(),sg=function(r){G(e,r);function e(t){return r.call(this,t)||this}return e.prototype.getDefaultShape=function(){return new og},e.prototype.buildPath=function(t,a){var n=a.extent;t.moveTo(a.x1,a.y1),t.bezierCurveTo(a.cpx1,a.cpy1,a.cpx2,a.cpy2,a.x2,a.y2),a.orient==="vertical"?(t.lineTo(a.x2+n,a.y2),t.bezierCurveTo(a.cpx2+n,a.cpy2,a.cpx1+n,a.cpy1,a.x1+n,a.y1)):(t.lineTo(a.x2,a.y2+n),t.bezierCurveTo(a.cpx2,a.cpy2+n,a.cpx1,a.cpy1+n,a.x1,a.y1+n)),t.closePath()},e.prototype.highlight=function(){hl(this)},e.prototype.downplay=function(){fl(this)},e}(Gt),lg=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t._focusAdjacencyDisabled=!1,t}return e.prototype.render=function(t,a,n){var i=this,o=t.getGraph(),s=this.group,l=t.layoutInfo,u=l.width,v=l.height,c=t.getData(),h=t.getData("edge"),f=t.get("orient");this._model=t,s.removeAll(),s.x=l.x,s.y=l.y,o.eachEdge(function(p){var d=new sg,g=ft(d);g.dataIndex=p.dataIndex,g.seriesIndex=t.seriesIndex,g.dataType="edge";var S=p.getModel(),m=S.getModel("lineStyle"),y=m.get("curveness"),w=p.node1.getLayout(),x=p.node1.getModel(),b=x.get("localX"),_=x.get("localY"),T=p.node2.getLayout(),I=p.node2.getModel(),A=I.get("localX"),D=I.get("localY"),E=p.getLayout(),P,C,L,R,V,N,k,O;d.shape.extent=Math.max(1,E.dy),d.shape.orient=f,f==="vertical"?(P=(b!=null?b*u:w.x)+E.sy,C=(_!=null?_*v:w.y)+w.dy,L=(A!=null?A*u:T.x)+E.ty,R=D!=null?D*v:T.y,V=P,N=C*(1-y)+R*y,k=L,O=C*y+R*(1-y)):(P=(b!=null?b*u:w.x)+w.dx,C=(_!=null?_*v:w.y)+E.sy,L=A!=null?A*u:T.x,R=(D!=null?D*v:T.y)+E.ty,V=P*(1-y)+L*y,N=C,k=P*y+L*(1-y),O=R),d.setShape({x1:P,y1:C,x2:L,y2:R,cpx1:V,cpy1:N,cpx2:k,cpy2:O}),d.useStyle(m.getItemStyle()),No(d.style,f,p);var H=""+S.get("value"),Y=Vt(S,"edgeLabel");$t(d,Y,{labelFetcher:{getFormattedLabel:function(et,vt,kt,U,B,Q){return t.getFormattedLabel(et,vt,"edge",U,Ir(B,Y.normal&&Y.normal.get("formatter"),H),Q)}},labelDataIndex:p.dataIndex,defaultText:H}),d.setTextConfig({position:"inside"});var tt=S.getModel("emphasis");Wt(d,S,"lineStyle",function(et){var vt=et.getItemStyle();return No(vt,f,p),vt}),s.add(d),h.setItemGraphicEl(p.dataIndex,d);var K=tt.get("focus");dt(d,K==="adjacency"?p.getAdjacentDataIndices():K==="trajectory"?p.getTrajectoryDataIndices():K,tt.get("blurScope"),tt.get("disabled"))}),o.eachNode(function(p){var d=p.getLayout(),g=p.getModel(),S=g.get("localX"),m=g.get("localY"),y=g.getModel("emphasis"),w=g.get(["itemStyle","borderRadius"])||0,x=new At({shape:{x:S!=null?S*u:d.x,y:m!=null?m*v:d.y,width:d.dx,height:d.dy,r:w},style:g.getModel("itemStyle").getItemStyle(),z2:10});$t(x,Vt(g),{labelFetcher:{getFormattedLabel:function(_,T){return t.getFormattedLabel(_,T,"node")}},labelDataIndex:p.dataIndex,defaultText:p.id}),x.disableLabelAnimation=!0,x.setStyle("fill",p.getVisual("color")),x.setStyle("decal",p.getVisual("style").decal),Wt(x,g),s.add(x),c.setItemGraphicEl(p.dataIndex,x),ft(x).dataType="node";var b=y.get("focus");dt(x,b==="adjacency"?p.getAdjacentDataIndices():b==="trajectory"?p.getTrajectoryDataIndices():b,y.get("blurScope"),y.get("disabled"))}),c.eachItemGraphicEl(function(p,d){var g=c.getItemModel(d);g.get("draggable")&&(p.drift=function(S,m){i._focusAdjacencyDisabled=!0,this.shape.x+=S,this.shape.y+=m,this.dirty(),n.dispatchAction({type:"dragNode",seriesId:t.id,dataIndex:c.getRawIndex(d),localX:this.shape.x/u,localY:this.shape.y/v})},p.ondragend=function(){i._focusAdjacencyDisabled=!1},p.draggable=!0,p.cursor="move")}),!this._data&&t.isAnimationEnabled()&&s.setClipPath(ug(s.getBoundingRect(),t,function(){s.removeClipPath()})),this._data=t.getData()},e.prototype.dispose=function(){},e.type="sankey",e}(gt);function No(r,e,t){switch(r.fill){case"source":r.fill=t.node1.getVisual("color"),r.decal=t.node1.getVisual("style").decal;break;case"target":r.fill=t.node2.getVisual("color"),r.decal=t.node2.getVisual("style").decal;break;case"gradient":var a=t.node1.getVisual("color"),n=t.node2.getVisual("color");ot(a)&&ot(n)&&(r.fill=new Js(0,0,+(e==="horizontal"),+(e==="vertical"),[{color:a,offset:0},{color:n,offset:1}]))}}function ug(r,e,t){var a=new At({shape:{x:r.x-10,y:r.y-10,width:0,height:r.height+20}});return Ht(a,{shape:{width:r.width+20}},e,t),a}var vg=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,a){var n=t.edges||t.links||[],i=t.data||t.nodes||[],o=t.levels||[];this.levelModels=[];for(var s=this.levelModels,l=0;l<o.length;l++)o[l].depth!=null&&o[l].depth>=0&&(s[o[l].depth]=new Lt(o[l],this,a));var u=du(i,n,this,!0,v);return u.data;function v(c,h){c.wrapMethod("getItemModel",function(f,p){var d=f.parentModel,g=d.getData().getItemLayout(p);if(g){var S=g.depth,m=d.levelModels[S];m&&(f.parentModel=m)}return f}),h.wrapMethod("getItemModel",function(f,p){var d=f.parentModel,g=d.getGraph().getEdgeByIndex(p),S=g.node1.getLayout();if(S){var m=S.depth,y=d.levelModels[m];y&&(f.parentModel=y)}return f})}},e.prototype.setNodePosition=function(t,a){var n=this.option.data||this.option.nodes,i=n[t];i.localX=a[0],i.localY=a[1]},e.prototype.getGraph=function(){return this.getData().graph},e.prototype.getEdgeData=function(){return this.getGraph().edgeData},e.prototype.formatTooltip=function(t,a,n){function i(f){return isNaN(f)||f==null}if(n==="edge"){var o=this.getDataParams(t,n),s=o.data,l=o.value,u=s.source+" -- "+s.target;return Nt("nameValue",{name:u,value:l,noValue:i(l)})}else{var v=this.getGraph().getNodeByIndex(t),c=v.getLayout().value,h=this.getDataParams(t,n).data.name;return Nt("nameValue",{name:h!=null?h+"":null,value:c,noValue:i(c)})}},e.prototype.optionUpdated=function(){},e.prototype.getDataParams=function(t,a){var n=r.prototype.getDataParams.call(this,t,a);if(n.value==null&&a==="node"){var i=this.getGraph().getNodeByIndex(t),o=i.getLayout().value;n.value=o}return n},e.type="series.sankey",e.defaultOption={z:2,coordinateSystem:"view",left:"5%",top:"5%",right:"20%",bottom:"5%",orient:"horizontal",nodeWidth:20,nodeGap:8,draggable:!0,layoutIterations:32,label:{show:!0,position:"right",fontSize:12},edgeLabel:{show:!1,fontSize:12},levels:[],nodeAlign:"justify",lineStyle:{color:"#314656",opacity:.2,curveness:.5},emphasis:{label:{show:!0},lineStyle:{opacity:.5}},select:{itemStyle:{borderColor:"#212121"}},animationEasing:"linear",animationDuration:1e3},e}(St);function cg(r,e){r.eachSeriesByType("sankey",function(t){var a=t.get("nodeWidth"),n=t.get("nodeGap"),i=hg(t,e);t.layoutInfo=i;var o=i.width,s=i.height,l=t.getGraph(),u=l.nodes,v=l.edges;pg(u);var c=Rt(u,function(d){return d.getLayout().value===0}),h=c.length!==0?0:t.get("layoutIterations"),f=t.get("orient"),p=t.get("nodeAlign");fg(u,v,a,n,o,s,h,f,p)})}function hg(r,e){return pe(r.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function fg(r,e,t,a,n,i,o,s,l){dg(r,e,t,n,i,s,l),Sg(r,e,i,n,a,o,s),Cg(r,s)}function pg(r){M(r,function(e){var t=le(e.outEdges,ta),a=le(e.inEdges,ta),n=e.getValue()||0,i=Math.max(t,a,n);e.setLayout({value:i},!0)})}function dg(r,e,t,a,n,i,o){for(var s=[],l=[],u=[],v=[],c=0,h=0;h<e.length;h++)s[h]=1;for(var h=0;h<r.length;h++)l[h]=r[h].inEdges.length,l[h]===0&&u.push(r[h]);for(var f=-1;u.length;){for(var p=0;p<u.length;p++){var d=u[p],g=d.hostGraph.data.getRawDataItem(d.dataIndex),S=g.depth!=null&&g.depth>=0;S&&g.depth>f&&(f=g.depth),d.setLayout({depth:S?g.depth:c},!0),i==="vertical"?d.setLayout({dy:t},!0):d.setLayout({dx:t},!0);for(var m=0;m<d.outEdges.length;m++){var y=d.outEdges[m],w=e.indexOf(y);s[w]=0;var x=y.node2,b=r.indexOf(x);--l[b]===0&&v.indexOf(x)<0&&v.push(x)}}++c,u=v,v=[]}for(var h=0;h<s.length;h++)if(s[h]===1)throw new Error("Sankey is a DAG, the original data has cycle!");var _=f>c-1?f:c-1;o&&o!=="left"&&gg(r,o,i,_);var T=i==="vertical"?(n-t)/_:(a-t)/_;mg(r,T,i)}function xu(r){var e=r.hostGraph.data.getRawDataItem(r.dataIndex);return e.depth!=null&&e.depth>=0}function gg(r,e,t,a){if(e==="right"){for(var n=[],i=r,o=0;i.length;){for(var s=0;s<i.length;s++){var l=i[s];l.setLayout({skNodeHeight:o},!0);for(var u=0;u<l.inEdges.length;u++){var v=l.inEdges[u];n.indexOf(v.node1)<0&&n.push(v.node1)}}i=n,n=[],++o}M(r,function(c){xu(c)||c.setLayout({depth:Math.max(0,a-c.getLayout().skNodeHeight)},!0)})}else e==="justify"&&yg(r,a)}function yg(r,e){M(r,function(t){!xu(t)&&!t.outEdges.length&&t.setLayout({depth:e},!0)})}function mg(r,e,t){M(r,function(a){var n=a.getLayout().depth*e;t==="vertical"?a.setLayout({y:n},!0):a.setLayout({x:n},!0)})}function Sg(r,e,t,a,n,i,o){var s=xg(r,o);bg(s,e,t,a,n,o),Ha(s,n,t,a,o);for(var l=1;i>0;i--)l*=.99,wg(s,l,o),Ha(s,n,t,a,o),Dg(s,l,o),Ha(s,n,t,a,o)}function xg(r,e){var t=[],a=e==="vertical"?"y":"x",n=ln(r,function(i){return i.getLayout()[a]});return n.keys.sort(function(i,o){return i-o}),M(n.keys,function(i){t.push(n.buckets.get(i))}),t}function bg(r,e,t,a,n,i){var o=1/0;M(r,function(s){var l=s.length,u=0;M(s,function(c){u+=c.getLayout().value});var v=i==="vertical"?(a-(l-1)*n)/u:(t-(l-1)*n)/u;v<o&&(o=v)}),M(r,function(s){M(s,function(l,u){var v=l.getLayout().value*o;i==="vertical"?(l.setLayout({x:u},!0),l.setLayout({dx:v},!0)):(l.setLayout({y:u},!0),l.setLayout({dy:v},!0))})}),M(e,function(s){var l=+s.getValue()*o;s.setLayout({dy:l},!0)})}function Ha(r,e,t,a,n){var i=n==="vertical"?"x":"y";M(r,function(o){o.sort(function(d,g){return d.getLayout()[i]-g.getLayout()[i]});for(var s,l,u,v=0,c=o.length,h=n==="vertical"?"dx":"dy",f=0;f<c;f++)l=o[f],u=v-l.getLayout()[i],u>0&&(s=l.getLayout()[i]+u,n==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0)),v=l.getLayout()[i]+l.getLayout()[h]+e;var p=n==="vertical"?a:t;if(u=v-e-p,u>0){s=l.getLayout()[i]-u,n==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0),v=s;for(var f=c-2;f>=0;--f)l=o[f],u=l.getLayout()[i]+l.getLayout()[h]+e-v,u>0&&(s=l.getLayout()[i]-u,n==="vertical"?l.setLayout({x:s},!0):l.setLayout({y:s},!0)),v=l.getLayout()[i]}})}function wg(r,e,t){M(r.slice().reverse(),function(a){M(a,function(n){if(n.outEdges.length){var i=le(n.outEdges,_g,t)/le(n.outEdges,ta);if(isNaN(i)){var o=n.outEdges.length;i=o?le(n.outEdges,Ag,t)/o:0}if(t==="vertical"){var s=n.getLayout().x+(i-ue(n,t))*e;n.setLayout({x:s},!0)}else{var l=n.getLayout().y+(i-ue(n,t))*e;n.setLayout({y:l},!0)}}})})}function _g(r,e){return ue(r.node2,e)*r.getValue()}function Ag(r,e){return ue(r.node2,e)}function Tg(r,e){return ue(r.node1,e)*r.getValue()}function Ig(r,e){return ue(r.node1,e)}function ue(r,e){return e==="vertical"?r.getLayout().x+r.getLayout().dx/2:r.getLayout().y+r.getLayout().dy/2}function ta(r){return r.getValue()}function le(r,e,t){for(var a=0,n=r.length,i=-1;++i<n;){var o=+e(r[i],t);isNaN(o)||(a+=o)}return a}function Dg(r,e,t){M(r,function(a){M(a,function(n){if(n.inEdges.length){var i=le(n.inEdges,Tg,t)/le(n.inEdges,ta);if(isNaN(i)){var o=n.inEdges.length;i=o?le(n.inEdges,Ig,t)/o:0}if(t==="vertical"){var s=n.getLayout().x+(i-ue(n,t))*e;n.setLayout({x:s},!0)}else{var l=n.getLayout().y+(i-ue(n,t))*e;n.setLayout({y:l},!0)}}})})}function Cg(r,e){var t=e==="vertical"?"x":"y";M(r,function(a){a.outEdges.sort(function(n,i){return n.node2.getLayout()[t]-i.node2.getLayout()[t]}),a.inEdges.sort(function(n,i){return n.node1.getLayout()[t]-i.node1.getLayout()[t]})}),M(r,function(a){var n=0,i=0;M(a.outEdges,function(o){o.setLayout({sy:n},!0),n+=o.getLayout().dy}),M(a.inEdges,function(o){o.setLayout({ty:i},!0),i+=o.getLayout().dy})})}function Lg(r){r.eachSeriesByType("sankey",function(e){var t=e.getGraph(),a=t.nodes,n=t.edges;if(a.length){var i=1/0,o=-1/0;M(a,function(s){var l=s.getLayout().value;l<i&&(i=l),l>o&&(o=l)}),M(a,function(s){var l=new Ll({type:"color",mappingMethod:"linear",dataExtent:[i,o],visual:e.get("color")}),u=l.mapValueToVisual(s.getLayout().value),v=s.getModel().get(["itemStyle","color"]);v!=null?(s.setVisual("color",v),s.setVisual("style",{fill:v})):(s.setVisual("color",u),s.setVisual("style",{fill:u}))})}n.length&&M(n,function(s){var l=s.getModel().get("lineStyle");s.setVisual("style",l)})})}function Pg(r){r.registerChartView(lg),r.registerSeriesModel(vg),r.registerLayout(cg),r.registerVisual(Lg),r.registerAction({type:"dragNode",event:"dragnode",update:"update"},function(e,t){t.eachComponent({mainType:"series",subType:"sankey",query:e},function(a){a.setNodePosition(e.dataIndex,[e.localX,e.localY])})})}function Go(r,e){var t=e.rippleEffectColor||e.color;r.eachChild(function(a){a.attr({z:e.z,zlevel:e.zlevel,style:{stroke:e.brushType==="stroke"?t:null,fill:e.brushType==="fill"?t:null}})})}var Mg=function(r){G(e,r);function e(t,a){var n=r.call(this)||this,i=new Cl(t,a),o=new X;return n.add(i),n.add(o),n.updateData(t,a),n}return e.prototype.stopEffectAnimation=function(){this.childAt(1).removeAll()},e.prototype.startEffectAnimation=function(t){for(var a=t.symbolType,n=t.color,i=t.rippleNumber,o=this.childAt(1),s=0;s<i;s++){var l=De(a,-1,-1,2,2,n);l.attr({style:{strokeNoScale:!0},z2:99,silent:!0,scaleX:.5,scaleY:.5});var u=-s/i*t.period+t.effectOffset;l.animate("",!0).when(t.period,{scaleX:t.rippleScale/2,scaleY:t.rippleScale/2}).delay(u).start(),l.animateStyle(!0).when(t.period,{opacity:0}).delay(u).start(),o.add(l)}Go(o,t)},e.prototype.updateEffectAnimation=function(t){for(var a=this._effectCfg,n=this.childAt(1),i=["symbolType","period","rippleScale","rippleNumber"],o=0;o<i.length;o++){var s=i[o];if(a[s]!==t[s]){this.stopEffectAnimation(),this.startEffectAnimation(t);return}}Go(n,t)},e.prototype.highlight=function(){hl(this)},e.prototype.downplay=function(){fl(this)},e.prototype.getSymbolType=function(){var t=this.childAt(0);return t&&t.getSymbolType()},e.prototype.updateData=function(t,a){var n=this,i=t.hostModel;this.childAt(0).updateData(t,a);var o=this.childAt(1),s=t.getItemModel(a),l=t.getItemVisual(a,"symbol"),u=qs(t.getItemVisual(a,"symbolSize")),v=t.getItemVisual(a,"style"),c=v&&v.fill,h=s.getModel("emphasis");o.setScale(u),o.traverse(function(g){g.setStyle("fill",c)});var f=pl(t.getItemVisual(a,"symbolOffset"),u);f&&(o.x=f[0],o.y=f[1]);var p=t.getItemVisual(a,"symbolRotate");o.rotation=(p||0)*Math.PI/180||0;var d={};d.showEffectOn=i.get("showEffectOn"),d.rippleScale=s.get(["rippleEffect","scale"]),d.brushType=s.get(["rippleEffect","brushType"]),d.period=s.get(["rippleEffect","period"])*1e3,d.effectOffset=a/t.count(),d.z=i.getShallow("z")||0,d.zlevel=i.getShallow("zlevel")||0,d.symbolType=l,d.color=c,d.rippleEffectColor=s.get(["rippleEffect","color"]),d.rippleNumber=s.get(["rippleEffect","number"]),d.showEffectOn==="render"?(this._effectCfg?this.updateEffectAnimation(d):this.startEffectAnimation(d),this._effectCfg=d):(this._effectCfg=null,this.stopEffectAnimation(),this.onHoverStateChange=function(g){g==="emphasis"?d.showEffectOn!=="render"&&n.startEffectAnimation(d):g==="normal"&&d.showEffectOn!=="render"&&n.stopEffectAnimation()}),this._effectCfg=d,dt(this,h.get("focus"),h.get("blurScope"),h.get("disabled"))},e.prototype.fadeOut=function(t){t&&t()},e}(X),Eg=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(){this._symbolDraw=new Fn(Mg)},e.prototype.render=function(t,a,n){var i=t.getData(),o=this._symbolDraw;o.updateData(i,{clipShape:this._getClipShape(t)}),this.group.add(o.group)},e.prototype._getClipShape=function(t){var a=t.coordinateSystem,n=a&&a.getArea&&a.getArea();return t.get("clip",!0)?n:null},e.prototype.updateTransform=function(t,a,n){var i=t.getData();this.group.dirty();var o=El("").reset(t,a,n);o.progress&&o.progress({start:0,end:i.count(),count:i.count()},i),this._symbolDraw.updateLayout()},e.prototype._updateGroupTransform=function(t){var a=t.coordinateSystem;a&&a.getRoamTransform&&(this.group.transform=jv(a.getRoamTransform()),this.group.decomposeTransform())},e.prototype.remove=function(t,a){this._symbolDraw&&this._symbolDraw.remove(!0)},e.type="effectScatter",e}(gt),Rg=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t}return e.prototype.getInitialData=function(t,a){return Cr(null,this,{useEncodeDefaulter:!0})},e.prototype.brushSelector=function(t,a,n){return n.point(a.getItemLayout(t))},e.type="series.effectScatter",e.dependencies=["grid","polar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,effectType:"ripple",progressive:0,showEffectOn:"render",clip:!0,rippleEffect:{period:4,scale:2.5,brushType:"fill",number:3},universalTransition:{divideShape:"clone"},symbolSize:10},e}(St);function Vg(r){r.registerChartView(Eg),r.registerSeriesModel(Rg),r.registerLayout(El("effectScatter"))}var bu=function(r){G(e,r);function e(t,a,n){var i=r.call(this)||this;return i.add(i.createLine(t,a,n)),i._updateEffectSymbol(t,a),i}return e.prototype.createLine=function(t,a,n){return new Rl(t,a,n)},e.prototype._updateEffectSymbol=function(t,a){var n=t.getItemModel(a),i=n.getModel("effect"),o=i.get("symbolSize"),s=i.get("symbol");q(o)||(o=[o,o]);var l=t.getItemVisual(a,"style"),u=i.get("color")||l&&l.stroke,v=this.childAt(1);this._symbolType!==s&&(this.remove(v),v=De(s,-.5,-.5,1,1,u),v.z2=100,v.culling=!0,this.add(v)),v&&(v.setStyle("shadowColor",u),v.setStyle(i.getItemStyle(["color"])),v.scaleX=o[0],v.scaleY=o[1],v.setColor(u),this._symbolType=s,this._symbolScale=o,this._updateEffectAnimation(t,i,a))},e.prototype._updateEffectAnimation=function(t,a,n){var i=this.childAt(1);if(i){var o=t.getItemLayout(n),s=a.get("period")*1e3,l=a.get("loop"),u=a.get("roundTrip"),v=a.get("constantSpeed"),c=Le(a.get("delay"),function(f){return f/t.count()*s/3});if(i.ignore=!0,this._updateAnimationPoints(i,o),v>0&&(s=this._getLineLength(i)/v*1e3),s!==this._period||l!==this._loop||u!==this._roundTrip){i.stopAnimation();var h=void 0;st(c)?h=c(n):h=c,i.__t>0&&(h=-s*i.__t),this._animateSymbol(i,s,h,l,u)}this._period=s,this._loop=l,this._roundTrip=u}},e.prototype._animateSymbol=function(t,a,n,i,o){if(a>0){t.__t=0;var s=this,l=t.animate("",i).when(o?a*2:a,{__t:o?2:1}).delay(n).during(function(){s._updateSymbolPosition(t)});i||l.done(function(){s.remove(t)}),l.start()}},e.prototype._getLineLength=function(t){return sr(t.__p1,t.__cp1)+sr(t.__cp1,t.__p2)},e.prototype._updateAnimationPoints=function(t,a){t.__p1=a[0],t.__p2=a[1],t.__cp1=a[2]||[(a[0][0]+a[1][0])/2,(a[0][1]+a[1][1])/2]},e.prototype.updateData=function(t,a,n){this.childAt(0).updateData(t,a,n),this._updateEffectSymbol(t,a)},e.prototype._updateSymbolPosition=function(t){var a=t.__p1,n=t.__p2,i=t.__cp1,o=t.__t<1?t.__t:2-t.__t,s=[t.x,t.y],l=s.slice(),u=sl,v=Kv;s[0]=u(a[0],i[0],n[0],o),s[1]=u(a[1],i[1],n[1],o);var c=t.__t<1?v(a[0],i[0],n[0],o):v(n[0],i[0],a[0],1-o),h=t.__t<1?v(a[1],i[1],n[1],o):v(n[1],i[1],a[1],1-o);t.rotation=-Math.atan2(h,c)-Math.PI/2,(this._symbolType==="line"||this._symbolType==="rect"||this._symbolType==="roundRect")&&(t.__lastT!==void 0&&t.__lastT<t.__t?(t.scaleY=sr(l,s)*1.05,o===1&&(s[0]=l[0]+(s[0]-l[0])/2,s[1]=l[1]+(s[1]-l[1])/2)):t.__lastT===1?t.scaleY=2*sr(a,s):t.scaleY=this._symbolScale[1]),t.__lastT=t.__t,t.ignore=!1,t.x=s[0],t.y=s[1]},e.prototype.updateLayout=function(t,a){this.childAt(0).updateLayout(t,a);var n=t.getItemModel(a).getModel("effect");this._updateEffectAnimation(t,n,a)},e}(X),wu=function(r){G(e,r);function e(t,a,n){var i=r.call(this)||this;return i._createPolyline(t,a,n),i}return e.prototype._createPolyline=function(t,a,n){var i=t.getItemLayout(a),o=new Re({shape:{points:i}});this.add(o),this._updateCommonStl(t,a,n)},e.prototype.updateData=function(t,a,n){var i=t.hostModel,o=this.childAt(0),s={shape:{points:t.getItemLayout(a)}};ht(o,s,i,a),this._updateCommonStl(t,a,n)},e.prototype._updateCommonStl=function(t,a,n){var i=this.childAt(0),o=t.getItemModel(a),s=n&&n.emphasisLineStyle,l=n&&n.focus,u=n&&n.blurScope,v=n&&n.emphasisDisabled;if(!n||t.hasItemOption){var c=o.getModel("emphasis");s=c.getModel("lineStyle").getLineStyle(),v=c.get("disabled"),l=c.get("focus"),u=c.get("blurScope")}i.useStyle(t.getItemVisual(a,"style")),i.style.fill=null,i.style.strokeNoScale=!0;var h=i.ensureState("emphasis");h.style=s,dt(this,l,u,v)},e.prototype.updateLayout=function(t,a){var n=this.childAt(0);n.setShape("points",t.getItemLayout(a))},e}(X),Ng=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t._lastFrame=0,t._lastFramePercent=0,t}return e.prototype.createLine=function(t,a,n){return new wu(t,a,n)},e.prototype._updateAnimationPoints=function(t,a){this._points=a;for(var n=[0],i=0,o=1;o<a.length;o++){var s=a[o-1],l=a[o];i+=sr(s,l),n.push(i)}if(i===0){this._length=0;return}for(var o=0;o<n.length;o++)n[o]/=i;this._offsets=n,this._length=i},e.prototype._getLineLength=function(){return this._length},e.prototype._updateSymbolPosition=function(t){var a=t.__t<1?t.__t:2-t.__t,n=this._points,i=this._offsets,o=n.length;if(i){var s=this._lastFrame,l;if(a<this._lastFramePercent){var u=Math.min(s+1,o-1);for(l=u;l>=0&&!(i[l]<=a);l--);l=Math.min(l,o-2)}else{for(l=s;l<o&&!(i[l]>a);l++);l=Math.min(l-1,o-2)}var v=(a-i[l])/(i[l+1]-i[l]),c=n[l],h=n[l+1];t.x=c[0]*(1-v)+v*h[0],t.y=c[1]*(1-v)+v*h[1];var f=t.__t<1?h[0]-c[0]:c[0]-h[0],p=t.__t<1?h[1]-c[1]:c[1]-h[1];t.rotation=-Math.atan2(p,f)-Math.PI/2,this._lastFrame=l,this._lastFramePercent=a,t.ignore=!1}},e}(bu),Gg=function(){function r(){this.polyline=!1,this.curveness=0,this.segs=[]}return r}(),kg=function(r){G(e,r);function e(t){var a=r.call(this,t)||this;return a._off=0,a.hoverDataIdx=-1,a}return e.prototype.reset=function(){this.notClear=!1,this._off=0},e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Gg},e.prototype.buildPath=function(t,a){var n=a.segs,i=a.curveness,o;if(a.polyline)for(o=this._off;o<n.length;){var s=n[o++];if(s>0){t.moveTo(n[o++],n[o++]);for(var l=1;l<s;l++)t.lineTo(n[o++],n[o++])}}else for(o=this._off;o<n.length;){var u=n[o++],v=n[o++],c=n[o++],h=n[o++];if(t.moveTo(u,v),i>0){var f=(u+c)/2-(v-h)*i,p=(v+h)/2-(c-u)*i;t.quadraticCurveTo(f,p,c,h)}else t.lineTo(c,h)}this.incremental&&(this._off=o,this.notClear=!0)},e.prototype.findDataIndex=function(t,a){var n=this.shape,i=n.segs,o=n.curveness,s=this.style.lineWidth;if(n.polyline)for(var l=0,u=0;u<i.length;){var v=i[u++];if(v>0)for(var c=i[u++],h=i[u++],f=1;f<v;f++){var p=i[u++],d=i[u++];if(Di(c,h,p,d,s,t,a))return l}l++}else for(var l=0,u=0;u<i.length;){var c=i[u++],h=i[u++],p=i[u++],d=i[u++];if(o>0){var g=(c+p)/2-(h-d)*o,S=(h+d)/2-(p-c)*o;if(Jv(c,h,g,S,p,d,s,t,a))return l}else if(Di(c,h,p,d,s,t,a))return l;l++}return-1},e.prototype.contain=function(t,a){var n=this.transformCoordToLocal(t,a),i=this.getBoundingRect();if(t=n[0],a=n[1],i.contain(t,a)){var o=this.hoverDataIdx=this.findDataIndex(t,a);return o>=0}return this.hoverDataIdx=-1,!1},e.prototype.getBoundingRect=function(){var t=this._rect;if(!t){for(var a=this.shape,n=a.segs,i=1/0,o=1/0,s=-1/0,l=-1/0,u=0;u<n.length;){var v=n[u++],c=n[u++];i=Math.min(v,i),s=Math.max(v,s),o=Math.min(c,o),l=Math.max(c,l)}t=this._rect=new ct(i,o,s,l)}return t},e}(Gt),zg=function(){function r(){this.group=new X}return r.prototype.updateData=function(e){this._clear();var t=this._create();t.setShape({segs:e.getLayout("linesPoints")}),this._setCommon(t,e)},r.prototype.incrementalPrepareUpdate=function(e){this.group.removeAll(),this._clear()},r.prototype.incrementalUpdate=function(e,t){var a=this._newAdded[0],n=t.getLayout("linesPoints"),i=a&&a.shape.segs;if(i&&i.length<2e4){var o=i.length,s=new Float32Array(o+n.length);s.set(i),s.set(n,o),a.setShape({segs:s})}else{this._newAdded=[];var l=this._create();l.incremental=!0,l.setShape({segs:n}),this._setCommon(l,t),l.__startIndex=e.start}},r.prototype.remove=function(){this._clear()},r.prototype.eachRendered=function(e){this._newAdded[0]&&e(this._newAdded[0])},r.prototype._create=function(){var e=new kg({cursor:"default",ignoreCoarsePointer:!0});return this._newAdded.push(e),this.group.add(e),e},r.prototype._setCommon=function(e,t,a){var n=t.hostModel;e.setShape({polyline:n.get("polyline"),curveness:n.get(["lineStyle","curveness"])}),e.useStyle(n.getModel("lineStyle").getLineStyle()),e.style.strokeNoScale=!0;var i=t.getVisual("style");i&&i.stroke&&e.setStyle("stroke",i.stroke),e.setStyle("fill",null);var o=ft(e);o.seriesIndex=n.seriesIndex,e.on("mousemove",function(s){o.dataIndex=null;var l=e.hoverDataIdx;l>0&&(o.dataIndex=l+e.__startIndex)})},r.prototype._clear=function(){this._newAdded=[],this.group.removeAll()},r}(),_u={seriesType:"lines",plan:Qv(),reset:function(r){var e=r.coordinateSystem;if(e){var t=r.get("polyline"),a=r.pipelineContext.large;return{progress:function(n,i){var o=[];if(a){var s=void 0,l=n.end-n.start;if(t){for(var u=0,v=n.start;v<n.end;v++)u+=r.getLineCoordsCount(v);s=new Float32Array(l+u*2)}else s=new Float32Array(l*4);for(var c=0,h=[],v=n.start;v<n.end;v++){var f=r.getLineCoords(v,o);t&&(s[c++]=f);for(var p=0;p<f;p++)h=e.dataToPoint(o[p],!1,h),s[c++]=h[0],s[c++]=h[1]}i.setLayout("linesPoints",s)}else for(var v=n.start;v<n.end;v++){var d=i.getItemModel(v),f=r.getLineCoords(v,o),g=[];if(t)for(var S=0;S<f;S++)g.push(e.dataToPoint(o[S]));else{g[0]=e.dataToPoint(o[0]),g[1]=e.dataToPoint(o[1]);var m=d.get(["lineStyle","curveness"]);+m&&(g[2]=[(g[0][0]+g[1][0])/2-(g[0][1]-g[1][1])*m,(g[0][1]+g[1][1])/2-(g[1][0]-g[0][0])*m])}i.setItemLayout(v,g)}}}}}},Og=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n){var i=t.getData(),o=this._updateLineDraw(i,t),s=t.get("zlevel"),l=t.get(["effect","trailLength"]),u=n.getZr(),v=u.painter.getType()==="svg";v||u.painter.getLayer(s).clear(!0),this._lastZlevel!=null&&!v&&u.configLayer(this._lastZlevel,{motionBlur:!1}),this._showEffect(t)&&l>0&&(v||u.configLayer(s,{motionBlur:!0,lastFrameAlpha:Math.max(Math.min(l/10+.9,1),0)})),o.updateData(i);var c=t.get("clip",!0)&&Hn(t.coordinateSystem,!1,t);c?this.group.setClipPath(c):this.group.removeClipPath(),this._lastZlevel=s,this._finished=!0},e.prototype.incrementalPrepareRender=function(t,a,n){var i=t.getData(),o=this._updateLineDraw(i,t);o.incrementalPrepareUpdate(i),this._clearLayer(n),this._finished=!1},e.prototype.incrementalRender=function(t,a,n){this._lineDraw.incrementalUpdate(t,a.getData()),this._finished=t.end===a.getData().count()},e.prototype.eachRendered=function(t){this._lineDraw&&this._lineDraw.eachRendered(t)},e.prototype.updateTransform=function(t,a,n){var i=t.getData(),o=t.pipelineContext;if(!this._finished||o.large||o.progressiveRender)return{update:!0};var s=_u.reset(t,a,n);s.progress&&s.progress({start:0,end:i.count(),count:i.count()},i),this._lineDraw.updateLayout(),this._clearLayer(n)},e.prototype._updateLineDraw=function(t,a){var n=this._lineDraw,i=this._showEffect(a),o=!!a.get("polyline"),s=a.pipelineContext,l=s.large;return(!n||i!==this._hasEffet||o!==this._isPolyline||l!==this._isLargeDraw)&&(n&&n.remove(),n=this._lineDraw=l?new zg:new Pl(o?i?Ng:wu:i?bu:Rl),this._hasEffet=i,this._isPolyline=o,this._isLargeDraw=l),this.group.add(n.group),n},e.prototype._showEffect=function(t){return!!t.get(["effect","show"])},e.prototype._clearLayer=function(t){var a=t.getZr(),n=a.painter.getType()==="svg";!n&&this._lastZlevel!=null&&a.painter.getLayer(this._lastZlevel).clear(!0)},e.prototype.remove=function(t,a){this._lineDraw&&this._lineDraw.remove(),this._lineDraw=null,this._clearLayer(a)},e.prototype.dispose=function(t,a){this.remove(t,a)},e.type="lines",e}(gt),Bg=typeof Uint32Array>"u"?Array:Uint32Array,Fg=typeof Float64Array>"u"?Array:Float64Array;function ko(r){var e=r.data;e&&e[0]&&e[0][0]&&e[0][0].coord&&(r.data=F(e,function(t){var a=[t[0].coord,t[1].coord],n={coords:a};return t[0].name&&(n.fromName=t[0].name),t[1].name&&(n.toName=t[1].name),al([n,t[0],t[1]])}))}var Hg=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.visualStyleAccessPath="lineStyle",t.visualDrawType="stroke",t}return e.prototype.init=function(t){t.data=t.data||[],ko(t);var a=this._processFlatCoordsArray(t.data);this._flatCoords=a.flatCoords,this._flatCoordsOffset=a.flatCoordsOffset,a.flatCoords&&(t.data=new Float32Array(a.count)),r.prototype.init.apply(this,arguments)},e.prototype.mergeOption=function(t){if(ko(t),t.data){var a=this._processFlatCoordsArray(t.data);this._flatCoords=a.flatCoords,this._flatCoordsOffset=a.flatCoordsOffset,a.flatCoords&&(t.data=new Float32Array(a.count))}r.prototype.mergeOption.apply(this,arguments)},e.prototype.appendData=function(t){var a=this._processFlatCoordsArray(t.data);a.flatCoords&&(this._flatCoords?(this._flatCoords=$r(this._flatCoords,a.flatCoords),this._flatCoordsOffset=$r(this._flatCoordsOffset,a.flatCoordsOffset)):(this._flatCoords=a.flatCoords,this._flatCoordsOffset=a.flatCoordsOffset),t.data=new Float32Array(a.count)),this.getRawData().appendData(t.data)},e.prototype._getCoordsFromItemModel=function(t){var a=this.getData().getItemModel(t),n=a.option instanceof Array?a.option:a.getShallow("coords");return n},e.prototype.getLineCoordsCount=function(t){return this._flatCoordsOffset?this._flatCoordsOffset[t*2+1]:this._getCoordsFromItemModel(t).length},e.prototype.getLineCoords=function(t,a){if(this._flatCoordsOffset){for(var n=this._flatCoordsOffset[t*2],i=this._flatCoordsOffset[t*2+1],o=0;o<i;o++)a[o]=a[o]||[],a[o][0]=this._flatCoords[n+o*2],a[o][1]=this._flatCoords[n+o*2+1];return i}else{for(var s=this._getCoordsFromItemModel(t),o=0;o<s.length;o++)a[o]=a[o]||[],a[o][0]=s[o][0],a[o][1]=s[o][1];return s.length}},e.prototype._processFlatCoordsArray=function(t){var a=0;if(this._flatCoords&&(a=this._flatCoords.length),jt(t[0])){for(var n=t.length,i=new Bg(n),o=new Fg(n),s=0,l=0,u=0,v=0;v<n;){u++;var c=t[v++];i[l++]=s+a,i[l++]=c;for(var h=0;h<c;h++){var f=t[v++],p=t[v++];o[s++]=f,o[s++]=p}}return{flatCoordsOffset:new Uint32Array(i.buffer,0,l),flatCoords:o,count:u}}return{flatCoordsOffset:null,flatCoords:null,count:t.length}},e.prototype.getInitialData=function(t,a){var n=new Pe(["value"],this);return n.hasItemOption=!1,n.initData(t.data,[],function(i,o,s,l){if(i instanceof Array)return NaN;n.hasItemOption=!0;var u=i.value;if(u!=null)return u instanceof Array?u[l]:u}),n},e.prototype.formatTooltip=function(t,a,n){var i=this.getData(),o=i.getItemModel(t),s=o.get("name");if(s)return s;var l=o.get("fromName"),u=o.get("toName"),v=[];return l!=null&&v.push(l),u!=null&&v.push(u),Nt("nameValue",{name:v.join(" > ")})},e.prototype.preventIncremental=function(){return!!this.get(["effect","show"])},e.prototype.getProgressive=function(){var t=this.option.progressive;return t??(this.option.large?1e4:this.get("progressive"))},e.prototype.getProgressiveThreshold=function(){var t=this.option.progressiveThreshold;return t??(this.option.large?2e4:this.get("progressiveThreshold"))},e.prototype.getZLevelKey=function(){var t=this.getModel("effect"),a=t.get("trailLength");return this.getData().count()>this.getProgressiveThreshold()?this.id:t.get("show")&&a>0?a+"":""},e.type="series.lines",e.dependencies=["grid","polar","geo","calendar"],e.defaultOption={coordinateSystem:"geo",z:2,legendHoverLink:!0,xAxisIndex:0,yAxisIndex:0,symbol:["none","none"],symbolSize:[10,10],geoIndex:0,effect:{show:!1,period:4,constantSpeed:0,symbol:"circle",symbolSize:3,loop:!0,trailLength:.2},large:!1,largeThreshold:2e3,polyline:!1,clip:!0,label:{show:!1,position:"end"},lineStyle:{opacity:.5}},e}(St);function Or(r){return r instanceof Array||(r=[r,r]),r}var Wg={seriesType:"lines",reset:function(r){var e=Or(r.get("symbol")),t=Or(r.get("symbolSize")),a=r.getData();a.setVisual("fromSymbol",e&&e[0]),a.setVisual("toSymbol",e&&e[1]),a.setVisual("fromSymbolSize",t&&t[0]),a.setVisual("toSymbolSize",t&&t[1]);function n(i,o){var s=i.getItemModel(o),l=Or(s.getShallow("symbol",!0)),u=Or(s.getShallow("symbolSize",!0));l[0]&&i.setItemVisual(o,"fromSymbol",l[0]),l[1]&&i.setItemVisual(o,"toSymbol",l[1]),u[0]&&i.setItemVisual(o,"fromSymbolSize",u[0]),u[1]&&i.setItemVisual(o,"toSymbolSize",u[1])}return{dataEach:a.hasItemOption?n:null}}};function Ug(r){r.registerChartView(Og),r.registerSeriesModel(Hg),r.registerLayout(_u),r.registerVisual(Wg)}var $g=256,Yg=function(){function r(){this.blurSize=30,this.pointSize=20,this.maxOpacity=1,this.minOpacity=0,this._gradientPixels={inRange:null,outOfRange:null};var e=Ci.createCanvas();this.canvas=e}return r.prototype.update=function(e,t,a,n,i,o){var s=this._getBrush(),l=this._getGradient(i,"inRange"),u=this._getGradient(i,"outOfRange"),v=this.pointSize+this.blurSize,c=this.canvas,h=c.getContext("2d"),f=e.length;c.width=t,c.height=a;for(var p=0;p<f;++p){var d=e[p],g=d[0],S=d[1],m=d[2],y=n(m);h.globalAlpha=y,h.drawImage(s,g-v,S-v)}if(!c.width||!c.height)return c;for(var w=h.getImageData(0,0,c.width,c.height),x=w.data,b=0,_=x.length,T=this.minOpacity,I=this.maxOpacity,A=I-T;b<_;){var y=x[b+3]/256,D=Math.floor(y*($g-1))*4;if(y>0){var E=o(y)?l:u;y>0&&(y=y*A+T),x[b++]=E[D],x[b++]=E[D+1],x[b++]=E[D+2],x[b++]=E[D+3]*y*256}else b+=4}return h.putImageData(w,0,0),c},r.prototype._getBrush=function(){var e=this._brushCanvas||(this._brushCanvas=Ci.createCanvas()),t=this.pointSize+this.blurSize,a=t*2;e.width=a,e.height=a;var n=e.getContext("2d");return n.clearRect(0,0,a,a),n.shadowOffsetX=a,n.shadowBlur=this.blurSize,n.shadowColor="#000",n.beginPath(),n.arc(-t,t,this.pointSize,0,Math.PI*2,!0),n.closePath(),n.fill(),e},r.prototype._getGradient=function(e,t){for(var a=this._gradientPixels,n=a[t]||(a[t]=new Uint8ClampedArray(256*4)),i=[0,0,0,0],o=0,s=0;s<256;s++)e[t](s/255,!0,i),n[o++]=i[0],n[o++]=i[1],n[o++]=i[2],n[o++]=i[3];return n},r}();function Zg(r,e,t){var a=r[1]-r[0];e=F(e,function(o){return{interval:[(o.interval[0]-r[0])/a,(o.interval[1]-r[0])/a]}});var n=e.length,i=0;return function(o){var s;for(s=i;s<n;s++){var l=e[s].interval;if(l[0]<=o&&o<=l[1]){i=s;break}}if(s===n)for(s=i-1;s>=0;s--){var l=e[s].interval;if(l[0]<=o&&o<=l[1]){i=s;break}}return s>=0&&s<n&&t[s]}}function Xg(r,e){var t=r[1]-r[0];return e=[(e[0]-r[0])/t,(e[1]-r[0])/t],function(a){return a>=e[0]&&a<=e[1]}}function zo(r){var e=r.dimensions;return e[0]==="lng"&&e[1]==="lat"}var qg=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n){var i;a.eachComponent("visualMap",function(s){s.eachTargetSeries(function(l){l===t&&(i=s)})}),this._progressiveEls=null,this.group.removeAll();var o=t.coordinateSystem;o.type==="cartesian2d"||o.type==="calendar"?this._renderOnCartesianAndCalendar(t,n,0,t.getData().count()):zo(o)&&this._renderOnGeo(o,t,i,n)},e.prototype.incrementalPrepareRender=function(t,a,n){this.group.removeAll()},e.prototype.incrementalRender=function(t,a,n,i){var o=a.coordinateSystem;o&&(zo(o)?this.render(a,n,i):(this._progressiveEls=[],this._renderOnCartesianAndCalendar(a,i,t.start,t.end,!0)))},e.prototype.eachRendered=function(t){dl(this._progressiveEls||this.group,t)},e.prototype._renderOnCartesianAndCalendar=function(t,a,n,i,o){var s=t.coordinateSystem,l=th(s,"cartesian2d"),u,v,c,h;if(l){var f=s.getAxis("x"),p=s.getAxis("y");u=f.getBandWidth()+.5,v=p.getBandWidth()+.5,c=f.scale.getExtent(),h=p.scale.getExtent()}for(var d=this.group,g=t.getData(),S=t.getModel(["emphasis","itemStyle"]).getItemStyle(),m=t.getModel(["blur","itemStyle"]).getItemStyle(),y=t.getModel(["select","itemStyle"]).getItemStyle(),w=t.get(["itemStyle","borderRadius"]),x=Vt(t),b=t.getModel("emphasis"),_=b.get("focus"),T=b.get("blurScope"),I=b.get("disabled"),A=l?[g.mapDimension("x"),g.mapDimension("y"),g.mapDimension("value")]:[g.mapDimension("time"),g.mapDimension("value")],D=n;D<i;D++){var E=void 0,P=g.getItemVisual(D,"style");if(l){var C=g.get(A[0],D),L=g.get(A[1],D);if(isNaN(g.get(A[2],D))||isNaN(C)||isNaN(L)||C<c[0]||C>c[1]||L<h[0]||L>h[1])continue;var R=s.dataToPoint([C,L]);E=new At({shape:{x:R[0]-u/2,y:R[1]-v/2,width:u,height:v},style:P})}else{if(isNaN(g.get(A[1],D)))continue;E=new At({z2:1,shape:s.dataToRect([g.get(A[0],D)]).contentShape,style:P})}if(g.hasItemOption){var V=g.getItemModel(D),N=V.getModel("emphasis");S=N.getModel("itemStyle").getItemStyle(),m=V.getModel(["blur","itemStyle"]).getItemStyle(),y=V.getModel(["select","itemStyle"]).getItemStyle(),w=V.get(["itemStyle","borderRadius"]),_=N.get("focus"),T=N.get("blurScope"),I=N.get("disabled"),x=Vt(V)}E.shape.r=w;var k=t.getRawValue(D),O="-";k&&k[2]!=null&&(O=k[2]+""),$t(E,x,{labelFetcher:t,labelDataIndex:D,defaultOpacity:P.opacity,defaultText:O}),E.ensureState("emphasis").style=S,E.ensureState("blur").style=m,E.ensureState("select").style=y,dt(E,_,T,I),E.incremental=o,o&&(E.states.emphasis.hoverLayer=!0),d.add(E),g.setItemGraphicEl(D,E),this._progressiveEls&&this._progressiveEls.push(E)}},e.prototype._renderOnGeo=function(t,a,n,i){var o=n.targetVisuals.inRange,s=n.targetVisuals.outOfRange,l=a.getData(),u=this._hmLayer||this._hmLayer||new Yg;u.blurSize=a.get("blurSize"),u.pointSize=a.get("pointSize"),u.minOpacity=a.get("minOpacity"),u.maxOpacity=a.get("maxOpacity");var v=t.getViewRect().clone(),c=t.getRoamTransform();v.applyTransform(c);var h=Math.max(v.x,0),f=Math.max(v.y,0),p=Math.min(v.width+v.x,i.getWidth()),d=Math.min(v.height+v.y,i.getHeight()),g=p-h,S=d-f,m=[l.mapDimension("lng"),l.mapDimension("lat"),l.mapDimension("value")],y=l.mapArray(m,function(_,T,I){var A=t.dataToPoint([_,T]);return A[0]-=h,A[1]-=f,A.push(I),A}),w=n.getExtent(),x=n.type==="visualMap.continuous"?Xg(w,n.option.range):Zg(w,n.getPieceList(),n.option.selected);u.update(y,g,S,o.color.getNormalizer(),{inRange:o.color.getColorMapper(),outOfRange:s.color.getColorMapper()},x);var b=new ce({style:{width:g,height:S,x:h,y:f,image:u.canvas},silent:!0});this.group.add(b)},e.type="heatmap",e}(gt),jg=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getInitialData=function(t,a){return Cr(null,this,{generateCoord:"value"})},e.prototype.preventIncremental=function(){var t=ll.get(this.get("coordinateSystem"));if(t&&t.dimensions)return t.dimensions[0]==="lng"&&t.dimensions[1]==="lat"},e.type="series.heatmap",e.dependencies=["grid","geo","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,geoIndex:0,blurSize:30,pointSize:20,maxOpacity:1,minOpacity:0,select:{itemStyle:{borderColor:"#212121"}}},e}(St);function Kg(r){r.registerChartView(qg),r.registerSeriesModel(jg)}var Jg=["itemStyle","borderWidth"],Oo=[{xy:"x",wh:"width",index:0,posDesc:["left","right"]},{xy:"y",wh:"height",index:1,posDesc:["top","bottom"]}],Wa=new _r,Qg=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n){var i=this.group,o=t.getData(),s=this._data,l=t.coordinateSystem,u=l.getBaseAxis(),v=u.isHorizontal(),c=l.master.getRect(),h={ecSize:{width:n.getWidth(),height:n.getHeight()},seriesModel:t,coordSys:l,coordSysExtent:[[c.x,c.x+c.width],[c.y,c.y+c.height]],isHorizontal:v,valueDim:Oo[+v],categoryDim:Oo[1-+v]};o.diff(s).add(function(p){if(o.hasValue(p)){var d=Fo(o,p),g=Bo(o,p,d,h),S=Ho(o,h,g);o.setItemGraphicEl(p,S),i.add(S),Uo(S,h,g)}}).update(function(p,d){var g=s.getItemGraphicEl(d);if(!o.hasValue(p)){i.remove(g);return}var S=Fo(o,p),m=Bo(o,p,S,h),y=Lu(o,m);g&&y!==g.__pictorialShapeStr&&(i.remove(g),o.setItemGraphicEl(p,null),g=null),g?oy(g,h,m):g=Ho(o,h,m,!0),o.setItemGraphicEl(p,g),g.__pictorialSymbolMeta=m,i.add(g),Uo(g,h,m)}).remove(function(p){var d=s.getItemGraphicEl(p);d&&Wo(s,p,d.__pictorialSymbolMeta.animationModel,d)}).execute();var f=t.get("clip",!0)?Hn(t.coordinateSystem,!1,t):null;return f?i.setClipPath(f):i.removeClipPath(),this._data=o,this.group},e.prototype.remove=function(t,a){var n=this.group,i=this._data;t.get("animation")?i&&i.eachItemGraphicEl(function(o){Wo(i,ft(o).dataIndex,t,o)}):n.removeAll()},e.type="pictorialBar",e}(gt);function Bo(r,e,t,a){var n=r.getItemLayout(e),i=t.get("symbolRepeat"),o=t.get("symbolClip"),s=t.get("symbolPosition")||"start",l=t.get("symbolRotate"),u=(l||0)*Math.PI/180||0,v=t.get("symbolPatternSize")||2,c=t.isAnimationEnabled(),h={dataIndex:e,layout:n,itemModel:t,symbolType:r.getItemVisual(e,"symbol")||"circle",style:r.getItemVisual(e,"style"),symbolClip:o,symbolRepeat:i,symbolRepeatDirection:t.get("symbolRepeatDirection"),symbolPatternSize:v,rotation:u,animationModel:c?t:null,hoverScale:c&&t.get(["emphasis","scale"]),z2:t.getShallow("z",!0)||0};ty(t,i,n,a,h),ey(r,e,n,i,o,h.boundingLength,h.pxSign,v,a,h),ry(t,h.symbolScale,u,a,h);var f=h.symbolSize,p=pl(t.get("symbolOffset"),f);return ay(t,f,n,i,o,p,s,h.valueLineWidth,h.boundingLength,h.repeatCutLength,a,h),h}function ty(r,e,t,a,n){var i=a.valueDim,o=r.get("symbolBoundingData"),s=a.coordSys.getOtherAxis(a.coordSys.getBaseAxis()),l=s.toGlobalCoord(s.dataToCoord(0)),u=1-+(t[i.wh]<=0),v;if(q(o)){var c=[Ua(s,o[0])-l,Ua(s,o[1])-l];c[1]<c[0]&&c.reverse(),v=c[u]}else o!=null?v=Ua(s,o)-l:e?v=a.coordSysExtent[i.index][u]-l:v=t[i.wh];n.boundingLength=v,e&&(n.repeatCutLength=t[i.wh]);var h=i.xy==="x",f=s.inverse;n.pxSign=h&&!f||!h&&f?v>=0?1:-1:v>0?1:-1}function Ua(r,e){return r.toGlobalCoord(r.dataToCoord(r.scale.parse(e)))}function ey(r,e,t,a,n,i,o,s,l,u){var v=l.valueDim,c=l.categoryDim,h=Math.abs(t[c.wh]),f=r.getItemVisual(e,"symbolSize"),p;q(f)?p=f.slice():f==null?p=["100%","100%"]:p=[f,f],p[c.index]=z(p[c.index],h),p[v.index]=z(p[v.index],a?h:Math.abs(i)),u.symbolSize=p;var d=u.symbolScale=[p[0]/s,p[1]/s];d[v.index]*=(l.isHorizontal?-1:1)*o}function ry(r,e,t,a,n){var i=r.get(Jg)||0;i&&(Wa.attr({scaleX:e[0],scaleY:e[1],rotation:t}),Wa.updateTransform(),i/=Wa.getLineScale(),i*=e[a.valueDim.index]),n.valueLineWidth=i||0}function ay(r,e,t,a,n,i,o,s,l,u,v,c){var h=v.categoryDim,f=v.valueDim,p=c.pxSign,d=Math.max(e[f.index]+s,0),g=d;if(a){var S=Math.abs(l),m=Le(r.get("symbolMargin"),"15%")+"",y=!1;m.lastIndexOf("!")===m.length-1&&(y=!0,m=m.slice(0,m.length-1));var w=z(m,e[f.index]),x=Math.max(d+w*2,0),b=y?0:w*2,_=tc(a),T=_?a:$o((S+b)/x),I=S-T*d;w=I/2/(y?T:Math.max(T-1,1)),x=d+w*2,b=y?0:w*2,!_&&a!=="fixed"&&(T=u?$o((Math.abs(u)+b)/x):0),g=T*x-b,c.repeatTimes=T,c.symbolMargin=w}var A=p*(g/2),D=c.pathPosition=[];D[h.index]=t[h.wh]/2,D[f.index]=o==="start"?A:o==="end"?l-A:l/2,i&&(D[0]+=i[0],D[1]+=i[1]);var E=c.bundlePosition=[];E[h.index]=t[h.xy],E[f.index]=t[f.xy];var P=c.barRectShape=W({},t);P[f.wh]=p*Math.max(Math.abs(t[f.wh]),Math.abs(D[f.index]+A)),P[h.wh]=t[h.wh];var C=c.clipShape={};C[h.xy]=-t[h.xy],C[h.wh]=v.ecSize[h.wh],C[f.xy]=0,C[f.wh]=t[f.wh]}function Au(r){var e=r.symbolPatternSize,t=De(r.symbolType,-e/2,-e/2,e,e);return t.attr({culling:!0}),t.type!=="image"&&t.setStyle({strokeNoScale:!0}),t}function Tu(r,e,t,a){var n=r.__pictorialBundle,i=t.symbolSize,o=t.valueLineWidth,s=t.pathPosition,l=e.valueDim,u=t.repeatTimes||0,v=0,c=i[e.valueDim.index]+o+t.symbolMargin*2;for(ei(r,function(d){d.__pictorialAnimationIndex=v,d.__pictorialRepeatTimes=u,v<u?Ye(d,null,p(v),t,a):Ye(d,null,{scaleX:0,scaleY:0},t,a,function(){n.remove(d)}),v++});v<u;v++){var h=Au(t);h.__pictorialAnimationIndex=v,h.__pictorialRepeatTimes=u,n.add(h);var f=p(v);Ye(h,{x:f.x,y:f.y,scaleX:0,scaleY:0},{scaleX:f.scaleX,scaleY:f.scaleY,rotation:f.rotation},t,a)}function p(d){var g=s.slice(),S=t.pxSign,m=d;return(t.symbolRepeatDirection==="start"?S>0:S<0)&&(m=u-1-d),g[l.index]=c*(m-u/2+.5)+s[l.index],{x:g[0],y:g[1],scaleX:t.symbolScale[0],scaleY:t.symbolScale[1],rotation:t.rotation}}}function Iu(r,e,t,a){var n=r.__pictorialBundle,i=r.__pictorialMainPath;i?Ye(i,null,{x:t.pathPosition[0],y:t.pathPosition[1],scaleX:t.symbolScale[0],scaleY:t.symbolScale[1],rotation:t.rotation},t,a):(i=r.__pictorialMainPath=Au(t),n.add(i),Ye(i,{x:t.pathPosition[0],y:t.pathPosition[1],scaleX:0,scaleY:0,rotation:t.rotation},{scaleX:t.symbolScale[0],scaleY:t.symbolScale[1]},t,a))}function Du(r,e,t){var a=W({},e.barRectShape),n=r.__pictorialBarRect;n?Ye(n,null,{shape:a},e,t):(n=r.__pictorialBarRect=new At({z2:2,shape:a,silent:!0,style:{stroke:"transparent",fill:"transparent",lineWidth:0}}),n.disableMorphing=!0,r.add(n))}function Cu(r,e,t,a){if(t.symbolClip){var n=r.__pictorialClipPath,i=W({},t.clipShape),o=e.valueDim,s=t.animationModel,l=t.dataIndex;if(n)ht(n,{shape:i},s,l);else{i[o.wh]=0,n=new At({shape:i}),r.__pictorialBundle.setClipPath(n),r.__pictorialClipPath=n;var u={};u[o.wh]=t.clipShape[o.wh],wr[a?"updateProps":"initProps"](n,{shape:u},s,l)}}}function Fo(r,e){var t=r.getItemModel(e);return t.getAnimationDelayParams=ny,t.isAnimationEnabled=iy,t}function ny(r){return{index:r.__pictorialAnimationIndex,count:r.__pictorialRepeatTimes}}function iy(){return this.parentModel.isAnimationEnabled()&&!!this.getShallow("animation")}function Ho(r,e,t,a){var n=new X,i=new X;return n.add(i),n.__pictorialBundle=i,i.x=t.bundlePosition[0],i.y=t.bundlePosition[1],t.symbolRepeat?Tu(n,e,t):Iu(n,e,t),Du(n,t,a),Cu(n,e,t,a),n.__pictorialShapeStr=Lu(r,t),n.__pictorialSymbolMeta=t,n}function oy(r,e,t){var a=t.animationModel,n=t.dataIndex,i=r.__pictorialBundle;ht(i,{x:t.bundlePosition[0],y:t.bundlePosition[1]},a,n),t.symbolRepeat?Tu(r,e,t,!0):Iu(r,e,t,!0),Du(r,t,!0),Cu(r,e,t,!0)}function Wo(r,e,t,a){var n=a.__pictorialBarRect;n&&n.removeTextContent();var i=[];ei(a,function(o){i.push(o)}),a.__pictorialMainPath&&i.push(a.__pictorialMainPath),a.__pictorialClipPath&&(t=null),M(i,function(o){Yr(o,{scaleX:0,scaleY:0},t,e,function(){a.parent&&a.parent.remove(a)})}),r.setItemGraphicEl(e,null)}function Lu(r,e){return[r.getItemVisual(e.dataIndex,"symbol")||"none",!!e.symbolRepeat,!!e.symbolClip].join(":")}function ei(r,e,t){M(r.__pictorialBundle.children(),function(a){a!==r.__pictorialBarRect&&e.call(t,a)})}function Ye(r,e,t,a,n,i){e&&r.attr(e),a.symbolClip&&!n?t&&r.attr(t):t&&wr[n?"updateProps":"initProps"](r,t,a.animationModel,a.dataIndex,i)}function Uo(r,e,t){var a=t.dataIndex,n=t.itemModel,i=n.getModel("emphasis"),o=i.getModel("itemStyle").getItemStyle(),s=n.getModel(["blur","itemStyle"]).getItemStyle(),l=n.getModel(["select","itemStyle"]).getItemStyle(),u=n.getShallow("cursor"),v=i.get("focus"),c=i.get("blurScope"),h=i.get("scale");ei(r,function(d){if(d instanceof ce){var g=d.style;d.useStyle(W({image:g.image,x:g.x,y:g.y,width:g.width,height:g.height},t.style))}else d.useStyle(t.style);var S=d.ensureState("emphasis");S.style=o,h&&(S.scaleX=d.scaleX*1.1,S.scaleY=d.scaleY*1.1),d.ensureState("blur").style=s,d.ensureState("select").style=l,u&&(d.cursor=u),d.z2=t.z2});var f=e.valueDim.posDesc[+(t.boundingLength>0)],p=r.__pictorialBarRect;p.ignoreClip=!0,$t(p,Vt(n),{labelFetcher:e.seriesModel,labelDataIndex:a,defaultText:un(e.seriesModel.getData(),a),inheritColor:t.style.fill,defaultOpacity:t.style.opacity,defaultOutsidePosition:f}),dt(r,v,c,i.get("disabled"))}function $o(r){var e=Math.round(r);return Math.abs(r-e)<1e-4?e:Math.ceil(r)}var sy=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.hasSymbolVisual=!0,t.defaultSymbol="roundRect",t}return e.prototype.getInitialData=function(t){return t.stack=null,r.prototype.getInitialData.apply(this,arguments)},e.type="series.pictorialBar",e.dependencies=["grid"],e.defaultOption=ec(Gi.defaultOption,{symbol:"circle",symbolSize:null,symbolRotate:null,symbolPosition:null,symbolOffset:null,symbolMargin:null,symbolRepeat:!1,symbolRepeatDirection:"end",symbolClip:!1,symbolBoundingData:null,symbolPatternSize:400,barGap:"-100%",clip:!1,progressive:0,emphasis:{scale:!1},select:{itemStyle:{borderColor:"#212121"}}}),e}(Gi);function ly(r){r.registerChartView(Qg),r.registerSeriesModel(sy),r.registerLayout(r.PRIORITY.VISUAL.LAYOUT,bt(rc,"pictorialBar")),r.registerLayout(r.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,ac("pictorialBar"))}var uy=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t._layers=[],t}return e.prototype.render=function(t,a,n){var i=t.getData(),o=this,s=this.group,l=t.getLayerSeries(),u=i.getLayout("layoutInfo"),v=u.rect,c=u.boundaryGap;s.x=0,s.y=v.y+c[0];function h(g){return g.name}var f=new je(this._layersSeries||[],l,h,h),p=[];f.add(it(d,this,"add")).update(it(d,this,"update")).remove(it(d,this,"remove")).execute();function d(g,S,m){var y=o._layers;if(g==="remove"){s.remove(y[S]);return}for(var w=[],x=[],b,_=l[S].indices,T=0;T<_.length;T++){var I=i.getItemLayout(_[T]),A=I.x,D=I.y0,E=I.y;w.push(A,D),x.push(A,D+E),b=i.getItemVisual(_[T],"style")}var P,C=i.getItemLayout(_[0]),L=t.getModel("label"),R=L.get("margin"),V=t.getModel("emphasis");if(g==="add"){var N=p[S]=new X;P=new eh({shape:{points:w,stackedOnPoints:x,smooth:.4,stackedOnSmooth:.4,smoothConstraint:!1},z2:0}),N.add(P),s.add(N),t.isAnimationEnabled()&&P.setClipPath(vy(P.getBoundingRect(),t,function(){P.removeClipPath()}))}else{var N=y[m];P=N.childAt(0),s.add(N),p[S]=N,ht(P,{shape:{points:w,stackedOnPoints:x}},t),Ze(P)}$t(P,Vt(t),{labelDataIndex:_[T-1],defaultText:i.getName(_[T-1]),inheritColor:b.fill},{normal:{verticalAlign:"middle"}}),P.setTextConfig({position:null,local:!0});var k=P.getTextContent();k&&(k.x=C.x-R,k.y=C.y0+C.y/2),P.useStyle(b),i.setItemGraphicEl(S,P),Wt(P,t),dt(P,V.get("focus"),V.get("blurScope"),V.get("disabled"))}this._layersSeries=l,this._layers=p},e.type="themeRiver",e}(gt);function vy(r,e,t){var a=new At({shape:{x:r.x-10,y:r.y-10,width:0,height:r.height+20}});return Ht(a,{shape:{x:r.x-50,width:r.width+100,height:r.height+20}},e,t),a}var $a=2,cy=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t){r.prototype.init.apply(this,arguments),this.legendVisualProvider=new pa(it(this.getData,this),it(this.getRawData,this))},e.prototype.fixData=function(t){var a=t.length,n={},i=ln(t,function(h){return n.hasOwnProperty(h[0]+"")||(n[h[0]+""]=-1),h[2]}),o=[];i.buckets.each(function(h,f){o.push({name:f,dataList:h})});for(var s=o.length,l=0;l<s;++l){for(var u=o[l].name,v=0;v<o[l].dataList.length;++v){var c=o[l].dataList[v][0]+"";n[c]=l}for(var c in n)n.hasOwnProperty(c)&&n[c]!==l&&(n[c]=l,t[a]=[c,0,u],a++)}return t},e.prototype.getInitialData=function(t,a){for(var n=this.getReferringComponents("singleAxis",Ke).models[0],i=n.get("type"),o=Rt(t.data,function(p){return p[2]!==void 0}),s=this.fixData(o||[]),l=[],u=this.nameMap=J(),v=0,c=0;c<s.length;++c)l.push(s[c][$a]),u.get(s[c][$a])||(u.set(s[c][$a],v),v++);var h=Mn(s,{coordDimensions:["single"],dimensionsDefine:[{name:"time",type:nc(i)},{name:"value",type:"float"},{name:"name",type:"ordinal"}],encodeDefine:{single:0,value:1,itemName:2}}).dimensions,f=new Pe(h,this);return f.initData(s),f},e.prototype.getLayerSeries=function(){for(var t=this.getData(),a=t.count(),n=[],i=0;i<a;++i)n[i]=i;var o=t.mapDimension("single"),s=ln(n,function(u){return t.get("name",u)}),l=[];return s.buckets.each(function(u,v){u.sort(function(c,h){return t.get(o,c)-t.get(o,h)}),l.push({name:v,indices:u})}),l},e.prototype.getAxisTooltipData=function(t,a,n){q(t)||(t=t?[t]:[]);for(var i=this.getData(),o=this.getLayerSeries(),s=[],l=o.length,u,v=0;v<l;++v){for(var c=Number.MAX_VALUE,h=-1,f=o[v].indices.length,p=0;p<f;++p){var d=i.get(t[0],o[v].indices[p]),g=Math.abs(d-a);g<=c&&(u=d,c=g,h=o[v].indices[p])}s.push(h)}return{dataIndices:s,nestestValue:u}},e.prototype.formatTooltip=function(t,a,n){var i=this.getData(),o=i.getName(t),s=i.get(i.mapDimension("value"),t);return Nt("nameValue",{name:o,value:s})},e.type="series.themeRiver",e.dependencies=["singleAxis"],e.defaultOption={z:2,colorBy:"data",coordinateSystem:"singleAxis",boundaryGap:["10%","10%"],singleAxisIndex:0,animationEasing:"linear",label:{margin:4,show:!0,position:"left",fontSize:11},emphasis:{label:{show:!0}}},e}(St);function hy(r,e){r.eachSeriesByType("themeRiver",function(t){var a=t.getData(),n=t.coordinateSystem,i={},o=n.getRect();i.rect=o;var s=t.get("boundaryGap"),l=n.getAxis();if(i.boundaryGap=s,l.orient==="horizontal"){s[0]=z(s[0],o.height),s[1]=z(s[1],o.height);var u=o.height-s[0]-s[1];Yo(a,t,u)}else{s[0]=z(s[0],o.width),s[1]=z(s[1],o.width);var v=o.width-s[0]-s[1];Yo(a,t,v)}a.setLayout("layoutInfo",i)})}function Yo(r,e,t){if(r.count())for(var a=e.coordinateSystem,n=e.getLayerSeries(),i=r.mapDimension("single"),o=r.mapDimension("value"),s=F(n,function(g){return F(g.indices,function(S){var m=a.dataToPoint(r.get(i,S));return m[1]=r.get(o,S),m})}),l=fy(s),u=l.y0,v=t/l.max,c=n.length,h=n[0].indices.length,f,p=0;p<h;++p){f=u[p]*v,r.setItemLayout(n[0].indices[p],{layerIndex:0,x:s[0][p][0],y0:f,y:s[0][p][1]*v});for(var d=1;d<c;++d)f+=s[d-1][p][1]*v,r.setItemLayout(n[d].indices[p],{layerIndex:d,x:s[d][p][0],y0:f,y:s[d][p][1]*v})}}function fy(r){for(var e=r.length,t=r[0].length,a=[],n=[],i=0,o=0;o<t;++o){for(var s=0,l=0;l<e;++l)s+=r[l][o][1];s>i&&(i=s),a.push(s)}for(var u=0;u<t;++u)n[u]=(i-a[u])/2;i=0;for(var v=0;v<t;++v){var c=a[v]+n[v];c>i&&(i=c)}return{y0:n,max:i}}function py(r){r.registerChartView(uy),r.registerSeriesModel(cy),r.registerLayout(hy),r.registerProcessor(da("themeRiver"))}var dy=2,gy=4,Zo=function(r){G(e,r);function e(t,a,n,i){var o=r.call(this)||this;o.z2=dy,o.textConfig={inside:!0},ft(o).seriesIndex=a.seriesIndex;var s=new Yt({z2:gy,silent:t.getModel().get(["label","silent"])});return o.setTextContent(s),o.updateData(!0,t,a,n,i),o}return e.prototype.updateData=function(t,a,n,i,o){this.node=a,a.piece=this,n=n||this._seriesModel,i=i||this._ecModel;var s=this;ft(s).dataIndex=a.dataIndex;var l=a.getModel(),u=l.getModel("emphasis"),v=a.getLayout(),c=W({},v);c.label=null;var h=a.getVisual("style");h.lineJoin="bevel";var f=a.getVisual("decal");f&&(h.decal=Ln(f,o));var p=Li(l.getModel("itemStyle"),c,!0);W(c,p),M(ic,function(m){var y=s.ensureState(m),w=l.getModel([m,"itemStyle"]);y.style=w.getItemStyle();var x=Li(w,c);x&&(y.shape=x)}),t?(s.setShape(c),s.shape.r=v.r0,Ht(s,{shape:{r:v.r}},n,a.dataIndex)):(ht(s,{shape:c},n),Ze(s)),s.useStyle(h),this._updateLabel(n);var d=l.getShallow("cursor");d&&s.attr("cursor",d),this._seriesModel=n||this._seriesModel,this._ecModel=i||this._ecModel;var g=u.get("focus"),S=g==="relative"?$r(a.getAncestorsIndices(),a.getDescendantIndices()):g==="ancestor"?a.getAncestorsIndices():g==="descendant"?a.getDescendantIndices():g;dt(this,S,u.get("blurScope"),u.get("disabled"))},e.prototype._updateLabel=function(t){var a=this,n=this.node.getModel(),i=n.getModel("label"),o=this.node.getLayout(),s=o.endAngle-o.startAngle,l=(o.startAngle+o.endAngle)/2,u=Math.cos(l),v=Math.sin(l),c=this,h=c.getTextContent(),f=this.node.dataIndex,p=i.get("minAngle")/180*Math.PI,d=i.get("show")&&!(p!=null&&Math.abs(s)<p);h.ignore=!d,M(sc,function(S){var m=S==="normal"?n.getModel("label"):n.getModel([S,"label"]),y=S==="normal",w=y?h:h.ensureState(S),x=t.getFormattedLabel(f,S);y&&(x=x||a.node.name),w.style=Bt(m,{},null,S!=="normal",!0),x&&(w.style.text=x);var b=m.get("show");b!=null&&!y&&(w.ignore=!b);var _=g(m,"position"),T=y?c:c.states[S],I=T.style.fill;T.textConfig={outsideFill:m.get("color")==="inherit"?I:null,inside:_!=="outside"};var A,D=g(m,"distance")||0,E=g(m,"align"),P=g(m,"rotate"),C=Math.PI*.5,L=Math.PI*1.5,R=Er(P==="tangential"?Math.PI/2-l:l),V=R>C&&!oc(R-C)&&R<L;_==="outside"?(A=o.r+D,E=V?"right":"left"):!E||E==="center"?(s===2*Math.PI&&o.r0===0?A=0:A=(o.r+o.r0)/2,E="center"):E==="left"?(A=o.r0+D,E=V?"right":"left"):E==="right"&&(A=o.r-D,E=V?"left":"right"),w.style.align=E,w.style.verticalAlign=g(m,"verticalAlign")||"middle",w.x=A*u+o.cx,w.y=A*v+o.cy;var N=0;P==="radial"?N=Er(-l)+(V?Math.PI:0):P==="tangential"?N=Er(Math.PI/2-l)+(V?Math.PI:0):jt(P)&&(N=P*Math.PI/180),w.rotation=Er(N)});function g(S,m){var y=S.get(m);return y??i.get(m)}h.dirtyStyle()},e}(Me),yn="sunburstRootToNode",Xo="sunburstHighlight",yy="sunburstUnhighlight";function my(r){r.registerAction({type:yn,update:"updateView"},function(e,t){t.eachComponent({mainType:"series",subType:"sunburst",query:e},a);function a(n,i){var o=yr(e,[yn],n);if(o){var s=n.getViewRoot();s&&(e.direction=jn(s,o.node)?"rollUp":"drillDown"),n.resetViewRoot(o.node)}}}),r.registerAction({type:Xo,update:"none"},function(e,t,a){e=W({},e),t.eachComponent({mainType:"series",subType:"sunburst",query:e},n);function n(i){var o=yr(e,[Xo],i);o&&(e.dataIndex=o.node.dataIndex)}a.dispatchAction(W(e,{type:"highlight"}))}),r.registerAction({type:yy,update:"updateView"},function(e,t,a){e=W({},e),a.dispatchAction(W(e,{type:"downplay"}))})}var Sy=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n,i){var o=this;this.seriesModel=t,this.api=n,this.ecModel=a;var s=t.getData(),l=s.tree.root,u=t.getViewRoot(),v=this.group,c=t.get("renderLabelForZeroData"),h=[];u.eachNode(function(m){h.push(m)});var f=this._oldChildren||[];p(h,f),S(l,u),this._initEvents(),this._oldChildren=h;function p(m,y){if(m.length===0&&y.length===0)return;new je(y,m,w,w).add(x).update(x).remove(bt(x,null)).execute();function w(b){return b.getId()}function x(b,_){var T=b==null?null:m[b],I=_==null?null:y[_];d(T,I)}}function d(m,y){if(!c&&m&&!m.getValue()&&(m=null),m!==l&&y!==l){if(y&&y.piece)m?(y.piece.updateData(!1,m,t,a,n),s.setItemGraphicEl(m.dataIndex,y.piece)):g(y);else if(m){var w=new Zo(m,t,a,n);v.add(w),s.setItemGraphicEl(m.dataIndex,w)}}}function g(m){m&&m.piece&&(v.remove(m.piece),m.piece=null)}function S(m,y){y.depth>0?(o.virtualPiece?o.virtualPiece.updateData(!1,m,t,a,n):(o.virtualPiece=new Zo(m,t,a,n),v.add(o.virtualPiece)),y.piece.off("click"),o.virtualPiece.on("click",function(w){o._rootToNode(y.parentNode)})):o.virtualPiece&&(v.remove(o.virtualPiece),o.virtualPiece=null)}},e.prototype._initEvents=function(){var t=this;this.group.off("click"),this.group.on("click",function(a){var n=!1,i=t.seriesModel.getViewRoot();i.eachNode(function(o){if(!n&&o.piece&&o.piece===a.target){var s=o.getModel().get("nodeClick");if(s==="rootToNode")t._rootToNode(o);else if(s==="link"){var l=o.getModel(),u=l.get("link");if(u){var v=l.get("target",!0)||"_blank";nl(u,v)}}n=!0}})})},e.prototype._rootToNode=function(t){t!==this.seriesModel.getViewRoot()&&this.api.dispatchAction({type:yn,from:this.uid,seriesId:this.seriesModel.id,targetNode:t})},e.prototype.containPoint=function(t,a){var n=a.getData(),i=n.getItemLayout(0);if(i){var o=t[0]-i.cx,s=t[1]-i.cy,l=Math.sqrt(o*o+s*s);return l<=i.r&&l>=i.r0}},e.type="sunburst",e}(gt),xy=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.ignoreStyleOnData=!0,t}return e.prototype.getInitialData=function(t,a){var n={name:t.name,children:t.data};Pu(n);var i=this._levelModels=F(t.levels||[],function(l){return new Lt(l,this,a)},this),o=qn.createTree(n,this,s);function s(l){l.wrapMethod("getItemModel",function(u,v){var c=o.getNodeByDataIndex(v),h=i[c.depth];return h&&(u.parentModel=h),u})}return o.data},e.prototype.optionUpdated=function(){this.resetViewRoot()},e.prototype.getDataParams=function(t){var a=r.prototype.getDataParams.apply(this,arguments),n=this.getData().tree.getNodeByDataIndex(t);return a.treePathInfo=ya(n,this),a},e.prototype.getLevelModel=function(t){return this._levelModels&&this._levelModels[t.depth]},e.prototype.getViewRoot=function(){return this._viewRoot},e.prototype.resetViewRoot=function(t){t?this._viewRoot=t:t=this._viewRoot;var a=this.getRawData().tree.root;(!t||t!==a&&!a.contains(t))&&(this._viewRoot=a)},e.prototype.enableAriaDecal=function(){eu(this)},e.type="series.sunburst",e.defaultOption={z:2,center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,minAngle:0,stillShowZeroSum:!0,nodeClick:"rootToNode",renderLabelForZeroData:!1,label:{rotate:"radial",show:!0,opacity:1,align:"center",position:"inside",distance:5,silent:!0},itemStyle:{borderWidth:1,borderColor:"white",borderType:"solid",shadowBlur:0,shadowColor:"rgba(0, 0, 0, 0.2)",shadowOffsetX:0,shadowOffsetY:0,opacity:1},emphasis:{focus:"descendant"},blur:{itemStyle:{opacity:.2},label:{opacity:.1}},animationType:"expansion",animationDuration:1e3,animationDurationUpdate:500,data:[],sort:"desc"},e}(St);function Pu(r){var e=0;M(r.children,function(a){Pu(a);var n=a.value;q(n)&&(n=n[0]),e+=n});var t=r.value;q(t)&&(t=t[0]),(t==null||isNaN(t))&&(t=e),t<0&&(t=0),q(r.value)?r.value[0]=t:r.value=t}var qo=Math.PI/180;function by(r,e,t){e.eachSeriesByType(r,function(a){var n=a.get("center"),i=a.get("radius");q(i)||(i=[0,i]),q(n)||(n=[n,n]);var o=t.getWidth(),s=t.getHeight(),l=Math.min(o,s),u=z(n[0],o),v=z(n[1],s),c=z(i[0],l/2),h=z(i[1],l/2),f=-a.get("startAngle")*qo,p=a.get("minAngle")*qo,d=a.getData().tree.root,g=a.getViewRoot(),S=g.depth,m=a.get("sort");m!=null&&Mu(g,m);var y=0;M(g.children,function(R){!isNaN(R.getValue())&&y++});var w=g.getValue(),x=Math.PI/(w||y)*2,b=g.depth>0,_=g.height-(b?-1:1),T=(h-c)/(_||1),I=a.get("clockwise"),A=a.get("stillShowZeroSum"),D=I?1:-1,E=function(R,V){if(R){var N=V;if(R!==d){var k=R.getValue(),O=w===0&&A?x:k*x;O<p&&(O=p),N=V+D*O;var H=R.depth-S-(b?-1:1),Y=c+T*H,tt=c+T*(H+1),K=a.getLevelModel(R);if(K){var et=K.get("r0",!0),vt=K.get("r",!0),kt=K.get("radius",!0);kt!=null&&(et=kt[0],vt=kt[1]),et!=null&&(Y=z(et,l/2)),vt!=null&&(tt=z(vt,l/2))}R.setLayout({angle:O,startAngle:V,endAngle:N,clockwise:I,cx:u,cy:v,r0:Y,r:tt})}if(R.children&&R.children.length){var U=0;M(R.children,function(B){U+=E(B,V+U)})}return N-V}};if(b){var P=c,C=c+T,L=Math.PI*2;d.setLayout({angle:L,startAngle:f,endAngle:f+L,clockwise:I,cx:u,cy:v,r0:P,r:C})}E(g,f)})}function Mu(r,e){var t=r.children||[];r.children=wy(t,e),t.length&&M(r.children,function(a){Mu(a,e)})}function wy(r,e){if(st(e)){var t=F(r,function(n,i){var o=n.getValue();return{params:{depth:n.depth,height:n.height,dataIndex:n.dataIndex,getValue:function(){return o}},index:i}});return t.sort(function(n,i){return e(n.params,i.params)}),F(t,function(n){return r[n.index]})}else{var a=e==="asc";return r.sort(function(n,i){var o=(n.getValue()-i.getValue())*(a?1:-1);return o===0?(n.dataIndex-i.dataIndex)*(a?-1:1):o})}}function _y(r){var e={};function t(a,n,i){for(var o=a;o&&o.depth>1;)o=o.parentNode;var s=n.getColorFromPalette(o.name||o.dataIndex+"",e);return a.depth>1&&ot(s)&&(s=lc(s,(a.depth-1)/(i-1)*.5)),s}r.eachSeriesByType("sunburst",function(a){var n=a.getData(),i=n.tree;i.eachNode(function(o){var s=o.getModel(),l=s.getModel("itemStyle").getItemStyle();l.fill||(l.fill=t(o,a,i.root.height));var u=n.ensureUniqueItemVisual(o.dataIndex,"style");W(u,l)})})}function Ay(r){r.registerChartView(Sy),r.registerSeriesModel(xy),r.registerLayout(bt(by,"sunburst")),r.registerProcessor(bt(da,"sunburst")),r.registerVisual(_y),my(r)}var jo={color:"fill",borderColor:"stroke"},Ty={symbol:1,symbolSize:1,symbolKeepAspect:1,legendIcon:1,visualMeta:1,liftZ:1,decal:1},Zt=fe(),Iy=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.optionUpdated=function(){this.currentZLevel=this.get("zlevel",!0),this.currentZ=this.get("z",!0)},e.prototype.getInitialData=function(t,a){return Cr(null,this)},e.prototype.getDataParams=function(t,a,n){var i=r.prototype.getDataParams.call(this,t,a);return n&&(i.info=Zt(n).info),i},e.type="series.custom",e.dependencies=["grid","polar","geo","singleAxis","calendar"],e.defaultOption={coordinateSystem:"cartesian2d",z:2,legendHoverLink:!0,clip:!1},e}(St);function Dy(r,e){return e=e||[0,0],F(["x","y"],function(t,a){var n=this.getAxis(t),i=e[a],o=r[a]/2;return n.type==="category"?n.getBandWidth():Math.abs(n.dataToCoord(i-o)-n.dataToCoord(i+o))},this)}function Cy(r){var e=r.master.getRect();return{coordSys:{type:"cartesian2d",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(t){return r.dataToPoint(t)},size:it(Dy,r)}}}function Ly(r,e){return e=e||[0,0],F([0,1],function(t){var a=e[t],n=r[t]/2,i=[],o=[];return i[t]=a-n,o[t]=a+n,i[1-t]=o[1-t]=e[1-t],Math.abs(this.dataToPoint(i)[t]-this.dataToPoint(o)[t])},this)}function Py(r){var e=r.getBoundingRect();return{coordSys:{type:"geo",x:e.x,y:e.y,width:e.width,height:e.height,zoom:r.getZoom()},api:{coord:function(t){return r.dataToPoint(t)},size:it(Ly,r)}}}function My(r,e){var t=this.getAxis(),a=e instanceof Array?e[0]:e,n=(r instanceof Array?r[0]:r)/2;return t.type==="category"?t.getBandWidth():Math.abs(t.dataToCoord(a-n)-t.dataToCoord(a+n))}function Ey(r){var e=r.getRect();return{coordSys:{type:"singleAxis",x:e.x,y:e.y,width:e.width,height:e.height},api:{coord:function(t){return r.dataToPoint(t)},size:it(My,r)}}}function Ry(r,e){return e=e||[0,0],F(["Radius","Angle"],function(t,a){var n="get"+t+"Axis",i=this[n](),o=e[a],s=r[a]/2,l=i.type==="category"?i.getBandWidth():Math.abs(i.dataToCoord(o-s)-i.dataToCoord(o+s));return t==="Angle"&&(l=l*Math.PI/180),l},this)}function Vy(r){var e=r.getRadiusAxis(),t=r.getAngleAxis(),a=e.getExtent();return a[0]>a[1]&&a.reverse(),{coordSys:{type:"polar",cx:r.cx,cy:r.cy,r:a[1],r0:a[0]},api:{coord:function(n){var i=e.dataToRadius(n[0]),o=t.dataToAngle(n[1]),s=r.coordToPoint([i,o]);return s.push(i,o*Math.PI/180),s},size:it(Ry,r)}}}function Ny(r){var e=r.getRect(),t=r.getRangeInfo();return{coordSys:{type:"calendar",x:e.x,y:e.y,width:e.width,height:e.height,cellWidth:r.getCellWidth(),cellHeight:r.getCellHeight(),rangeInfo:{start:t.start,end:t.end,weeks:t.weeks,dayCount:t.allDay}},api:{coord:function(a,n){return r.dataToPoint(a,n)}}}}var Xt="emphasis",ie="normal",ri="blur",ai="select",ve=[ie,Xt,ri,ai],Ya={normal:["itemStyle"],emphasis:[Xt,"itemStyle"],blur:[ri,"itemStyle"],select:[ai,"itemStyle"]},Za={normal:["label"],emphasis:[Xt,"label"],blur:[ri,"label"],select:[ai,"label"]},Gy=["x","y"],ky="e\0\0",It={normal:{},emphasis:{},blur:{},select:{}},zy={cartesian2d:Cy,geo:Py,single:Ey,polar:Vy,calendar:Ny};function mn(r){return r instanceof Gt}function Sn(r){return r instanceof dr}function Oy(r,e){e.copyTransform(r),Sn(e)&&Sn(r)&&(e.setStyle(r.style),e.z=r.z,e.z2=r.z2,e.zlevel=r.zlevel,e.invisible=r.invisible,e.ignore=r.ignore,mn(e)&&mn(r)&&e.setShape(r.shape))}var By=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.render=function(t,a,n,i){this._progressiveEls=null;var o=this._data,s=t.getData(),l=this.group,u=Ko(t,s,a,n);o||l.removeAll(),s.diff(o).add(function(c){Xa(n,null,c,u(c,i),t,l,s)}).remove(function(c){var h=o.getItemGraphicEl(c);h&&Wn(h,Zt(h).option,t)}).update(function(c,h){var f=o.getItemGraphicEl(h);Xa(n,f,c,u(c,i),t,l,s)}).execute();var v=t.get("clip",!0)?Hn(t.coordinateSystem,!1,t):null;v?l.setClipPath(v):l.removeClipPath(),this._data=s},e.prototype.incrementalPrepareRender=function(t,a,n){this.group.removeAll(),this._data=null},e.prototype.incrementalRender=function(t,a,n,i,o){var s=a.getData(),l=Ko(a,s,n,i),u=this._progressiveEls=[];function v(f){f.isGroup||(f.incremental=!0,f.ensureState("emphasis").hoverLayer=!0)}for(var c=t.start;c<t.end;c++){var h=Xa(null,null,c,l(c,o),a,this.group,s);h&&(h.traverse(v),u.push(h))}},e.prototype.eachRendered=function(t){dl(this._progressiveEls||this.group,t)},e.prototype.filterForExposedEvent=function(t,a,n,i){var o=a.element;if(o==null||n.name===o)return!0;for(;(n=n.__hostTarget||n.parent)&&n!==this.group;)if(n.name===o)return!0;return!1},e.type="custom",e}(gt);function ni(r){var e=r.type,t;if(e==="path"){var a=r.shape,n=a.width!=null&&a.height!=null?{x:a.x||0,y:a.y||0,width:a.width,height:a.height}:null,i=Vu(a);t=cc(i,null,n,a.layout||"center"),Zt(t).customPathData=i}else if(e==="image")t=new ce({}),Zt(t).customImagePath=r.style.image;else if(e==="text")t=new Yt({});else if(e==="group")t=new X;else{if(e==="compoundPath")throw new Error('"compoundPath" is not supported yet.');var o=hc(e);if(!o){var s="";fc(s)}t=new o}return Zt(t).customGraphicType=e,t.name=r.name,t.z2EmphasisLift=1,t.z2SelectLift=1,t}function ii(r,e,t,a,n,i,o){rh(e);var s=n&&n.normal.cfg;s&&e.setTextConfig(s),a&&a.transition==null&&(a.transition=Gy);var l=a&&a.style;if(l){if(e.type==="text"){var u=l;xt(u,"textFill")&&(u.fill=u.textFill),xt(u,"textStroke")&&(u.stroke=u.textStroke)}var v=void 0,c=mn(e)?l.decal:null;r&&c&&(c.dirty=!0,v=Ln(c,r)),l.__decalPattern=v}if(Sn(e)&&l){var v=l.__decalPattern;v&&(l.decal=v)}ah(e,a,i,{dataIndex:t,isInit:o,clearStyle:!0}),nh(e,a.keyframeAnimation,i)}function Eu(r,e,t,a,n){var i=e.isGroup?null:e,o=n&&n[r].cfg;if(i){var s=i.ensureState(r);if(a===!1){var l=i.getState(r);l&&(l.style=null)}else s.style=a||null;o&&(s.textConfig=o),Xe(i)}}function Fy(r,e,t){if(!r.isGroup){var a=r,n=t.currentZ,i=t.currentZLevel;a.z=n,a.zlevel=i;var o=e.z2;o!=null&&(a.z2=o||0);for(var s=0;s<ve.length;s++)Hy(a,e,ve[s])}}function Hy(r,e,t){var a=t===ie,n=a?e:ea(e,t),i=n?n.z2:null,o;i!=null&&(o=a?r:r.ensureState(t),o.z2=i||0)}function Ko(r,e,t,a){var n=r.get("renderItem"),i=r.coordinateSystem,o={};i&&(o=i.prepareCustoms?i.prepareCustoms(i):zy[i.type](i));for(var s=j({getWidth:a.getWidth,getHeight:a.getHeight,getZr:a.getZr,getDevicePixelRatio:a.getDevicePixelRatio,value:w,style:b,ordinalRawValue:x,styleEmphasis:_,visual:A,barLayout:D,currentSeriesIndices:E,font:P},o.api||{}),l={context:{},seriesId:r.id,seriesName:r.name,seriesIndex:r.seriesIndex,coordSys:o.coordSys,dataInsideLength:e.count(),encode:Wy(r.getData())},u,v,c={},h={},f={},p={},d=0;d<ve.length;d++){var g=ve[d];f[g]=r.getModel(Ya[g]),p[g]=r.getModel(Za[g])}function S(C){return C===u?v||(v=e.getItemModel(C)):e.getItemModel(C)}function m(C,L){return e.hasItemOption?C===u?c[L]||(c[L]=S(C).getModel(Ya[L])):S(C).getModel(Ya[L]):f[L]}function y(C,L){return e.hasItemOption?C===u?h[L]||(h[L]=S(C).getModel(Za[L])):S(C).getModel(Za[L]):p[L]}return function(C,L){return u=C,v=null,c={},h={},n&&n(j({dataIndexInside:C,dataIndex:e.getRawIndex(C),actionType:L?L.type:null},l),s)};function w(C,L){return L==null&&(L=u),e.getStore().get(e.getDimensionIndex(C||0),L)}function x(C,L){L==null&&(L=u),C=C||0;var R=e.getDimensionInfo(C);if(!R){var V=e.getDimensionIndex(C);return V>=0?e.getStore().get(V,L):void 0}var N=e.get(R.name,L),k=R&&R.ordinalMeta;return k?k.categories[N]:N}function b(C,L){L==null&&(L=u);var R=e.getItemVisual(L,"style"),V=R&&R.fill,N=R&&R.opacity,k=m(L,ie).getItemStyle();V!=null&&(k.fill=V),N!=null&&(k.opacity=N);var O={inheritColor:ot(V)?V:"#000"},H=y(L,ie),Y=Bt(H,null,O,!1,!0);Y.text=H.getShallow("show")?oe(r.getFormattedLabel(L,ie),un(e,L)):null;var tt=Pi(H,O,!1);return I(C,k),k=ki(k,Y,tt),C&&T(k,C),k.legacy=!0,k}function _(C,L){L==null&&(L=u);var R=m(L,Xt).getItemStyle(),V=y(L,Xt),N=Bt(V,null,null,!0,!0);N.text=V.getShallow("show")?Ir(r.getFormattedLabel(L,Xt),r.getFormattedLabel(L,ie),un(e,L)):null;var k=Pi(V,null,!0);return I(C,R),R=ki(R,N,k),C&&T(R,C),R.legacy=!0,R}function T(C,L){for(var R in L)xt(L,R)&&(C[R]=L[R])}function I(C,L){C&&(C.textFill&&(L.textFill=C.textFill),C.textPosition&&(L.textPosition=C.textPosition))}function A(C,L){if(L==null&&(L=u),xt(jo,C)){var R=e.getItemVisual(L,"style");return R?R[jo[C]]:null}if(xt(Ty,C))return e.getItemVisual(L,C)}function D(C){if(i.type==="cartesian2d"){var L=i.getBaseAxis();return uc(j({axis:L},C))}}function E(){return t.getCurrentSeriesIndices()}function P(C){return vc(C,t)}}function Wy(r){var e={};return M(r.dimensions,function(t){var a=r.getDimensionInfo(t);if(!a.isExtraCoord){var n=a.coordDim,i=e[n]=e[n]||[];i[a.coordDimIndex]=r.getDimensionIndex(t)}}),e}function Xa(r,e,t,a,n,i,o){if(!a){i.remove(e);return}var s=oi(r,e,t,a,n,i);return s&&o.setItemGraphicEl(t,s),s&&dt(s,a.focus,a.blurScope,a.emphasisDisabled),s}function oi(r,e,t,a,n,i){var o=-1,s=e;e&&Ru(e,a,n)&&(o=Ge(i.childrenRef(),e),e=null);var l=!e,u=e;u?u.clearStates():(u=ni(a),s&&Oy(s,u)),a.morph===!1?u.disableMorphing=!0:u.disableMorphing&&(u.disableMorphing=!1),It.normal.cfg=It.normal.conOpt=It.emphasis.cfg=It.emphasis.conOpt=It.blur.cfg=It.blur.conOpt=It.select.cfg=It.select.conOpt=null,It.isLegacy=!1,$y(u,t,a,n,l,It),Uy(u,t,a,n,l),ii(r,u,t,a,It,n,l),xt(a,"info")&&(Zt(u).info=a.info);for(var v=0;v<ve.length;v++){var c=ve[v];if(c!==ie){var h=ea(a,c),f=si(a,h,c);Eu(c,u,h,f,It)}}return Fy(u,a,n),a.type==="group"&&Yy(r,u,t,a,n),o>=0?i.replaceAt(u,o):i.add(u),u}function Ru(r,e,t){var a=Zt(r),n=e.type,i=e.shape,o=e.style;return t.isUniversalTransitionEnabled()||n!=null&&n!==a.customGraphicType||n==="path"&&jy(i)&&Vu(i)!==a.customPathData||n==="image"&&xt(o,"image")&&o.image!==a.customImagePath}function Uy(r,e,t,a,n){var i=t.clipPath;if(i===!1)r&&r.getClipPath()&&r.removeClipPath();else if(i){var o=r.getClipPath();o&&Ru(o,i,a)&&(o=null),o||(o=ni(i),r.setClipPath(o)),ii(null,o,e,i,null,a,n)}}function $y(r,e,t,a,n,i){if(!r.isGroup){Jo(t,null,i),Jo(t,Xt,i);var o=i.normal.conOpt,s=i.emphasis.conOpt,l=i.blur.conOpt,u=i.select.conOpt;if(o!=null||s!=null||u!=null||l!=null){var v=r.getTextContent();if(o===!1)v&&r.removeTextContent();else{o=i.normal.conOpt=o||{type:"text"},v?v.clearStates():(v=ni(o),r.setTextContent(v)),ii(null,v,e,o,null,a,n);for(var c=o&&o.style,h=0;h<ve.length;h++){var f=ve[h];if(f!==ie){var p=i[f].conOpt;Eu(f,v,p,si(o,p,f),null)}}c?v.dirty():v.markRedraw()}}}}function Jo(r,e,t){var a=e?ea(r,e):r,n=e?si(r,a,Xt):r.style,i=r.type,o=a?a.textConfig:null,s=r.textContent,l=s?e?ea(s,e):s:null;if(n&&(t.isLegacy||ih(n,i,!!o,!!l))){t.isLegacy=!0;var u=oh(n,i,!e);!o&&u.textConfig&&(o=u.textConfig),!l&&u.textContent&&(l=u.textContent)}if(!e&&l){var v=l;!v.type&&(v.type="text")}var c=e?t[e]:t.normal;c.cfg=o,c.conOpt=l}function ea(r,e){return e?r?r[e]:null:r}function si(r,e,t){var a=e&&e.style;return a==null&&t===Xt&&r&&(a=r.styleEmphasis),a}function Yy(r,e,t,a,n){var i=a.children,o=i?i.length:0,s=a.$mergeChildren,l=s==="byName"||a.diffChildrenByName,u=s===!1;if(!(!o&&!l&&!u)){if(l){Xy({api:r,oldChildren:e.children()||[],newChildren:i||[],dataIndex:t,seriesModel:n,group:e});return}u&&e.removeAll();for(var v=0;v<o;v++){var c=i[v],h=e.childAt(v);c?(c.ignore==null&&(c.ignore=!1),oi(r,h,t,c,n,e)):h.ignore=!0}for(var f=e.childCount()-1;f>=v;f--){var p=e.childAt(f);Zy(e,p,n)}}}function Zy(r,e,t){e&&Wn(e,Zt(r).option,t)}function Xy(r){new je(r.oldChildren,r.newChildren,Qo,Qo,r).add(ts).update(ts).remove(qy).execute()}function Qo(r,e){var t=r&&r.name;return t??ky+e}function ts(r,e){var t=this.context,a=r!=null?t.newChildren[r]:null,n=e!=null?t.oldChildren[e]:null;oi(t.api,n,t.dataIndex,a,t.seriesModel,t.group)}function qy(r){var e=this.context,t=e.oldChildren[r];t&&Wn(t,Zt(t).option,e.seriesModel)}function Vu(r){return r&&(r.pathData||r.d)}function jy(r){return r&&(xt(r,"pathData")||xt(r,"d"))}function Ky(r){r.registerChartView(By),r.registerSeriesModel(Iy)}var Jy=function(r){G(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.makeElOption=function(t,a,n,i,o){var s=n.axis;s.dim==="angle"&&(this.animationThreshold=Math.PI/18);var l=s.polar,u=l.getOtherAxis(s),v=u.getExtent(),c=s.dataToCoord(a),h=i.get("type");if(h&&h!=="none"){var f=gl(i),p=tm[h](s,l,c,v);p.style=f,t.graphicKey=p.type,t.pointer=p}var d=i.get(["label","margin"]),g=Qy(a,n,i,l,d);pc(t,n,i,o,g)},e}(yl);function Qy(r,e,t,a,n){var i=e.axis,o=i.dataToCoord(r),s=a.getAngleAxis().getExtent()[0];s=s/180*Math.PI;var l=a.getRadiusAxis().getExtent(),u,v,c;if(i.dim==="radius"){var h=Ar();Cn(h,h,s),pr(h,h,[a.cx,a.cy]),u=cl([o,-n],h);var f=e.getModel("axisLabel").get("rotate")||0,p=Ce.innerTextLayout(s,f*Math.PI/180,-1);v=p.textAlign,c=p.textVerticalAlign}else{var d=l[1];u=a.coordToPoint([d+n,o]);var g=a.cx,S=a.cy;v=Math.abs(u[0]-g)/d<.3?"center":u[0]>g?"left":"right",c=Math.abs(u[1]-S)/d<.3?"middle":u[1]>S?"top":"bottom"}return{position:u,align:v,verticalAlign:c}}var tm={line:function(r,e,t,a){return r.dim==="angle"?{type:"Line",shape:ml(e.coordToPoint([a[0],t]),e.coordToPoint([a[1],t]))}:{type:"Circle",shape:{cx:e.cx,cy:e.cy,r:t}}},shadow:function(r,e,t,a){var n=Math.max(1,r.getBandWidth()),i=Math.PI/180;return r.dim==="angle"?{type:"Sector",shape:Mi(e.cx,e.cy,a[0],a[1],(-t-n/2)*i,(-t+n/2)*i)}:{type:"Sector",shape:Mi(e.cx,e.cy,t-n/2,t+n/2,0,Math.PI*2)}}},em=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.findAxisModel=function(t){var a,n=this.ecModel;return n.eachComponent(t,function(i){i.getCoordSysModel()===this&&(a=i)},this),a},e.type="polar",e.dependencies=["radiusAxis","angleAxis"],e.defaultOption={z:0,center:["50%","50%"],radius:"80%"},e}(he),li=function(r){G(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.getCoordSysModel=function(){return this.getReferringComponents("polar",Ke).models[0]},e.type="polarAxis",e}(he);Ve(li,ha);var rm=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.type="angleAxis",e}(li),am=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.type="radiusAxis",e}(li),ui=function(r){G(e,r);function e(t,a){return r.call(this,"radius",t,a)||this}return e.prototype.pointToData=function(t,a){return this.polar.pointToData(t,a)[this.dim==="radius"?0:1]},e}(Jt);ui.prototype.dataToRadius=Jt.prototype.dataToCoord;ui.prototype.radiusToData=Jt.prototype.coordToData;var nm=fe(),vi=function(r){G(e,r);function e(t,a){return r.call(this,"angle",t,a||[0,360])||this}return e.prototype.pointToData=function(t,a){return this.polar.pointToData(t,a)[this.dim==="radius"?0:1]},e.prototype.calculateCategoryInterval=function(){var t=this,a=t.getLabelModel(),n=t.scale,i=n.getExtent(),o=n.count();if(i[1]-i[0]<1)return 0;var s=i[0],l=t.dataToCoord(s+1)-t.dataToCoord(s),u=Math.abs(l),v=dc(s==null?"":s+"",a.getFont(),"center","top"),c=Math.max(v.height,7),h=c/u;isNaN(h)&&(h=1/0);var f=Math.max(0,Math.floor(h)),p=nm(t.model),d=p.lastAutoInterval,g=p.lastTickCount;return d!=null&&g!=null&&Math.abs(d-f)<=1&&Math.abs(g-o)<=1&&d>f?f=d:(p.lastTickCount=o,p.lastAutoInterval=f),f},e}(Jt);vi.prototype.dataToAngle=Jt.prototype.dataToCoord;vi.prototype.angleToData=Jt.prototype.coordToData;var Nu=["radius","angle"],im=function(){function r(e){this.dimensions=Nu,this.type="polar",this.cx=0,this.cy=0,this._radiusAxis=new ui,this._angleAxis=new vi,this.axisPointerEnabled=!0,this.name=e||"",this._radiusAxis.polar=this._angleAxis.polar=this}return r.prototype.containPoint=function(e){var t=this.pointToCoord(e);return this._radiusAxis.contain(t[0])&&this._angleAxis.contain(t[1])},r.prototype.containData=function(e){return this._radiusAxis.containData(e[0])&&this._angleAxis.containData(e[1])},r.prototype.getAxis=function(e){var t="_"+e+"Axis";return this[t]},r.prototype.getAxes=function(){return[this._radiusAxis,this._angleAxis]},r.prototype.getAxesByScale=function(e){var t=[],a=this._angleAxis,n=this._radiusAxis;return a.scale.type===e&&t.push(a),n.scale.type===e&&t.push(n),t},r.prototype.getAngleAxis=function(){return this._angleAxis},r.prototype.getRadiusAxis=function(){return this._radiusAxis},r.prototype.getOtherAxis=function(e){var t=this._angleAxis;return e===t?this._radiusAxis:t},r.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAngleAxis()},r.prototype.getTooltipAxes=function(e){var t=e!=null&&e!=="auto"?this.getAxis(e):this.getBaseAxis();return{baseAxes:[t],otherAxes:[this.getOtherAxis(t)]}},r.prototype.dataToPoint=function(e,t){return this.coordToPoint([this._radiusAxis.dataToRadius(e[0],t),this._angleAxis.dataToAngle(e[1],t)])},r.prototype.pointToData=function(e,t){var a=this.pointToCoord(e);return[this._radiusAxis.radiusToData(a[0],t),this._angleAxis.angleToData(a[1],t)]},r.prototype.pointToCoord=function(e){var t=e[0]-this.cx,a=e[1]-this.cy,n=this.getAngleAxis(),i=n.getExtent(),o=Math.min(i[0],i[1]),s=Math.max(i[0],i[1]);n.inverse?o=s-360:s=o+360;var l=Math.sqrt(t*t+a*a);t/=l,a/=l;for(var u=Math.atan2(-a,t)/Math.PI*180,v=u<o?1:-1;u<o||u>s;)u+=v*360;return[l,u]},r.prototype.coordToPoint=function(e){var t=e[0],a=e[1]/180*Math.PI,n=Math.cos(a)*t+this.cx,i=-Math.sin(a)*t+this.cy;return[n,i]},r.prototype.getArea=function(){var e=this.getAngleAxis(),t=this.getRadiusAxis(),a=t.getExtent().slice();a[0]>a[1]&&a.reverse();var n=e.getExtent(),i=Math.PI/180,o=1e-4;return{cx:this.cx,cy:this.cy,r0:a[0],r:a[1],startAngle:-n[0]*i,endAngle:-n[1]*i,clockwise:e.inverse,contain:function(s,l){var u=s-this.cx,v=l-this.cy,c=u*u+v*v,h=this.r,f=this.r0;return h!==f&&c-o<=h*h&&c+o>=f*f}}},r.prototype.convertToPixel=function(e,t,a){var n=es(t);return n===this?this.dataToPoint(a):null},r.prototype.convertFromPixel=function(e,t,a){var n=es(t);return n===this?this.pointToData(a):null},r}();function es(r){var e=r.seriesModel,t=r.polarModel;return t&&t.coordinateSystem||e&&e.coordinateSystem}function om(r,e,t){var a=e.get("center"),n=t.getWidth(),i=t.getHeight();r.cx=z(a[0],n),r.cy=z(a[1],i);var o=r.getRadiusAxis(),s=Math.min(n,i)/2,l=e.get("radius");l==null?l=[0,"100%"]:q(l)||(l=[0,l]);var u=[z(l[0],s),z(l[1],s)];o.inverse?o.setExtent(u[1],u[0]):o.setExtent(u[0],u[1])}function sm(r,e){var t=this,a=t.getAngleAxis(),n=t.getRadiusAxis();if(a.scale.setExtent(1/0,-1/0),n.scale.setExtent(1/0,-1/0),r.eachSeries(function(s){if(s.coordinateSystem===t){var l=s.getData();M(Ei(l,"radius"),function(u){n.scale.unionExtentFromData(l,u)}),M(Ei(l,"angle"),function(u){a.scale.unionExtentFromData(l,u)})}}),Zr(a.scale,a.model),Zr(n.scale,n.model),a.type==="category"&&!a.onBand){var i=a.getExtent(),o=360/a.scale.count();a.inverse?i[1]+=o:i[1]-=o,a.setExtent(i[0],i[1])}}function lm(r){return r.mainType==="angleAxis"}function rs(r,e){var t;if(r.type=e.get("type"),r.scale=Vn(e),r.onBand=e.get("boundaryGap")&&r.type==="category",r.inverse=e.get("inverse"),lm(e)){r.inverse=r.inverse!==e.get("clockwise");var a=e.get("startAngle"),n=(t=e.get("endAngle"))!==null&&t!==void 0?t:a+(r.inverse?-360:360);r.setExtent(a,n)}e.axis=r,r.model=e}var um={dimensions:Nu,create:function(r,e){var t=[];return r.eachComponent("polar",function(a,n){var i=new im(n+"");i.update=sm;var o=i.getRadiusAxis(),s=i.getAngleAxis(),l=a.findAxisModel("radiusAxis"),u=a.findAxisModel("angleAxis");rs(o,l),rs(s,u),om(i,a,e),t.push(i),a.coordinateSystem=i,i.model=a}),r.eachSeries(function(a){if(a.get("coordinateSystem")==="polar"){var n=a.getReferringComponents("polar",Ke).models[0];a.coordinateSystem=n.coordinateSystem}}),t}},vm=["axisLine","axisLabel","axisTick","minorTick","splitLine","minorSplitLine","splitArea"];function Br(r,e,t){e[1]>e[0]&&(e=e.slice().reverse());var a=r.coordToPoint([e[0],t]),n=r.coordToPoint([e[1],t]);return{x1:a[0],y1:a[1],x2:n[0],y2:n[1]}}function Fr(r){var e=r.getRadiusAxis();return e.inverse?0:1}function as(r){var e=r[0],t=r[r.length-1];e&&t&&Math.abs(Math.abs(e.coord-t.coord)-360)<1e-4&&r.pop()}var cm=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="PolarAxisPointer",t}return e.prototype.render=function(t,a){if(this.group.removeAll(),!!t.get("show")){var n=t.axis,i=n.polar,o=i.getRadiusAxis().getExtent(),s=n.getTicksCoords(),l=n.getMinorTicksCoords(),u=F(n.getViewLabels(),function(v){v=Ut(v);var c=n.scale,h=c.type==="ordinal"?c.getRawOrdinalNumber(v.tickValue):v.tickValue;return v.coord=n.dataToCoord(h),v});as(u),as(s),M(vm,function(v){t.get([v,"show"])&&(!n.scale.isBlank()||v==="axisLine")&&hm[v](this.group,t,i,s,l,o,u)},this)}},e.type="angleAxis",e}(Dr),hm={axisLine:function(r,e,t,a,n,i){var o=e.getModel(["axisLine","lineStyle"]),s=t.getAngleAxis(),l=Math.PI/180,u=s.getExtent(),v=Fr(t),c=v?0:1,h,f=Math.abs(u[1]-u[0])===360?"Circle":"Arc";i[c]===0?h=new wr[f]({shape:{cx:t.cx,cy:t.cy,r:i[v],startAngle:-u[0]*l,endAngle:-u[1]*l,clockwise:s.inverse},style:o.getLineStyle(),z2:1,silent:!0}):h=new js({shape:{cx:t.cx,cy:t.cy,r:i[v],r0:i[c]},style:o.getLineStyle(),z2:1,silent:!0}),h.style.fill=null,r.add(h)},axisTick:function(r,e,t,a,n,i){var o=e.getModel("axisTick"),s=(o.get("inside")?-1:1)*o.get("length"),l=i[Fr(t)],u=F(a,function(v){return new re({shape:Br(t,[l,l+s],v.coord)})});r.add(Et(u,{style:j(o.getModel("lineStyle").getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])})}))},minorTick:function(r,e,t,a,n,i){if(n.length){for(var o=e.getModel("axisTick"),s=e.getModel("minorTick"),l=(o.get("inside")?-1:1)*s.get("length"),u=i[Fr(t)],v=[],c=0;c<n.length;c++)for(var h=0;h<n[c].length;h++)v.push(new re({shape:Br(t,[u,u+l],n[c][h].coord)}));r.add(Et(v,{style:j(s.getModel("lineStyle").getLineStyle(),j(o.getLineStyle(),{stroke:e.get(["axisLine","lineStyle","color"])}))}))}},axisLabel:function(r,e,t,a,n,i,o){var s=e.getCategories(!0),l=e.getModel("axisLabel"),u=l.get("margin"),v=e.get("triggerEvent");M(o,function(c,h){var f=l,p=c.tickValue,d=i[Fr(t)],g=t.coordToPoint([d+u,c.coord]),S=t.cx,m=t.cy,y=Math.abs(g[0]-S)/d<.3?"center":g[0]>S?"left":"right",w=Math.abs(g[1]-m)/d<.3?"middle":g[1]>m?"top":"bottom";if(s&&s[p]){var x=s[p];Rn(x)&&x.textStyle&&(f=new Lt(x.textStyle,l,l.ecModel))}var b=new Yt({silent:Ce.isLabelSilent(e),style:Bt(f,{x:g[0],y:g[1],fill:f.getTextColor()||e.get(["axisLine","lineStyle","color"]),text:c.formattedLabel,align:y,verticalAlign:w})});if(r.add(b),v){var _=Ce.makeAxisEventDataBase(e);_.targetType="axisLabel",_.value=c.rawLabel,ft(b).eventData=_}},this)},splitLine:function(r,e,t,a,n,i){var o=e.getModel("splitLine"),s=o.getModel("lineStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var v=[],c=0;c<a.length;c++){var h=u++%l.length;v[h]=v[h]||[],v[h].push(new re({shape:Br(t,i,a[c].coord)}))}for(var c=0;c<v.length;c++)r.add(Et(v[c],{style:j({stroke:l[c%l.length]},s.getLineStyle()),silent:!0,z:e.get("z")}))},minorSplitLine:function(r,e,t,a,n,i){if(n.length){for(var o=e.getModel("minorSplitLine"),s=o.getModel("lineStyle"),l=[],u=0;u<n.length;u++)for(var v=0;v<n[u].length;v++)l.push(new re({shape:Br(t,i,n[u][v].coord)}));r.add(Et(l,{style:s.getLineStyle(),silent:!0,z:e.get("z")}))}},splitArea:function(r,e,t,a,n,i){if(a.length){var o=e.getModel("splitArea"),s=o.getModel("areaStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var v=[],c=Math.PI/180,h=-a[0].coord*c,f=Math.min(i[0],i[1]),p=Math.max(i[0],i[1]),d=e.get("clockwise"),g=1,S=a.length;g<=S;g++){var m=g===S?a[0].coord:a[g].coord,y=u++%l.length;v[y]=v[y]||[],v[y].push(new Me({shape:{cx:t.cx,cy:t.cy,r0:f,r:p,startAngle:h,endAngle:-m*c,clockwise:d},silent:!0})),h=-m*c}for(var g=0;g<v.length;g++)r.add(Et(v[g],{style:j({fill:l[g%l.length]},s.getAreaStyle()),silent:!0}))}}},fm=["axisLine","axisTickLabel","axisName"],pm=["splitLine","splitArea","minorSplitLine"],dm=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="PolarAxisPointer",t}return e.prototype.render=function(t,a){if(this.group.removeAll(),!!t.get("show")){var n=this._axisGroup,i=this._axisGroup=new X;this.group.add(i);var o=t.axis,s=o.polar,l=s.getAngleAxis(),u=o.getTicksCoords(),v=o.getMinorTicksCoords(),c=l.getExtent()[0],h=o.getExtent(),f=ym(s,t,c),p=new Ce(t,f);M(fm,p.add,p),i.add(p.getGroup()),Nn(n,i,t),M(pm,function(d){t.get([d,"show"])&&!o.scale.isBlank()&&gm[d](this.group,t,s,c,h,u,v)},this)}},e.type="radiusAxis",e}(Dr),gm={splitLine:function(r,e,t,a,n,i){var o=e.getModel("splitLine"),s=o.getModel("lineStyle"),l=s.get("color"),u=0,v=t.getAngleAxis(),c=Math.PI/180,h=v.getExtent(),f=Math.abs(h[1]-h[0])===360?"Circle":"Arc";l=l instanceof Array?l:[l];for(var p=[],d=0;d<i.length;d++){var g=u++%l.length;p[g]=p[g]||[],p[g].push(new wr[f]({shape:{cx:t.cx,cy:t.cy,r:Math.max(i[d].coord,0),startAngle:-h[0]*c,endAngle:-h[1]*c,clockwise:v.inverse}}))}for(var d=0;d<p.length;d++)r.add(Et(p[d],{style:j({stroke:l[d%l.length],fill:null},s.getLineStyle()),silent:!0}))},minorSplitLine:function(r,e,t,a,n,i,o){if(o.length){for(var s=e.getModel("minorSplitLine"),l=s.getModel("lineStyle"),u=[],v=0;v<o.length;v++)for(var c=0;c<o[v].length;c++)u.push(new _r({shape:{cx:t.cx,cy:t.cy,r:o[v][c].coord}}));r.add(Et(u,{style:j({fill:null},l.getLineStyle()),silent:!0}))}},splitArea:function(r,e,t,a,n,i){if(i.length){var o=e.getModel("splitArea"),s=o.getModel("areaStyle"),l=s.get("color"),u=0;l=l instanceof Array?l:[l];for(var v=[],c=i[0].coord,h=1;h<i.length;h++){var f=u++%l.length;v[f]=v[f]||[],v[f].push(new Me({shape:{cx:t.cx,cy:t.cy,r0:c,r:i[h].coord,startAngle:0,endAngle:Math.PI*2},silent:!0})),c=i[h].coord}for(var h=0;h<v.length;h++)r.add(Et(v[h],{style:j({fill:l[h%l.length]},s.getAreaStyle()),silent:!0}))}}};function ym(r,e,t){return{position:[r.cx,r.cy],rotation:t/180*Math.PI,labelDirection:-1,tickDirection:-1,nameDirection:1,labelRotate:e.getModel("axisLabel").get("rotate"),z2:1}}function Gu(r){return r.get("stack")||"__ec_stack_"+r.seriesIndex}function ku(r,e){return e.dim+r.model.componentIndex}function mm(r,e,t){var a={},n=Sm(Rt(e.getSeriesByType(r),function(i){return!e.isSeriesFiltered(i)&&i.coordinateSystem&&i.coordinateSystem.type==="polar"}));e.eachSeriesByType(r,function(i){if(i.coordinateSystem.type==="polar"){var o=i.getData(),s=i.coordinateSystem,l=s.getBaseAxis(),u=ku(s,l),v=Gu(i),c=n[u][v],h=c.offset,f=c.width,p=s.getOtherAxis(l),d=i.coordinateSystem.cx,g=i.coordinateSystem.cy,S=i.get("barMinHeight")||0,m=i.get("barMinAngle")||0;a[v]=a[v]||[];for(var y=o.mapDimension(p.dim),w=o.mapDimension(l.dim),x=gc(o,y),b=l.dim!=="radius"||!i.get("roundCap",!0),_=p.model,T=_.get("startValue"),I=p.dataToCoord(T||0),A=0,D=o.count();A<D;A++){var E=o.get(y,A),P=o.get(w,A),C=E>=0?"p":"n",L=I;x&&(a[v][P]||(a[v][P]={p:I,n:I}),L=a[v][P][C]);var R=void 0,V=void 0,N=void 0,k=void 0;if(p.dim==="radius"){var O=p.dataToCoord(E)-I,H=l.dataToCoord(P);Math.abs(O)<S&&(O=(O<0?-1:1)*S),R=L,V=L+O,N=H-h,k=N-f,x&&(a[v][P][C]=V)}else{var Y=p.dataToCoord(E,b)-I,tt=l.dataToCoord(P);Math.abs(Y)<m&&(Y=(Y<0?-1:1)*m),R=tt+h,V=R+f,N=L,k=L+Y,x&&(a[v][P][C]=k)}o.setItemLayout(A,{cx:d,cy:g,r0:R,r:V,startAngle:-N*Math.PI/180,endAngle:-k*Math.PI/180,clockwise:N>=k})}}})}function Sm(r){var e={};M(r,function(a,n){var i=a.getData(),o=a.coordinateSystem,s=o.getBaseAxis(),l=ku(o,s),u=s.getExtent(),v=s.type==="category"?s.getBandWidth():Math.abs(u[1]-u[0])/i.count(),c=e[l]||{bandWidth:v,remainedWidth:v,autoWidthCount:0,categoryGap:"20%",gap:"30%",stacks:{}},h=c.stacks;e[l]=c;var f=Gu(a);h[f]||c.autoWidthCount++,h[f]=h[f]||{width:0,maxWidth:0};var p=z(a.get("barWidth"),v),d=z(a.get("barMaxWidth"),v),g=a.get("barGap"),S=a.get("barCategoryGap");p&&!h[f].width&&(p=Math.min(c.remainedWidth,p),h[f].width=p,c.remainedWidth-=p),d&&(h[f].maxWidth=d),g!=null&&(c.gap=g),S!=null&&(c.categoryGap=S)});var t={};return M(e,function(a,n){t[n]={};var i=a.stacks,o=a.bandWidth,s=z(a.categoryGap,o),l=z(a.gap,1),u=a.remainedWidth,v=a.autoWidthCount,c=(u-s)/(v+(v-1)*l);c=Math.max(c,0),M(i,function(d,g){var S=d.maxWidth;S&&S<c&&(S=Math.min(S,u),d.width&&(S=Math.min(S,d.width)),u-=S,d.width=S,v--)}),c=(u-s)/(v+(v-1)*l),c=Math.max(c,0);var h=0,f;M(i,function(d,g){d.width||(d.width=c),f=d,h+=d.width*(1+l)}),f&&(h-=f.width*l);var p=-h/2;M(i,function(d,g){t[n][g]=t[n][g]||{offset:p,width:d.width},p+=d.width*(1+l)})}),t}var xm={startAngle:90,clockwise:!0,splitNumber:12,axisLabel:{rotate:0}},bm={splitNumber:5},wm=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.type="polar",e}(Ne);function _m(r){$(Gn),Dr.registerAxisPointerClass("PolarAxisPointer",Jy),r.registerCoordinateSystem("polar",um),r.registerComponentModel(em),r.registerComponentView(wm),qr(r,"angle",rm,xm),qr(r,"radius",am,bm),r.registerComponentView(cm),r.registerComponentView(dm),r.registerLayout(bt(mm,"bar"))}function xn(r,e){e=e||{};var t=r.coordinateSystem,a=r.axis,n={},i=a.position,o=a.orient,s=t.getRect(),l=[s.x,s.x+s.width,s.y,s.y+s.height],u={horizontal:{top:l[2],bottom:l[3]},vertical:{left:l[0],right:l[1]}};n.position=[o==="vertical"?u.vertical[i]:l[0],o==="horizontal"?u.horizontal[i]:l[3]];var v={horizontal:0,vertical:1};n.rotation=Math.PI/2*v[o];var c={top:-1,bottom:1,right:1,left:-1};n.labelDirection=n.tickDirection=n.nameDirection=c[i],r.get(["axisTick","inside"])&&(n.tickDirection=-n.tickDirection),Le(e.labelInside,r.get(["axisLabel","inside"]))&&(n.labelDirection=-n.labelDirection);var h=e.rotate;return h==null&&(h=r.get(["axisLabel","rotate"])),n.labelRotation=i==="top"?-h:h,n.z2=1,n}var Am=["axisLine","axisTickLabel","axisName"],Tm=["splitArea","splitLine"],Im=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.axisPointerClass="SingleAxisPointer",t}return e.prototype.render=function(t,a,n,i){var o=this.group;o.removeAll();var s=this._axisGroup;this._axisGroup=new X;var l=xn(t),u=new Ce(t,l);M(Am,u.add,u),o.add(this._axisGroup),o.add(u.getGroup()),M(Tm,function(v){t.get([v,"show"])&&Dm[v](this,this.group,this._axisGroup,t)},this),Nn(s,this._axisGroup,t),r.prototype.render.call(this,t,a,n,i)},e.prototype.remove=function(){sh(this)},e.type="singleAxis",e}(Dr),Dm={splitLine:function(r,e,t,a){var n=a.axis;if(!n.scale.isBlank()){var i=a.getModel("splitLine"),o=i.getModel("lineStyle"),s=o.get("color");s=s instanceof Array?s:[s];for(var l=o.get("width"),u=a.coordinateSystem.getRect(),v=n.isHorizontal(),c=[],h=0,f=n.getTicksCoords({tickModel:i}),p=[],d=[],g=0;g<f.length;++g){var S=n.toGlobalCoord(f[g].coord);v?(p[0]=S,p[1]=u.y,d[0]=S,d[1]=u.y+u.height):(p[0]=u.x,p[1]=S,d[0]=u.x+u.width,d[1]=S);var m=new re({shape:{x1:p[0],y1:p[1],x2:d[0],y2:d[1]},silent:!0});yc(m.shape,l);var y=h++%s.length;c[y]=c[y]||[],c[y].push(m)}for(var w=o.getLineStyle(["color"]),g=0;g<c.length;++g)e.add(Et(c[g],{style:j({stroke:s[g%s.length]},w),silent:!0}))}},splitArea:function(r,e,t,a){lh(r,t,a,a)}},Wr=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.getCoordSysModel=function(){return this},e.type="singleAxis",e.layoutMode="box",e.defaultOption={left:"5%",top:"5%",right:"5%",bottom:"5%",type:"value",position:"bottom",orient:"horizontal",axisLine:{show:!0,lineStyle:{width:1,type:"solid"}},tooltip:{show:!0},axisTick:{show:!0,length:6,lineStyle:{width:1}},axisLabel:{show:!0,interval:"auto"},splitLine:{show:!0,lineStyle:{type:"dashed",opacity:.2}}},e}(he);Ve(Wr,ha.prototype);var Cm=function(r){G(e,r);function e(t,a,n,i,o){var s=r.call(this,t,a,n)||this;return s.type=i||"value",s.position=o||"bottom",s}return e.prototype.isHorizontal=function(){var t=this.position;return t==="top"||t==="bottom"},e.prototype.pointToData=function(t,a){return this.coordinateSystem.pointToData(t)[0]},e}(Jt),zu=["single"],Lm=function(){function r(e,t,a){this.type="single",this.dimension="single",this.dimensions=zu,this.axisPointerEnabled=!0,this.model=e,this._init(e,t,a)}return r.prototype._init=function(e,t,a){var n=this.dimension,i=new Cm(n,Vn(e),[0,0],e.get("type"),e.get("position")),o=i.type==="category";i.onBand=o&&e.get("boundaryGap"),i.inverse=e.get("inverse"),i.orient=e.get("orient"),e.axis=i,i.model=e,i.coordinateSystem=this,this._axis=i},r.prototype.update=function(e,t){e.eachSeries(function(a){if(a.coordinateSystem===this){var n=a.getData();M(n.mapDimensionsAll(this.dimension),function(i){this._axis.scale.unionExtentFromData(n,i)},this),Zr(this._axis.scale,this._axis.model)}},this)},r.prototype.resize=function(e,t){this._rect=pe({left:e.get("left"),top:e.get("top"),right:e.get("right"),bottom:e.get("bottom"),width:e.get("width"),height:e.get("height")},{width:t.getWidth(),height:t.getHeight()}),this._adjustAxis()},r.prototype.getRect=function(){return this._rect},r.prototype._adjustAxis=function(){var e=this._rect,t=this._axis,a=t.isHorizontal(),n=a?[0,e.width]:[0,e.height],i=t.inverse?1:0;t.setExtent(n[i],n[1-i]),this._updateAxisTransform(t,a?e.x:e.y)},r.prototype._updateAxisTransform=function(e,t){var a=e.getExtent(),n=a[0]+a[1],i=e.isHorizontal();e.toGlobalCoord=i?function(o){return o+t}:function(o){return n-o+t},e.toLocalCoord=i?function(o){return o-t}:function(o){return n-o+t}},r.prototype.getAxis=function(){return this._axis},r.prototype.getBaseAxis=function(){return this._axis},r.prototype.getAxes=function(){return[this._axis]},r.prototype.getTooltipAxes=function(){return{baseAxes:[this.getAxis()],otherAxes:[]}},r.prototype.containPoint=function(e){var t=this.getRect(),a=this.getAxis(),n=a.orient;return n==="horizontal"?a.contain(a.toLocalCoord(e[0]))&&e[1]>=t.y&&e[1]<=t.y+t.height:a.contain(a.toLocalCoord(e[1]))&&e[0]>=t.y&&e[0]<=t.y+t.height},r.prototype.pointToData=function(e){var t=this.getAxis();return[t.coordToData(t.toLocalCoord(e[t.orient==="horizontal"?0:1]))]},r.prototype.dataToPoint=function(e){var t=this.getAxis(),a=this.getRect(),n=[],i=t.orient==="horizontal"?0:1;return e instanceof Array&&(e=e[0]),n[i]=t.toGlobalCoord(t.dataToCoord(+e)),n[1-i]=i===0?a.y+a.height/2:a.x+a.width/2,n},r.prototype.convertToPixel=function(e,t,a){var n=ns(t);return n===this?this.dataToPoint(a):null},r.prototype.convertFromPixel=function(e,t,a){var n=ns(t);return n===this?this.pointToData(a):null},r}();function ns(r){var e=r.seriesModel,t=r.singleAxisModel;return t&&t.coordinateSystem||e&&e.coordinateSystem}function Pm(r,e){var t=[];return r.eachComponent("singleAxis",function(a,n){var i=new Lm(a,r,e);i.name="single_"+n,i.resize(a,e),a.coordinateSystem=i,t.push(i)}),r.eachSeries(function(a){if(a.get("coordinateSystem")==="singleAxis"){var n=a.getReferringComponents("singleAxis",Ke).models[0];a.coordinateSystem=n&&n.coordinateSystem}}),t}var Mm={create:Pm,dimensions:zu},is=["x","y"],Em=["width","height"],Rm=function(r){G(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.makeElOption=function(t,a,n,i,o){var s=n.axis,l=s.coordinateSystem,u=qa(l,1-ra(s)),v=l.dataToPoint(a)[0],c=i.get("type");if(c&&c!=="none"){var h=gl(i),f=Vm[c](s,v,u);f.style=h,t.graphicKey=f.type,t.pointer=f}var p=xn(n);mc(a,t,p,n,i,o)},e.prototype.getHandleTransform=function(t,a,n){var i=xn(a,{labelInside:!1});i.labelMargin=n.get(["handle","margin"]);var o=Sc(a.axis,t,i);return{x:o[0],y:o[1],rotation:i.rotation+(i.labelDirection<0?Math.PI:0)}},e.prototype.updateHandleTransform=function(t,a,n,i){var o=n.axis,s=o.coordinateSystem,l=ra(o),u=qa(s,l),v=[t.x,t.y];v[l]+=a[l],v[l]=Math.min(u[1],v[l]),v[l]=Math.max(u[0],v[l]);var c=qa(s,1-l),h=(c[1]+c[0])/2,f=[h,h];return f[l]=v[l],{x:v[0],y:v[1],rotation:t.rotation,cursorPoint:f,tooltipOption:{verticalAlign:"middle"}}},e}(yl),Vm={line:function(r,e,t){var a=ml([e,t[0]],[e,t[1]],ra(r));return{type:"Line",subPixelOptimize:!0,shape:a}},shadow:function(r,e,t){var a=r.getBandWidth(),n=t[1]-t[0];return{type:"Rect",shape:xc([e-a/2,t[0]],[a,n],ra(r))}}};function ra(r){return r.isHorizontal()?0:1}function qa(r,e){var t=r.getRect();return[t[is[e]],t[is[e]]+t[Em[e]]]}var Nm=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.type="single",e}(Ne);function Gm(r){$(Gn),Dr.registerAxisPointerClass("SingleAxisPointer",Rm),r.registerComponentView(Nm),r.registerComponentView(Im),r.registerComponentModel(Wr),qr(r,"single",Wr,Wr.defaultOption),r.registerCoordinateSystem("single",Mm)}var km=["rect","polygon","keep","clear"];function zm(r,e){var t=Ft(r?r.brush:[]);if(t.length){var a=[];M(t,function(l){var u=l.hasOwnProperty("toolbox")?l.toolbox:[];u instanceof Array&&(a=a.concat(u))});var n=r&&r.toolbox;q(n)&&(n=n[0]),n||(n={feature:{}},r.toolbox=[n]);var i=n.feature||(n.feature={}),o=i.brush||(i.brush={}),s=o.type||(o.type=[]);s.push.apply(s,a),Om(s),e&&!s.length&&s.push.apply(s,km)}}function Om(r){var e={};M(r,function(t){e[t]=1}),r.length=0,M(e,function(t,a){r.push(a)})}function Bm(r){var e=r.brushType,t={point:function(a){return os[e].point(a,t,r)},rect:function(a){return os[e].rect(a,t,r)}};return t}var os={lineX:ss(0),lineY:ss(1),rect:{point:function(r,e,t){return r&&t.boundingRect.contain(r[0],r[1])},rect:function(r,e,t){return r&&t.boundingRect.intersect(r)}},polygon:{point:function(r,e,t){return r&&t.boundingRect.contain(r[0],r[1])&&Te(t.range,r[0],r[1])},rect:function(r,e,t){var a=t.range;if(!r||a.length<=1)return!1;var n=r.x,i=r.y,o=r.width,s=r.height,l=a[0];if(Te(a,n,i)||Te(a,n+o,i)||Te(a,n,i+s)||Te(a,n+o,i+s)||ct.create(r).contain(l[0],l[1])||Rr(n,i,n+o,i,a)||Rr(n,i,n,i+s,a)||Rr(n+o,i,n+o,i+s,a)||Rr(n,i+s,n+o,i+s,a))return!0}}};function ss(r){var e=["x","y"],t=["width","height"];return{point:function(a,n,i){if(a){var o=i.range,s=a[r];return nr(s,o)}},rect:function(a,n,i){if(a){var o=i.range,s=[a[e[r]],a[e[r]]+a[t[r]]];return s[1]<s[0]&&s.reverse(),nr(s[0],o)||nr(s[1],o)||nr(o[0],s)||nr(o[1],s)}}}}function nr(r,e){return e[0]<=r&&r<=e[1]}var ls=["inBrush","outOfBrush"],ja="__ecBrushSelect",bn="__ecInBrushSelectEvent";function Ou(r){r.eachComponent({mainType:"brush"},function(e){var t=e.brushTargetManager=new ch(e.option,r);t.setInputRanges(e.areas,r)})}function Fm(r,e,t){var a=[],n,i;r.eachComponent({mainType:"brush"},function(o){t&&t.type==="takeGlobalCursor"&&o.setBrushOption(t.key==="brush"?t.brushOption:{brushType:!1})}),Ou(r),r.eachComponent({mainType:"brush"},function(o,s){var l={brushId:o.id,brushIndex:s,brushName:o.name,areas:Ut(o.areas),selected:[]};a.push(l);var u=o.option,v=u.brushLink,c=[],h=[],f=[],p=!1;s||(n=u.throttleType,i=u.throttleDelay);var d=F(o.areas,function(x){var b=$m[x.brushType],_=j({boundingRect:b?b(x):void 0},x);return _.selectors=Bm(_),_}),g=uh(o.option,ls,function(x){x.mappingMethod="fixed"});q(v)&&M(v,function(x){c[x]=1});function S(x){return v==="all"||!!c[x]}function m(x){return!!x.length}r.eachSeries(function(x,b){var _=f[b]=[];x.subType==="parallel"?y(x,b):w(x,b,_)});function y(x,b){var _=x.coordinateSystem;p=p||_.hasAxisBrushed(),S(b)&&_.eachActiveState(x.getData(),function(T,I){T==="active"&&(h[I]=1)})}function w(x,b,_){if(!(!x.brushSelector||Um(o,b))&&(M(d,function(I){o.brushTargetManager.controlSeries(I,x,r)&&_.push(I),p=p||m(_)}),S(b)&&m(_))){var T=x.getData();T.each(function(I){us(x,_,T,I)&&(h[I]=1)})}}r.eachSeries(function(x,b){var _={seriesId:x.id,seriesIndex:b,seriesName:x.name,dataIndex:[]};l.selected.push(_);var T=f[b],I=x.getData(),A=S(b)?function(D){return h[D]?(_.dataIndex.push(I.getRawIndex(D)),"inBrush"):"outOfBrush"}:function(D){return us(x,T,I,D)?(_.dataIndex.push(I.getRawIndex(D)),"inBrush"):"outOfBrush"};(S(b)?p:m(T))&&vh(ls,g,I,A)})}),Hm(e,n,i,a,t)}function Hm(r,e,t,a,n){if(n){var i=r.getZr();if(!i[bn]){i[ja]||(i[ja]=Wm);var o=vl(i,ja,t,e);o(r,a)}}}function Wm(r,e){if(!r.isDisposed()){var t=r.getZr();t[bn]=!0,r.dispatchAction({type:"brushSelect",batch:e}),t[bn]=!1}}function us(r,e,t,a){for(var n=0,i=e.length;n<i;n++){var o=e[n];if(r.brushSelector(a,t,o.selectors,o))return!0}}function Um(r,e){var t=r.option.seriesIndex;return t!=null&&t!=="all"&&(q(t)?Ge(t,e)<0:e!==t)}var $m={rect:function(r){return vs(r.range)},polygon:function(r){for(var e,t=r.range,a=0,n=t.length;a<n;a++){e=e||[[1/0,-1/0],[1/0,-1/0]];var i=t[a];i[0]<e[0][0]&&(e[0][0]=i[0]),i[0]>e[0][1]&&(e[0][1]=i[0]),i[1]<e[1][0]&&(e[1][0]=i[1]),i[1]>e[1][1]&&(e[1][1]=i[1])}return e&&vs(e)}};function vs(r){return new ct(r[0][0],r[1][0],r[0][1]-r[0][0],r[1][1]-r[1][0])}var Ym=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.init=function(t,a){this.ecModel=t,this.api=a,this.model,(this._brushController=new Ml(a.getZr())).on("brush",it(this._onBrush,this)).mount()},e.prototype.render=function(t,a,n,i){this.model=t,this._updateController(t,a,n,i)},e.prototype.updateTransform=function(t,a,n,i){Ou(a),this._updateController(t,a,n,i)},e.prototype.updateVisual=function(t,a,n,i){this.updateTransform(t,a,n,i)},e.prototype.updateView=function(t,a,n,i){this._updateController(t,a,n,i)},e.prototype._updateController=function(t,a,n,i){(!i||i.$from!==t.id)&&this._brushController.setPanels(t.brushTargetManager.makePanelOpts(n)).enableBrush(t.brushOption).updateCovers(t.areas.slice())},e.prototype.dispose=function(){this._brushController.dispose()},e.prototype._onBrush=function(t){var a=this.model.id,n=this.model.brushTargetManager.setOutputRanges(t.areas,this.ecModel);(!t.isEnd||t.removeOnClick)&&this.api.dispatchAction({type:"brush",brushId:a,areas:Ut(n),$from:a}),t.isEnd&&this.api.dispatchAction({type:"brushEnd",brushId:a,areas:Ut(n),$from:a})},e.type="brush",e}(Ne),Zm="#ddd",Xm=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t.areas=[],t.brushOption={},t}return e.prototype.optionUpdated=function(t,a){var n=this.option;!a&&hh(n,t,["inBrush","outOfBrush"]);var i=n.inBrush=n.inBrush||{};n.outOfBrush=n.outOfBrush||{color:Zm},i.hasOwnProperty("liftZ")||(i.liftZ=5)},e.prototype.setAreas=function(t){t&&(this.areas=F(t,function(a){return cs(this.option,a)},this))},e.prototype.setBrushOption=function(t){this.brushOption=cs(this.option,t),this.brushType=this.brushOption.brushType},e.type="brush",e.dependencies=["geo","grid","xAxis","yAxis","parallel","series"],e.defaultOption={seriesIndex:"all",brushType:"rect",brushMode:"single",transformable:!0,brushStyle:{borderWidth:1,color:"rgba(210,219,238,0.3)",borderColor:"#D2DBEE"},throttleType:"fixRate",throttleDelay:0,removeOnClick:!0,z:1e4},e}(he);function cs(r,e){return qt({brushType:r.brushType,brushMode:r.brushMode,transformable:r.transformable,brushStyle:new Lt(r.brushStyle).getItemStyle(),removeOnClick:r.removeOnClick,z:r.z},e,!0)}var qm=["rect","polygon","lineX","lineY","keep","clear"],jm=function(r){G(e,r);function e(){return r!==null&&r.apply(this,arguments)||this}return e.prototype.render=function(t,a,n){var i,o,s;a.eachComponent({mainType:"brush"},function(l){i=l.brushType,o=l.brushOption.brushMode||"single",s=s||!!l.areas.length}),this._brushType=i,this._brushMode=o,M(t.get("type",!0),function(l){t.setIconStatus(l,(l==="keep"?o==="multiple":l==="clear"?s:l===i)?"emphasis":"normal")})},e.prototype.updateView=function(t,a,n){this.render(t,a,n)},e.prototype.getIcons=function(){var t=this.model,a=t.get("icon",!0),n={};return M(t.get("type",!0),function(i){a[i]&&(n[i]=a[i])}),n},e.prototype.onclick=function(t,a,n){var i=this._brushType,o=this._brushMode;n==="clear"?(a.dispatchAction({type:"axisAreaSelect",intervals:[]}),a.dispatchAction({type:"brush",command:"clear",areas:[]})):a.dispatchAction({type:"takeGlobalCursor",key:"brush",brushOption:{brushType:n==="keep"?i:i===n?!1:n,brushMode:n==="keep"?o==="multiple"?"single":"multiple":o}})},e.getDefaultOption=function(t){var a={show:!0,type:qm.slice(),icon:{rect:"M7.3,34.7 M0.4,10V-0.2h9.8 M89.6,10V-0.2h-9.8 M0.4,60v10.2h9.8 M89.6,60v10.2h-9.8 M12.3,22.4V10.5h13.1 M33.6,10.5h7.8 M49.1,10.5h7.8 M77.5,22.4V10.5h-13 M12.3,31.1v8.2 M77.7,31.1v8.2 M12.3,47.6v11.9h13.1 M33.6,59.5h7.6 M49.1,59.5 h7.7 M77.5,47.6v11.9h-13",polygon:"M55.2,34.9c1.7,0,3.1,1.4,3.1,3.1s-1.4,3.1-3.1,3.1 s-3.1-1.4-3.1-3.1S53.5,34.9,55.2,34.9z M50.4,51c1.7,0,3.1,1.4,3.1,3.1c0,1.7-1.4,3.1-3.1,3.1c-1.7,0-3.1-1.4-3.1-3.1 C47.3,52.4,48.7,51,50.4,51z M55.6,37.1l1.5-7.8 M60.1,13.5l1.6-8.7l-7.8,4 M59,19l-1,5.3 M24,16.1l6.4,4.9l6.4-3.3 M48.5,11.6 l-5.9,3.1 M19.1,12.8L9.7,5.1l1.1,7.7 M13.4,29.8l1,7.3l6.6,1.6 M11.6,18.4l1,6.1 M32.8,41.9 M26.6,40.4 M27.3,40.2l6.1,1.6 M49.9,52.1l-5.6-7.6l-4.9-1.2",lineX:"M15.2,30 M19.7,15.6V1.9H29 M34.8,1.9H40.4 M55.3,15.6V1.9H45.9 M19.7,44.4V58.1H29 M34.8,58.1H40.4 M55.3,44.4 V58.1H45.9 M12.5,20.3l-9.4,9.6l9.6,9.8 M3.1,29.9h16.5 M62.5,20.3l9.4,9.6L62.3,39.7 M71.9,29.9H55.4",lineY:"M38.8,7.7 M52.7,12h13.2v9 M65.9,26.6V32 M52.7,46.3h13.2v-9 M24.9,12H11.8v9 M11.8,26.6V32 M24.9,46.3H11.8v-9 M48.2,5.1l-9.3-9l-9.4,9.2 M38.9-3.9V12 M48.2,53.3l-9.3,9l-9.4-9.2 M38.9,62.3V46.4",keep:"M4,10.5V1h10.3 M20.7,1h6.1 M33,1h6.1 M55.4,10.5V1H45.2 M4,17.3v6.6 M55.6,17.3v6.6 M4,30.5V40h10.3 M20.7,40 h6.1 M33,40h6.1 M55.4,30.5V40H45.2 M21,18.9h62.9v48.6H21V18.9z",clear:"M22,14.7l30.9,31 M52.9,14.7L22,45.7 M4.7,16.8V4.2h13.1 M26,4.2h7.8 M41.6,4.2h7.8 M70.3,16.8V4.2H57.2 M4.7,25.9v8.6 M70.3,25.9v8.6 M4.7,43.2v12.6h13.1 M26,55.8h7.8 M41.6,55.8h7.8 M70.3,43.2v12.6H57.2"},title:t.getLocaleModel().get(["toolbox","brush","title"])};return a},e}(fh);function Km(r){r.registerComponentView(Ym),r.registerComponentModel(Xm),r.registerPreprocessor(zm),r.registerVisual(r.PRIORITY.VISUAL.BRUSH,Fm),r.registerAction({type:"brush",event:"brush",update:"updateVisual"},function(e,t){t.eachComponent({mainType:"brush",query:e},function(a){a.setAreas(e.areas)})}),r.registerAction({type:"brushSelect",event:"brushSelected",update:"none"},qe),r.registerAction({type:"brushEnd",event:"brushEnd",update:"none"},qe),ph("brush",jm)}var Jm=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.createMarkerModelFromSeries=function(t,a,n){return new e(t,a,n)},e.type="markPoint",e.defaultOption={z:5,symbol:"pin",symbolSize:50,tooltip:{trigger:"item"},label:{show:!0,position:"inside"},itemStyle:{borderWidth:2},emphasis:{label:{show:!0}}},e}(Vl);function hs(r,e,t){var a=e.coordinateSystem;r.each(function(n){var i=r.getItemModel(n),o,s=z(i.get("x"),t.getWidth()),l=z(i.get("y"),t.getHeight());if(!isNaN(s)&&!isNaN(l))o=[s,l];else if(e.getMarkerPosition)o=e.getMarkerPosition(r.getValues(r.dimensions,n));else if(a){var u=r.get(a.dimensions[0],n),v=r.get(a.dimensions[1],n);o=a.dataToPoint([u,v])}isNaN(s)||(o[0]=s),isNaN(l)||(o[1]=l),r.setItemLayout(n,o)})}var Qm=function(r){G(e,r);function e(){var t=r!==null&&r.apply(this,arguments)||this;return t.type=e.type,t}return e.prototype.updateTransform=function(t,a,n){a.eachSeries(function(i){var o=Vl.getMarkerModelFromSeries(i,"markPoint");o&&(hs(o.getData(),i,n),this.markerGroupMap.get(i.id).updateLayout())},this)},e.prototype.renderSeries=function(t,a,n,i){var o=t.coordinateSystem,s=t.id,l=t.getData(),u=this.markerGroupMap,v=u.get(s)||u.set(s,new Fn),c=t0(o,t,a);a.setData(c),hs(a.getData(),t,i),c.each(function(h){var f=c.getItemModel(h),p=f.getShallow("symbol"),d=f.getShallow("symbolSize"),g=f.getShallow("symbolRotate"),S=f.getShallow("symbolOffset"),m=f.getShallow("symbolKeepAspect");if(st(p)||st(d)||st(g)||st(S)){var y=a.getRawValue(h),w=a.getDataParams(h);st(p)&&(p=p(y,w)),st(d)&&(d=d(y,w)),st(g)&&(g=g(y,w)),st(S)&&(S=S(y,w))}var x=f.getModel("itemStyle").getItemStyle(),b=bc(l,"color");x.fill||(x.fill=b),c.setItemVisual(h,{symbol:p,symbolSize:d,symbolRotate:g,symbolOffset:S,symbolKeepAspect:m,style:x})}),v.updateData(c),this.group.add(v.group),c.eachItemGraphicEl(function(h){h.traverse(function(f){ft(f).dataModel=a})}),this.markKeep(v),v.group.silent=a.get("silent")||t.get("silent")},e.type="markPoint",e}(dh);function t0(r,e,t){var a;r?a=F(r&&r.dimensions,function(s){var l=e.getData().getDimensionInfo(e.getData().mapDimension(s))||{};return W(W({},l),{name:s,ordinalMeta:null})}):a=[{name:"value",type:"float"}];var n=new Pe(a,t),i=F(t.get("data"),bt(gh,e));r&&(i=Rt(i,bt(yh,r)));var o=mh(!!r,a);return n.initData(i,null,o),n}function e0(r){r.registerComponentModel(Jm),r.registerComponentView(Qm),r.registerPreprocessor(function(e){Sh(e.series,"markPoint")&&(e.markPoint=e.markPoint||{})})}var r0={label:{enabled:!0},decal:{show:!1}},fs=fe(),a0={};function n0(r,e){var t=r.getModel("aria");if(!t.get("enabled"))return;var a=Ut(r0);qt(a.label,r.getLocaleModel().get("aria"),!1),qt(t.option,a,!1),n(),i();function n(){var u=t.getModel("decal"),v=u.get("show");if(v){var c=J();r.eachSeries(function(h){if(!h.isColorBySeries()){var f=c.get(h.type);f||(f={},c.set(h.type,f)),fs(h).scope=f}}),r.eachRawSeries(function(h){if(r.isSeriesFiltered(h))return;if(st(h.enableAriaDecal)){h.enableAriaDecal();return}var f=h.getData();if(h.isColorBySeries()){var m=on(h.ecModel,h.name,a0,r.getSeriesCount()),y=f.getVisual("decal");f.setVisual("decal",w(y,m))}else{var p=h.getRawData(),d={},g=fs(h).scope;f.each(function(x){var b=f.getRawIndex(x);d[b]=x});var S=p.count();p.each(function(x){var b=d[x],_=p.getName(x)||x+"",T=on(h.ecModel,_,g,S),I=f.getItemVisual(b,"decal");f.setItemVisual(b,"decal",w(I,T))})}function w(x,b){var _=x?W(W({},b),x):b;return _.dirty=!0,_}})}}function i(){var u=e.getZr().dom;if(u){var v=r.getLocaleModel().get("aria"),c=t.getModel("label");if(c.option=j(c.option,v),!!c.get("enabled")){if(u.setAttribute("role","img"),c.get("description")){u.setAttribute("aria-label",c.get("description"));return}var h=r.getSeriesCount(),f=c.get(["data","maxCount"])||10,p=c.get(["series","maxCount"])||10,d=Math.min(h,p),g;if(!(h<1)){var S=s();if(S){var m=c.get(["general","withTitle"]);g=o(m,{title:S})}else g=c.get(["general","withoutTitle"]);var y=[],w=h>1?c.get(["series","multiple","prefix"]):c.get(["series","single","prefix"]);g+=o(w,{seriesCount:h}),r.eachSeries(function(T,I){if(I<d){var A=void 0,D=T.get("name"),E=D?"withName":"withoutName";A=h>1?c.get(["series","multiple",E]):c.get(["series","single",E]),A=o(A,{seriesId:T.seriesIndex,seriesName:T.get("name"),seriesType:l(T.subType)});var P=T.getData();if(P.count()>f){var C=c.get(["data","partialData"]);A+=o(C,{displayCnt:f})}else A+=c.get(["data","allData"]);for(var L=c.get(["data","separator","middle"]),R=c.get(["data","separator","end"]),V=c.get(["data","excludeDimensionId"]),N=[],k=0;k<P.count();k++)if(k<f){var O=P.getName(k),H=V?Rt(P.getValues(k),function(tt,K){return Ge(V,K)===-1}):P.getValues(k),Y=c.get(["data",O?"withName":"withoutName"]);N.push(o(Y,{name:O,value:H.join(L)}))}A+=N.join(L)+R,y.push(A)}});var x=c.getModel(["series","multiple","separator"]),b=x.get("middle"),_=x.get("end");g+=y.join(b)+_,u.setAttribute("aria-label",g)}}}}function o(u,v){if(!ot(u))return u;var c=u;return M(v,function(h,f){c=c.replace(new RegExp("\\{\\s*"+f+"\\s*\\}","g"),h)}),c}function s(){var u=r.get("title");return u&&u.length&&(u=u[0]),u&&u.text}function l(u){var v=r.getLocaleModel().get(["series","typeNames"]);return v[u]||v.chart}}function i0(r){if(!(!r||!r.aria)){var e=r.aria;e.show!=null&&(e.enabled=e.show),e.label=e.label||{},M(["description","general","series","data"],function(t){e[t]!=null&&(e.label[t]=e[t])})}}function o0(r){r.registerPreprocessor(i0),r.registerVisual(r.PRIORITY.VISUAL.ARIA,n0)}var Ka=Math.sin,Ja=Math.cos,Bu=Math.PI,me=Math.PI*2,s0=180/Bu,Fu=function(){function r(){}return r.prototype.reset=function(e){this._start=!0,this._d=[],this._str="",this._p=Math.pow(10,e||4)},r.prototype.moveTo=function(e,t){this._add("M",e,t)},r.prototype.lineTo=function(e,t){this._add("L",e,t)},r.prototype.bezierCurveTo=function(e,t,a,n,i,o){this._add("C",e,t,a,n,i,o)},r.prototype.quadraticCurveTo=function(e,t,a,n){this._add("Q",e,t,a,n)},r.prototype.arc=function(e,t,a,n,i,o){this.ellipse(e,t,a,a,0,n,i,o)},r.prototype.ellipse=function(e,t,a,n,i,o,s,l){var u=s-o,v=!l,c=Math.abs(u),h=ne(c-me)||(v?u>=me:-u>=me),f=u>0?u%me:u%me+me,p=!1;h?p=!0:ne(c)?p=!1:p=f>=Bu==!!v;var d=e+a*Ja(o),g=t+n*Ka(o);this._start&&this._add("M",d,g);var S=Math.round(i*s0);if(h){var m=1/this._p,y=(v?1:-1)*(me-m);this._add("A",a,n,S,1,+v,e+a*Ja(o+y),t+n*Ka(o+y)),m>.01&&this._add("A",a,n,S,0,+v,d,g)}else{var w=e+a*Ja(s),x=t+n*Ka(s);this._add("A",a,n,S,+p,+v,w,x)}},r.prototype.rect=function(e,t,a,n){this._add("M",e,t),this._add("l",a,0),this._add("l",0,n),this._add("l",-a,0),this._add("Z")},r.prototype.closePath=function(){this._d.length>0&&this._add("Z")},r.prototype._add=function(e,t,a,n,i,o,s,l,u){for(var v=[],c=this._p,h=1;h<arguments.length;h++){var f=arguments[h];if(isNaN(f)){this._invalid=!0;return}v.push(Math.round(f*c)/c)}this._d.push(e+v.join(" ")),this._start=e==="Z"},r.prototype.generateStr=function(){this._str=this._invalid?"":this._d.join(""),this._d=[]},r.prototype.getStr=function(){return this._str},r}(),ci="none",l0=Math.round;function u0(r){var e=r.fill;return e!=null&&e!==ci}function v0(r){var e=r.stroke;return e!=null&&e!==ci}var wn=["lineCap","miterLimit","lineJoin"],c0=F(wn,function(r){return"stroke-"+r.toLowerCase()});function h0(r,e,t,a){var n=e.opacity==null?1:e.opacity;if(t instanceof ce){r("opacity",n);return}if(u0(e)){var i=gr(e.fill);r("fill",i.color);var o=e.fillOpacity!=null?e.fillOpacity*i.opacity*n:i.opacity*n;o<1&&r("fill-opacity",o)}else r("fill",ci);if(v0(e)){var s=gr(e.stroke);r("stroke",s.color);var l=e.strokeNoScale?t.getLineScale():1,u=l?(e.lineWidth||0)/l:0,v=e.strokeOpacity!=null?e.strokeOpacity*s.opacity*n:s.opacity*n,c=e.strokeFirst;if(u!==1&&r("stroke-width",u),c&&r("paint-order",c?"stroke":"fill"),v<1&&r("stroke-opacity",v),e.lineDash){var h=wc(t),f=h[0],p=h[1];f&&(p=l0(p||0),r("stroke-dasharray",f.join(",")),(p||a)&&r("stroke-dashoffset",p))}for(var d=0;d<wn.length;d++){var g=wn[d];if(e[g]!==Ri[g]){var S=e[g]||Ri[g];S&&r(c0[d],S)}}}}var Hu="http://www.w3.org/2000/svg",Wu="http://www.w3.org/1999/xlink",f0="http://www.w3.org/2000/xmlns/",p0="http://www.w3.org/XML/1998/namespace",ps="ecmeta_";function Uu(r){return document.createElementNS(Hu,r)}function lt(r,e,t,a,n){return{tag:r,attrs:t||{},children:a,text:n,key:e}}function d0(r,e){var t=[];if(e)for(var a in e){var n=e[a],i=a;n!==!1&&(n!==!0&&n!=null&&(i+='="'+n+'"'),t.push(i))}return"<"+r+" "+t.join(" ")+">"}function g0(r){return"</"+r+">"}function hi(r,e){e=e||{};var t=e.newline?`
`:"";function a(n){var i=n.children,o=n.tag,s=n.attrs,l=n.text;return d0(o,s)+(o!=="style"?_c(l):l||"")+(i?""+t+F(i,function(u){return a(u)}).join(t)+t:"")+g0(o)}return a(r)}function y0(r,e,t){t=t||{};var a=t.newline?`
`:"",n=" {"+a,i=a+"}",o=F(Dt(r),function(l){return l+n+F(Dt(r[l]),function(u){return u+":"+r[l][u]+";"}).join(a)+i}).join(a),s=F(Dt(e),function(l){return"@keyframes "+l+n+F(Dt(e[l]),function(u){return u+n+F(Dt(e[l][u]),function(v){var c=e[l][u][v];return v==="d"&&(c='path("'+c+'")'),v+":"+c+";"}).join(a)+i}).join(a)+i}).join(a);return!o&&!s?"":["<![CDATA[",o,s,"]]>"].join(a)}function _n(r){return{zrId:r,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssStyleCache:{},cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function ds(r,e,t,a){return lt("svg","root",{width:r,height:e,xmlns:Hu,"xmlns:xlink":Wu,version:"1.1",baseProfile:"full",viewBox:a?"0 0 "+r+" "+e:!1},t)}var m0=0;function $u(){return m0++}var gs={cubicIn:"0.32,0,0.67,0",cubicOut:"0.33,1,0.68,1",cubicInOut:"0.65,0,0.35,1",quadraticIn:"0.11,0,0.5,0",quadraticOut:"0.5,1,0.89,1",quadraticInOut:"0.45,0,0.55,1",quarticIn:"0.5,0,0.75,0",quarticOut:"0.25,1,0.5,1",quarticInOut:"0.76,0,0.24,1",quinticIn:"0.64,0,0.78,0",quinticOut:"0.22,1,0.36,1",quinticInOut:"0.83,0,0.17,1",sinusoidalIn:"0.12,0,0.39,0",sinusoidalOut:"0.61,1,0.88,1",sinusoidalInOut:"0.37,0,0.63,1",exponentialIn:"0.7,0,0.84,0",exponentialOut:"0.16,1,0.3,1",exponentialInOut:"0.87,0,0.13,1",circularIn:"0.55,0,1,0.45",circularOut:"0,0.55,0.45,1",circularInOut:"0.85,0,0.15,1"},_e="transform-origin";function S0(r,e,t){var a=W({},r.shape);W(a,e),r.buildPath(t,a);var n=new Fu;return n.reset(xl(r)),t.rebuildPath(n,1),n.generateStr(),n.getStr()}function x0(r,e){var t=e.originX,a=e.originY;(t||a)&&(r[_e]=t+"px "+a+"px")}var b0={fill:"fill",opacity:"opacity",lineWidth:"stroke-width",lineDashOffset:"stroke-dashoffset"};function Yu(r,e){var t=e.zrId+"-ani-"+e.cssAnimIdx++;return e.cssAnims[t]=r,t}function w0(r,e,t){var a=r.shape.paths,n={},i,o;if(M(a,function(l){var u=_n(t.zrId);u.animation=!0,Sa(l,{},u,!0);var v=u.cssAnims,c=u.cssNodes,h=Dt(v),f=h.length;if(f){o=h[f-1];var p=v[o];for(var d in p){var g=p[d];n[d]=n[d]||{d:""},n[d].d+=g.d||""}for(var S in c){var m=c[S].animation;m.indexOf(o)>=0&&(i=m)}}}),!!i){e.d=!1;var s=Yu(n,t);return i.replace(o,s)}}function ys(r){return ot(r)?gs[r]?"cubic-bezier("+gs[r]+")":Ac(r)?r:"":""}function Sa(r,e,t,a){var n=r.animators,i=n.length,o=[];if(r instanceof tl){var s=w0(r,e,t);if(s)o.push(s);else if(!i)return}else if(!i)return;for(var l={},u=0;u<i;u++){var v=n[u],c=[v.getMaxTime()/1e3+"s"],h=ys(v.getClip().easing),f=v.getDelay();h?c.push(h):c.push("linear"),f&&c.push(f/1e3+"s"),v.getLoop()&&c.push("infinite");var p=c.join(" ");l[p]=l[p]||[p,[]],l[p][1].push(v)}function d(m){var y=m[1],w=y.length,x={},b={},_={},T="animation-timing-function";function I(U,B,Q){for(var Z=U.getTracks(),rt=U.getMaxTime(),ut=0;ut<Z.length;ut++){var yt=Z[ut];if(yt.needsAnimate()){var at=yt.keyframes,pt=yt.propName;if(Q&&(pt=Q(pt)),pt)for(var wt=0;wt<at.length;wt++){var Pt=at[wt],de=Math.round(Pt.time/rt*100)+"%",Je=ys(Pt.easing),mi=Pt.rawValue;(ot(mi)||jt(mi))&&(B[de]=B[de]||{},B[de][pt]=Pt.rawValue,Je&&(B[de][T]=Je))}}}}for(var A=0;A<w;A++){var D=y[A],E=D.targetName;E?E==="shape"&&I(D,b):!a&&I(D,x)}for(var P in x){var C={};Tc(C,r),W(C,x[P]);var L=Sl(C),R=x[P][T];_[P]=L?{transform:L}:{},x0(_[P],C),R&&(_[P][T]=R)}var V,N=!0;for(var P in b){_[P]=_[P]||{};var k=!V,R=b[P][T];k&&(V=new bl);var O=V.len();V.reset(),_[P].d=S0(r,b[P],V);var H=V.len();if(!k&&O!==H){N=!1;break}R&&(_[P][T]=R)}if(!N)for(var P in _)delete _[P].d;if(!a)for(var A=0;A<w;A++){var D=y[A],E=D.targetName;E==="style"&&I(D,_,function(Z){return b0[Z]})}for(var Y=Dt(_),tt=!0,K,A=1;A<Y.length;A++){var et=Y[A-1],vt=Y[A];if(_[et][_e]!==_[vt][_e]){tt=!1;break}K=_[et][_e]}if(tt&&K){for(var P in _)_[P][_e]&&delete _[P][_e];e[_e]=K}if(Rt(Y,function(U){return Dt(_[U]).length>0}).length){var kt=Yu(_,t);return kt+" "+m[0]+" both"}}for(var g in l){var s=d(l[g]);s&&o.push(s)}if(o.length){var S=t.zrId+"-cls-"+$u();t.cssNodes["."+S]={animation:o.join(",")},e.class=S}}function _0(r,e,t){if(!r.ignore)if(r.isSilent()){var a={"pointer-events":"none"};ms(a,e,t)}else{var n=r.states.emphasis&&r.states.emphasis.style?r.states.emphasis.style:{},i=n.fill;if(!i){var o=r.style&&r.style.fill,s=r.states.select&&r.states.select.style&&r.states.select.style.fill,l=r.currentStates.indexOf("select")>=0&&s||o;l&&(i=Ic(l))}var u=n.lineWidth;if(u){var v=!n.strokeNoScale&&r.transform?r.transform[0]:1;u=u/v}var a={cursor:"pointer"};i&&(a.fill=i),n.stroke&&(a.stroke=n.stroke),u&&(a["stroke-width"]=u),ms(a,e,t)}}function ms(r,e,t,a){var n=JSON.stringify(r),i=t.cssStyleCache[n];i||(i=t.zrId+"-cls-"+$u(),t.cssStyleCache[n]=i,t.cssNodes["."+i+":hover"]=r),e.class=e.class?e.class+" "+i:i}var xr=Math.round;function Zu(r){return r&&ot(r.src)}function Xu(r){return r&&st(r.toDataURL)}function fi(r,e,t,a){h0(function(n,i){var o=n==="fill"||n==="stroke";o&&wl(i)?ju(e,r,n,a):o&&kn(i)?Ku(t,r,n,a):r[n]=i,o&&a.ssr&&i==="none"&&(r["pointer-events"]="visible")},e,t,!1),P0(t,r,a)}function pi(r,e){var t=Bc(e);t&&(t.each(function(a,n){a!=null&&(r[(ps+n).toLowerCase()]=a+"")}),e.isSilent()&&(r[ps+"silent"]="true"))}function Ss(r){return ne(r[0]-1)&&ne(r[1])&&ne(r[2])&&ne(r[3]-1)}function A0(r){return ne(r[4])&&ne(r[5])}function di(r,e,t){if(e&&!(A0(e)&&Ss(e))){var a=1e4;r.transform=Ss(e)?"translate("+xr(e[4]*a)/a+" "+xr(e[5]*a)/a+")":Oc(e)}}function xs(r,e,t){for(var a=r.points,n=[],i=0;i<a.length;i++)n.push(xr(a[i][0]*t)/t),n.push(xr(a[i][1]*t)/t);e.points=n.join(" ")}function bs(r){return!r.smooth}function T0(r){var e=F(r,function(t){return typeof t=="string"?[t,t]:t});return function(t,a,n){for(var i=0;i<e.length;i++){var o=e[i],s=t[o[0]];s!=null&&(a[o[1]]=xr(s*n)/n)}}}var I0={circle:[T0(["cx","cy","r"])],polyline:[xs,bs],polygon:[xs,bs]};function D0(r){for(var e=r.animators,t=0;t<e.length;t++)if(e[t].targetName==="shape")return!0;return!1}function qu(r,e){var t=r.style,a=r.shape,n=I0[r.type],i={},o=e.animation,s="path",l=r.style.strokePercent,u=e.compress&&xl(r)||4;if(n&&!e.willUpdate&&!(n[1]&&!n[1](a))&&!(o&&D0(r))&&!(l<1)){s=r.type;var v=Math.pow(10,u);n[0](a,i,v)}else{var c=!r.path||r.shapeChanged();r.path||r.createPathProxy();var h=r.path;c&&(h.beginPath(),r.buildPath(h,r.shape),r.pathUpdated());var f=h.getVersion(),p=r,d=p.__svgPathBuilder;(p.__svgPathVersion!==f||!d||l!==p.__svgPathStrokePercent)&&(d||(d=p.__svgPathBuilder=new Fu),d.reset(u),h.rebuildPath(d,l),d.generateStr(),p.__svgPathVersion=f,p.__svgPathStrokePercent=l),i.d=d.getStr()}return di(i,r.transform),fi(i,t,r,e),pi(i,r),e.animation&&Sa(r,i,e),e.emphasis&&_0(r,i,e),lt(s,r.id+"",i)}function C0(r,e){var t=r.style,a=t.image;if(a&&!ot(a)&&(Zu(a)?a=a.src:Xu(a)&&(a=a.toDataURL())),!!a){var n=t.x||0,i=t.y||0,o=t.width,s=t.height,l={href:a,width:o,height:s};return n&&(l.x=n),i&&(l.y=i),di(l,r.transform),fi(l,t,r,e),pi(l,r),e.animation&&Sa(r,l,e),lt("image",r.id+"",l)}}function L0(r,e){var t=r.style,a=t.text;if(a!=null&&(a+=""),!(!a||isNaN(t.x)||isNaN(t.y))){var n=t.font||Dc,i=t.x||0,o=Cc(t.y||0,Lc(n),t.textBaseline),s=Pc[t.textAlign]||t.textAlign,l={"dominant-baseline":"central","text-anchor":s};if(Mc(t)){var u="",v=t.fontStyle,c=Ec(t.fontSize);if(!parseFloat(c))return;var h=t.fontFamily||Rc,f=t.fontWeight;u+="font-size:"+c+";font-family:"+h+";",v&&v!=="normal"&&(u+="font-style:"+v+";"),f&&f!=="normal"&&(u+="font-weight:"+f+";"),l.style=u}else l.style="font: "+n;return a.match(/\s/)&&(l["xml:space"]="preserve"),i&&(l.x=i),o&&(l.y=o),di(l,r.transform),fi(l,t,r,e),pi(l,r),e.animation&&Sa(r,l,e),lt("text",r.id+"",l,void 0,a)}}function ws(r,e){if(r instanceof Gt)return qu(r,e);if(r instanceof ce)return C0(r,e);if(r instanceof Ks)return L0(r,e)}function P0(r,e,t){var a=r.style;if(Fc(a)){var n=Hc(r),i=t.shadowCache,o=i[n];if(!o){var s=r.getGlobalScale(),l=s[0],u=s[1];if(!l||!u)return;var v=a.shadowOffsetX||0,c=a.shadowOffsetY||0,h=a.shadowBlur,f=gr(a.shadowColor),p=f.opacity,d=f.color,g=h/2/l,S=h/2/u,m=g+" "+S;o=t.zrId+"-s"+t.shadowIdx++,t.defs[o]=lt("filter",o,{id:o,x:"-100%",y:"-100%",width:"300%",height:"300%"},[lt("feDropShadow","",{dx:v/l,dy:c/u,stdDeviation:m,"flood-color":d,"flood-opacity":p})]),i[n]=o}e.filter=ca(o)}}function ju(r,e,t,a){var n=r[t],i,o={gradientUnits:n.global?"userSpaceOnUse":"objectBoundingBox"};if(Vc(n))i="linearGradient",o.x1=n.x,o.y1=n.y,o.x2=n.x2,o.y2=n.y2;else if(Nc(n))i="radialGradient",o.cx=oe(n.x,.5),o.cy=oe(n.y,.5),o.r=oe(n.r,.5);else return;for(var s=n.colorStops,l=[],u=0,v=s.length;u<v;++u){var c=Gc(s[u].offset)*100+"%",h=s[u].color,f=gr(h),p=f.color,d=f.opacity,g={offset:c};g["stop-color"]=p,d<1&&(g["stop-opacity"]=d),l.push(lt("stop",u+"",g))}var S=lt(i,"",o,l),m=hi(S),y=a.gradientCache,w=y[m];w||(w=a.zrId+"-g"+a.gradientIdx++,y[m]=w,o.id=w,a.defs[w]=lt(i,w,o,l)),e[t]=ca(w)}function Ku(r,e,t,a){var n=r.style[t],i=r.getBoundingRect(),o={},s=n.repeat,l=s==="no-repeat",u=s==="repeat-x",v=s==="repeat-y",c;if(kc(n)){var h=n.imageWidth,f=n.imageHeight,p=void 0,d=n.image;if(ot(d)?p=d:Zu(d)?p=d.src:Xu(d)&&(p=d.toDataURL()),typeof Image>"u"){var g="Image width/height must been given explictly in svg-ssr renderer.";Ur(h,g),Ur(f,g)}else if(h==null||f==null){var S=function(A,D){if(A){var E=A.elm,P=h||D.width,C=f||D.height;A.tag==="pattern"&&(u?(C=1,P/=i.width):v&&(P=1,C/=i.height)),A.attrs.width=P,A.attrs.height=C,E&&(E.setAttribute("width",P),E.setAttribute("height",C))}},m=zc(p,null,r,function(A){l||S(b,A),S(c,A)});m&&m.width&&m.height&&(h=h||m.width,f=f||m.height)}c=lt("image","img",{href:p,width:h,height:f}),o.width=h,o.height=f}else n.svgElement&&(c=Ut(n.svgElement),o.width=n.svgWidth,o.height=n.svgHeight);if(c){var y,w;l?y=w=1:u?(w=1,y=o.width/i.width):v?(y=1,w=o.height/i.height):o.patternUnits="userSpaceOnUse",y!=null&&!isNaN(y)&&(o.width=y),w!=null&&!isNaN(w)&&(o.height=w);var x=Sl(n);x&&(o.patternTransform=x);var b=lt("pattern","",o,[c]),_=hi(b),T=a.patternCache,I=T[_];I||(I=a.zrId+"-p"+a.patternIdx++,T[_]=I,o.id=I,b=a.defs[I]=lt("pattern",I,o,[c])),e[t]=ca(I)}}function M0(r,e,t){var a=t.clipPathCache,n=t.defs,i=a[r.id];if(!i){i=t.zrId+"-c"+t.clipPathIdx++;var o={id:i};a[r.id]=i,n[i]=lt("clipPath",i,o,[qu(r,t)])}e["clip-path"]=ca(i)}function _s(r){return document.createTextNode(r)}function Ie(r,e,t){r.insertBefore(e,t)}function As(r,e){r.removeChild(e)}function Ts(r,e){r.appendChild(e)}function Ju(r){return r.parentNode}function Qu(r){return r.nextSibling}function Qa(r,e){r.textContent=e}var Is=58,E0=120,R0=lt("","");function An(r){return r===void 0}function Ot(r){return r!==void 0}function V0(r,e,t){for(var a={},n=e;n<=t;++n){var i=r[n].key;i!==void 0&&(a[i]=n)}return a}function cr(r,e){var t=r.key===e.key,a=r.tag===e.tag;return a&&t}function br(r){var e,t=r.children,a=r.tag;if(Ot(a)){var n=r.elm=Uu(a);if(gi(R0,r),q(t))for(e=0;e<t.length;++e){var i=t[e];i!=null&&Ts(n,br(i))}else Ot(r.text)&&!Rn(r.text)&&Ts(n,_s(r.text))}else r.elm=_s(r.text);return r.elm}function tv(r,e,t,a,n){for(;a<=n;++a){var i=t[a];i!=null&&Ie(r,br(i),e)}}function aa(r,e,t,a){for(;t<=a;++t){var n=e[t];if(n!=null)if(Ot(n.tag)){var i=Ju(n.elm);As(i,n.elm)}else As(r,n.elm)}}function gi(r,e){var t,a=e.elm,n=r&&r.attrs||{},i=e.attrs||{};if(n!==i){for(t in i){var o=i[t],s=n[t];s!==o&&(o===!0?a.setAttribute(t,""):o===!1?a.removeAttribute(t):t==="style"?a.style.cssText=o:t.charCodeAt(0)!==E0?a.setAttribute(t,o):t==="xmlns:xlink"||t==="xmlns"?a.setAttributeNS(f0,t,o):t.charCodeAt(3)===Is?a.setAttributeNS(p0,t,o):t.charCodeAt(5)===Is?a.setAttributeNS(Wu,t,o):a.setAttribute(t,o))}for(t in n)t in i||a.removeAttribute(t)}}function N0(r,e,t){for(var a=0,n=0,i=e.length-1,o=e[0],s=e[i],l=t.length-1,u=t[0],v=t[l],c,h,f,p;a<=i&&n<=l;)o==null?o=e[++a]:s==null?s=e[--i]:u==null?u=t[++n]:v==null?v=t[--l]:cr(o,u)?(Ue(o,u),o=e[++a],u=t[++n]):cr(s,v)?(Ue(s,v),s=e[--i],v=t[--l]):cr(o,v)?(Ue(o,v),Ie(r,o.elm,Qu(s.elm)),o=e[++a],v=t[--l]):cr(s,u)?(Ue(s,u),Ie(r,s.elm,o.elm),s=e[--i],u=t[++n]):(An(c)&&(c=V0(e,a,i)),h=c[u.key],An(h)?Ie(r,br(u),o.elm):(f=e[h],f.tag!==u.tag?Ie(r,br(u),o.elm):(Ue(f,u),e[h]=void 0,Ie(r,f.elm,o.elm))),u=t[++n]);(a<=i||n<=l)&&(a>i?(p=t[l+1]==null?null:t[l+1].elm,tv(r,p,t,n,l)):aa(r,e,a,i))}function Ue(r,e){var t=e.elm=r.elm,a=r.children,n=e.children;r!==e&&(gi(r,e),An(e.text)?Ot(a)&&Ot(n)?a!==n&&N0(t,a,n):Ot(n)?(Ot(r.text)&&Qa(t,""),tv(t,null,n,0,n.length-1)):Ot(a)?aa(t,a,0,a.length-1):Ot(r.text)&&Qa(t,""):r.text!==e.text&&(Ot(a)&&aa(t,a,0,a.length-1),Qa(t,e.text)))}function G0(r,e){if(cr(r,e))Ue(r,e);else{var t=r.elm,a=Ju(t);br(e),a!==null&&(Ie(a,e.elm,Qu(t)),aa(a,[r],0,0))}return e}var k0=0,z0=function(){function r(e,t,a){if(this.type="svg",this.refreshHover=Ds(),this.configLayer=Ds(),this.storage=t,this._opts=a=W({},a),this.root=e,this._id="zr"+k0++,this._oldVNode=ds(a.width,a.height),e&&!a.ssr){var n=this._viewport=document.createElement("div");n.style.cssText="position:relative;overflow:hidden";var i=this._svgDom=this._oldVNode.elm=Uu("svg");gi(null,this._oldVNode),n.appendChild(i),e.appendChild(n)}this.resize(a.width,a.height)}return r.prototype.getType=function(){return this.type},r.prototype.getViewportRoot=function(){return this._viewport},r.prototype.getViewportRootOffset=function(){var e=this.getViewportRoot();if(e)return{offsetLeft:e.offsetLeft||0,offsetTop:e.offsetTop||0}},r.prototype.getSvgDom=function(){return this._svgDom},r.prototype.refresh=function(){if(this.root){var e=this.renderToVNode({willUpdate:!0});e.attrs.style="position:absolute;left:0;top:0;user-select:none",G0(this._oldVNode,e),this._oldVNode=e}},r.prototype.renderOneToVNode=function(e){return ws(e,_n(this._id))},r.prototype.renderToVNode=function(e){e=e||{};var t=this.storage.getDisplayList(!0),a=this._width,n=this._height,i=_n(this._id);i.animation=e.animation,i.willUpdate=e.willUpdate,i.compress=e.compress,i.emphasis=e.emphasis,i.ssr=this._opts.ssr;var o=[],s=this._bgVNode=O0(a,n,this._backgroundColor,i);s&&o.push(s);var l=e.compress?null:this._mainVNode=lt("g","main",{},[]);this._paintList(t,i,l?l.children:o),l&&o.push(l);var u=F(Dt(i.defs),function(h){return i.defs[h]});if(u.length&&o.push(lt("defs","defs",{},u)),e.animation){var v=y0(i.cssNodes,i.cssAnims,{newline:!0});if(v){var c=lt("style","stl",{},[],v);o.push(c)}}return ds(a,n,o,e.useViewBox)},r.prototype.renderToString=function(e){return e=e||{},hi(this.renderToVNode({animation:oe(e.cssAnimation,!0),emphasis:oe(e.cssEmphasis,!0),willUpdate:!1,compress:!0,useViewBox:oe(e.useViewBox,!0)}),{newline:!0})},r.prototype.setBackgroundColor=function(e){this._backgroundColor=e},r.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},r.prototype._paintList=function(e,t,a){for(var n=e.length,i=[],o=0,s,l,u=0,v=0;v<n;v++){var c=e[v];if(!c.invisible){var h=c.__clipPaths,f=h&&h.length||0,p=l&&l.length||0,d=void 0;for(d=Math.max(f-1,p-1);d>=0&&!(h&&l&&h[d]===l[d]);d--);for(var g=p-1;g>d;g--)o--,s=i[o-1];for(var S=d+1;S<f;S++){var m={};M0(h[S],m,t);var y=lt("g","clip-g-"+u++,m,[]);(s?s.children:a).push(y),i[o++]=y,s=y}l=h;var w=ws(c,t);w&&(s?s.children:a).push(w)}}},r.prototype.resize=function(e,t){var a=this._opts,n=this.root,i=this._viewport;if(e!=null&&(a.width=e),t!=null&&(a.height=t),n&&i&&(i.style.display="none",e=Vi(n,0,a),t=Vi(n,1,a),i.style.display=""),this._width!==e||this._height!==t){if(this._width=e,this._height=t,i){var o=i.style;o.width=e+"px",o.height=t+"px"}if(kn(this._backgroundColor))this.refresh();else{var s=this._svgDom;s&&(s.setAttribute("width",e),s.setAttribute("height",t));var l=this._bgVNode&&this._bgVNode.elm;l&&(l.setAttribute("width",e),l.setAttribute("height",t))}}},r.prototype.getWidth=function(){return this._width},r.prototype.getHeight=function(){return this._height},r.prototype.dispose=function(){this.root&&(this.root.innerHTML=""),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},r.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},r.prototype.toDataURL=function(e){var t=this.renderToString(),a="data:image/svg+xml;";return e?(t=Wc(t),t&&a+"base64,"+t):a+"charset=UTF-8,"+encodeURIComponent(t)},r}();function Ds(r){return function(){}}function O0(r,e,t,a){var n;if(t&&t!=="none")if(n=lt("rect","bg",{width:r,height:e,x:"0",y:"0"}),wl(t))ju({fill:t},n.attrs,"fill",a);else if(kn(t))Ku({style:{fill:t},dirty:qe,getBoundingRect:function(){return{width:r,height:e}}},n.attrs,"fill",a);else{var i=gr(t),o=i.color,s=i.opacity;n.attrs.fill=o,s<1&&(n.attrs["fill-opacity"]=s)}return n}function B0(r){r.registerPainter("svg",z0)}var zt=bl.CMD;function $e(r,e){return Math.abs(r-e)<1e-5}function Tn(r){var e=r.data,t=r.len(),a=[],n,i=0,o=0,s=0,l=0;function u(P,C){n&&n.length>2&&a.push(n),n=[P,C]}function v(P,C,L,R){$e(P,L)&&$e(C,R)||n.push(P,C,L,R,L,R)}function c(P,C,L,R,V,N){var k=Math.abs(C-P),O=Math.tan(k/4)*4/3,H=C<P?-1:1,Y=Math.cos(P),tt=Math.sin(P),K=Math.cos(C),et=Math.sin(C),vt=Y*V+L,kt=tt*N+R,U=K*V+L,B=et*N+R,Q=V*O*H,Z=N*O*H;n.push(vt-Q*tt,kt+Z*Y,U+Q*et,B-Z*K,U,B)}for(var h,f,p,d,g=0;g<t;){var S=e[g++],m=g===1;switch(m&&(i=e[g],o=e[g+1],s=i,l=o,(S===zt.L||S===zt.C||S===zt.Q)&&(n=[s,l])),S){case zt.M:i=s=e[g++],o=l=e[g++],u(s,l);break;case zt.L:h=e[g++],f=e[g++],v(i,o,h,f),i=h,o=f;break;case zt.C:n.push(e[g++],e[g++],e[g++],e[g++],i=e[g++],o=e[g++]);break;case zt.Q:h=e[g++],f=e[g++],p=e[g++],d=e[g++],n.push(i+2/3*(h-i),o+2/3*(f-o),p+2/3*(h-p),d+2/3*(f-d),p,d),i=p,o=d;break;case zt.A:var y=e[g++],w=e[g++],x=e[g++],b=e[g++],_=e[g++],T=e[g++]+_;g+=1;var I=!e[g++];h=Math.cos(_)*x+y,f=Math.sin(_)*b+w,m?(s=h,l=f,u(s,l)):v(i,o,h,f),i=Math.cos(T)*x+y,o=Math.sin(T)*b+w;for(var A=(I?-1:1)*Math.PI/2,D=_;I?D>T:D<T;D+=A){var E=I?Math.max(D+A,T):Math.min(D+A,T);c(D,E,y,w,x,b)}break;case zt.R:s=i=e[g++],l=o=e[g++],h=s+e[g++],f=l+e[g++],u(h,l),v(h,l,h,f),v(h,f,s,f),v(s,f,s,l),v(s,l,h,l);break;case zt.Z:n&&v(i,o,s,l),i=s,o=l;break}}return n&&n.length>2&&a.push(n),a}function In(r,e,t,a,n,i,o,s,l,u){if($e(r,t)&&$e(e,a)&&$e(n,o)&&$e(i,s)){l.push(o,s);return}var v=2/u,c=v*v,h=o-r,f=s-e,p=Math.sqrt(h*h+f*f);h/=p,f/=p;var d=t-r,g=a-e,S=n-o,m=i-s,y=d*d+g*g,w=S*S+m*m;if(y<c&&w<c){l.push(o,s);return}var x=h*d+f*g,b=-h*S-f*m,_=y-x*x,T=w-b*b;if(_<c&&x>=0&&T<c&&b>=0){l.push(o,s);return}var I=[],A=[];Xr(r,t,n,o,.5,I),Xr(e,a,i,s,.5,A),In(I[0],A[0],I[1],A[1],I[2],A[2],I[3],A[3],l,u),In(I[4],A[4],I[5],A[5],I[6],A[6],I[7],A[7],l,u)}function F0(r,e){var t=Tn(r),a=[];e=e||1;for(var n=0;n<t.length;n++){var i=t[n],o=[],s=i[0],l=i[1];o.push(s,l);for(var u=2;u<i.length;){var v=i[u++],c=i[u++],h=i[u++],f=i[u++],p=i[u++],d=i[u++];In(s,l,v,c,h,f,p,d,o,e),s=p,l=d}a.push(o)}return a}function ev(r,e,t){var a=r[e],n=r[1-e],i=Math.abs(a/n),o=Math.ceil(Math.sqrt(i*t)),s=Math.floor(t/o);s===0&&(s=1,o=t);for(var l=[],u=0;u<o;u++)l.push(s);var v=o*s,c=t-v;if(c>0)for(var u=0;u<c;u++)l[u%o]+=1;return l}function Cs(r,e,t){for(var a=r.r0,n=r.r,i=r.startAngle,o=r.endAngle,s=Math.abs(o-i),l=s*n,u=n-a,v=l>Math.abs(u),c=ev([l,u],v?0:1,e),h=(v?s:u)/c.length,f=0;f<c.length;f++)for(var p=(v?u:s)/c[f],d=0;d<c[f];d++){var g={};v?(g.startAngle=i+h*f,g.endAngle=i+h*(f+1),g.r0=a+p*d,g.r=a+p*(d+1)):(g.startAngle=i+p*d,g.endAngle=i+p*(d+1),g.r0=a+h*f,g.r=a+h*(f+1)),g.clockwise=r.clockwise,g.cx=r.cx,g.cy=r.cy,t.push(g)}}function H0(r,e,t){for(var a=r.width,n=r.height,i=a>n,o=ev([a,n],i?0:1,e),s=i?"width":"height",l=i?"height":"width",u=i?"x":"y",v=i?"y":"x",c=r[s]/o.length,h=0;h<o.length;h++)for(var f=r[l]/o[h],p=0;p<o[h];p++){var d={};d[u]=h*c,d[v]=p*f,d[s]=c,d[l]=f,d.x+=r.x,d.y+=r.y,t.push(d)}}function Ls(r,e,t,a){return r*a-t*e}function W0(r,e,t,a,n,i,o,s){var l=t-r,u=a-e,v=o-n,c=s-i,h=Ls(v,c,l,u);if(Math.abs(h)<1e-6)return null;var f=r-n,p=e-i,d=Ls(f,p,v,c)/h;return d<0||d>1?null:new ae(d*l+r,d*u+e)}function U0(r,e,t){var a=new ae;ae.sub(a,t,e),a.normalize();var n=new ae;ae.sub(n,r,e);var i=n.dot(a);return i}function Fe(r,e){var t=r[r.length-1];t&&t[0]===e[0]&&t[1]===e[1]||r.push(e)}function $0(r,e,t){for(var a=r.length,n=[],i=0;i<a;i++){var o=r[i],s=r[(i+1)%a],l=W0(o[0],o[1],s[0],s[1],e.x,e.y,t.x,t.y);l&&n.push({projPt:U0(l,e,t),pt:l,idx:i})}if(n.length<2)return[{points:r},{points:r}];n.sort(function(g,S){return g.projPt-S.projPt});var u=n[0],v=n[n.length-1];if(v.idx<u.idx){var c=u;u=v,v=c}for(var h=[u.pt.x,u.pt.y],f=[v.pt.x,v.pt.y],p=[h],d=[f],i=u.idx+1;i<=v.idx;i++)Fe(p,r[i].slice());Fe(p,f),Fe(p,h);for(var i=v.idx+1;i<=u.idx+a;i++)Fe(d,r[i%a].slice());return Fe(d,h),Fe(d,f),[{points:p},{points:d}]}function Ps(r){var e=r.points,t=[],a=[];ua(e,t,a);var n=new ct(t[0],t[1],a[0]-t[0],a[1]-t[1]),i=n.width,o=n.height,s=n.x,l=n.y,u=new ae,v=new ae;return i>o?(u.x=v.x=s+i/2,u.y=l,v.y=l+o):(u.y=v.y=l+o/2,u.x=s,v.x=s+i),$0(e,u,v)}function na(r,e,t,a){if(t===1)a.push(e);else{var n=Math.floor(t/2),i=r(e);na(r,i[0],n,a),na(r,i[1],t-n,a)}return a}function Y0(r,e){for(var t=[],a=0;a<e;a++)t.push(zn(r));return t}function Z0(r,e){e.setStyle(r.style),e.z=r.z,e.z2=r.z2,e.zlevel=r.zlevel}function X0(r){for(var e=[],t=0;t<r.length;)e.push([r[t++],r[t++]]);return e}function q0(r,e){var t=[],a=r.shape,n;switch(r.type){case"rect":H0(a,e,t),n=At;break;case"sector":Cs(a,e,t),n=Me;break;case"circle":Cs({r0:0,r:a.r,startAngle:0,endAngle:Math.PI*2,cx:a.cx,cy:a.cy},e,t),n=Me;break;default:var i=r.getComputedTransform(),o=i?Math.sqrt(Math.max(i[0]*i[0]+i[1]*i[1],i[2]*i[2]+i[3]*i[3])):1,s=F(F0(r.getUpdatedPathProxy(),o),function(S){return X0(S)}),l=s.length;if(l===0)na(Ps,{points:s[0]},e,t);else if(l===e)for(var u=0;u<l;u++)t.push({points:s[u]});else{var v=0,c=F(s,function(S){var m=[],y=[];ua(S,m,y);var w=(y[1]-m[1])*(y[0]-m[0]);return v+=w,{poly:S,area:w}});c.sort(function(S,m){return m.area-S.area});for(var h=e,u=0;u<l;u++){var f=c[u];if(h<=0)break;var p=u===l-1?h:Math.ceil(f.area/v*e);p<0||(na(Ps,{points:f.poly},p,t),h-=p)}}n=Ee;break}if(!n)return Y0(r,e);for(var d=[],u=0;u<t.length;u++){var g=new n;g.setShape(t[u]),Z0(r,g),d.push(g)}return d}function j0(r,e){var t=r.length,a=e.length;if(t===a)return[r,e];for(var n=[],i=[],o=t<a?r:e,s=Math.min(t,a),l=Math.abs(a-t)/6,u=(s-2)/6,v=Math.ceil(l/u)+1,c=[o[0],o[1]],h=l,f=2;f<s;){var p=o[f-2],d=o[f-1],g=o[f++],S=o[f++],m=o[f++],y=o[f++],w=o[f++],x=o[f++];if(h<=0){c.push(g,S,m,y,w,x);continue}for(var b=Math.min(h,v-1)+1,_=1;_<=b;_++){var T=_/b;Xr(p,g,m,w,T,n),Xr(d,S,y,x,T,i),p=n[3],d=i[3],c.push(n[1],i[1],n[2],i[2],p,d),g=n[5],S=i[5],m=n[6],y=i[6]}h-=b-1}return o===r?[c,e]:[r,c]}function Ms(r,e){for(var t=r.length,a=r[t-2],n=r[t-1],i=[],o=0;o<e.length;)i[o++]=a,i[o++]=n;return i}function K0(r,e){for(var t,a,n,i=[],o=[],s=0;s<Math.max(r.length,e.length);s++){var l=r[s],u=e[s],v=void 0,c=void 0;l?u?(t=j0(l,u),v=t[0],c=t[1],a=v,n=c):(c=Ms(n||l,l),v=l):(v=Ms(a||u,u),c=u),i.push(v),o.push(c)}return[i,o]}function Es(r){for(var e=0,t=0,a=0,n=r.length,i=0,o=n-2;i<n;o=i,i+=2){var s=r[o],l=r[o+1],u=r[i],v=r[i+1],c=s*v-u*l;e+=c,t+=(s+u)*c,a+=(l+v)*c}return e===0?[r[0]||0,r[1]||0]:[t/e/3,a/e/3,e]}function J0(r,e,t,a){for(var n=(r.length-2)/6,i=1/0,o=0,s=r.length,l=s-2,u=0;u<n;u++){for(var v=u*6,c=0,h=0;h<s;h+=2){var f=h===0?v:(v+h-2)%l+2,p=r[f]-t[0],d=r[f+1]-t[1],g=e[h]-a[0],S=e[h+1]-a[1],m=g-p,y=S-d;c+=m*m+y*y}c<i&&(i=c,o=u)}return o}function Q0(r){for(var e=[],t=r.length,a=0;a<t;a+=2)e[a]=r[t-a-2],e[a+1]=r[t-a-1];return e}function tS(r,e,t,a){for(var n=[],i,o=0;o<r.length;o++){var s=r[o],l=e[o],u=Es(s),v=Es(l);i==null&&(i=u[2]<0!=v[2]<0);var c=[],h=[],f=0,p=1/0,d=[],g=s.length;i&&(s=Q0(s));for(var S=J0(s,l,u,v)*6,m=g-2,y=0;y<m;y+=2){var w=(S+y)%m+2;c[y+2]=s[w]-u[0],c[y+3]=s[w+1]-u[1]}c[0]=s[S]-u[0],c[1]=s[S+1]-u[1];for(var x=a/t,b=-a/2;b<=a/2;b+=x){for(var _=Math.sin(b),T=Math.cos(b),I=0,y=0;y<s.length;y+=2){var A=c[y],D=c[y+1],E=l[y]-v[0],P=l[y+1]-v[1],C=E*T-P*_,L=E*_+P*T;d[y]=C,d[y+1]=L;var R=C-A,V=L-D;I+=R*R+V*V}if(I<p){p=I,f=b;for(var N=0;N<d.length;N++)h[N]=d[N]}}n.push({from:c,to:h,fromCp:u,toCp:v,rotation:-f})}return n}function ia(r){return r.__isCombineMorphing}var rv="__mOriginal_";function oa(r,e,t){var a=rv+e,n=r[a]||r[e];r[a]||(r[a]=r[e]);var i=t.replace,o=t.after,s=t.before;r[e]=function(){var l=arguments,u;return s&&s.apply(this,l),i?u=i.apply(this,l):u=n.apply(this,l),o&&o.apply(this,l),u}}function hr(r,e){var t=rv+e;r[t]&&(r[e]=r[t],r[t]=null)}function Rs(r,e){for(var t=0;t<r.length;t++)for(var a=r[t],n=0;n<a.length;){var i=a[n],o=a[n+1];a[n++]=e[0]*i+e[2]*o+e[4],a[n++]=e[1]*i+e[3]*o+e[5]}}function av(r,e){var t=r.getUpdatedPathProxy(),a=e.getUpdatedPathProxy(),n=K0(Tn(t),Tn(a)),i=n[0],o=n[1],s=r.getComputedTransform(),l=e.getComputedTransform();function u(){this.transform=null}s&&Rs(i,s),l&&Rs(o,l),oa(e,"updateTransform",{replace:u}),e.transform=null;var v=tS(i,o,10,Math.PI),c=[];oa(e,"buildPath",{replace:function(h){for(var f=e.__morphT,p=1-f,d=[],g=0;g<v.length;g++){var S=v[g],m=S.from,y=S.to,w=S.rotation*f,x=S.fromCp,b=S.toCp,_=Math.sin(w),T=Math.cos(w);Uc(d,x,b,f);for(var I=0;I<m.length;I+=2){var A=m[I],D=m[I+1],E=y[I],P=y[I+1],C=A*p+E*f,L=D*p+P*f;c[I]=C*T-L*_+d[0],c[I+1]=C*_+L*T+d[1]}var R=c[0],V=c[1];h.moveTo(R,V);for(var I=2;I<m.length;){var E=c[I++],P=c[I++],N=c[I++],k=c[I++],O=c[I++],H=c[I++];R===E&&V===P&&N===O&&k===H?h.lineTo(O,H):h.bezierCurveTo(E,P,N,k,O,H),R=O,V=H}}}})}function yi(r,e,t){if(!r||!e)return e;var a=t.done,n=t.during;av(r,e),e.__morphT=0;function i(){hr(e,"buildPath"),hr(e,"updateTransform"),e.__morphT=-1,e.createPathProxy(),e.dirtyShape()}return e.animateTo({__morphT:1},j({during:function(o){e.dirtyShape(),n&&n(o)},done:function(){i(),a&&a()}},t)),e}function eS(r,e,t,a,n,i){var o=16;r=n===t?0:Math.round(32767*(r-t)/(n-t)),e=i===a?0:Math.round(32767*(e-a)/(i-a));for(var s=0,l,u=(1<<o)/2;u>0;u/=2){var v=0,c=0;(r&u)>0&&(v=1),(e&u)>0&&(c=1),s+=u*u*(3*v^c),c===0&&(v===1&&(r=u-1-r,e=u-1-e),l=r,r=e,e=l)}return s}function sa(r){var e=1/0,t=1/0,a=-1/0,n=-1/0,i=F(r,function(s){var l=s.getBoundingRect(),u=s.getComputedTransform(),v=l.x+l.width/2+(u?u[4]:0),c=l.y+l.height/2+(u?u[5]:0);return e=Math.min(v,e),t=Math.min(c,t),a=Math.max(v,a),n=Math.max(c,n),[v,c]}),o=F(i,function(s,l){return{cp:s,z:eS(s[0],s[1],e,t,a,n),path:r[l]}});return o.sort(function(s,l){return s.z-l.z}).map(function(s){return s.path})}function nv(r){return q0(r.path,r.count)}function Dn(){return{fromIndividuals:[],toIndividuals:[],count:0}}function rS(r,e,t){var a=[];function n(x){for(var b=0;b<x.length;b++){var _=x[b];ia(_)?n(_.childrenRef()):_ instanceof Gt&&a.push(_)}}n(r);var i=a.length;if(!i)return Dn();var o=t.dividePath||nv,s=o({path:e,count:i});if(s.length!==i)return console.error("Invalid morphing: unmatched splitted path"),Dn();a=sa(a),s=sa(s);for(var l=t.done,u=t.during,v=t.individualDelay,c=new ir,h=0;h<i;h++){var f=a[h],p=s[h];p.parent=e,p.copyTransform(c),v||av(f,p)}e.__isCombineMorphing=!0,e.childrenRef=function(){return s};function d(x){for(var b=0;b<s.length;b++)s[b].addSelfToZr(x)}oa(e,"addSelfToZr",{after:function(x){d(x)}}),oa(e,"removeSelfFromZr",{after:function(x){for(var b=0;b<s.length;b++)s[b].removeSelfFromZr(x)}});function g(){e.__isCombineMorphing=!1,e.__morphT=-1,e.childrenRef=null,hr(e,"addSelfToZr"),hr(e,"removeSelfFromZr")}var S=s.length;if(v)for(var m=S,y=function(){m--,m===0&&(g(),l&&l())},h=0;h<S;h++){var w=v?j({delay:(t.delay||0)+v(h,S,a[h],s[h]),done:y},t):t;yi(a[h],s[h],w)}else e.__morphT=0,e.animateTo({__morphT:1},j({during:function(x){for(var b=0;b<S;b++){var _=s[b];_.__morphT=e.__morphT,_.dirtyShape()}u&&u(x)},done:function(){g();for(var x=0;x<r.length;x++)hr(r[x],"updateTransform");l&&l()}},t));return e.__zr&&d(e.__zr),{fromIndividuals:a,toIndividuals:s,count:S}}function aS(r,e,t){var a=e.length,n=[],i=t.dividePath||nv;function o(f){for(var p=0;p<f.length;p++){var d=f[p];ia(d)?o(d.childrenRef()):d instanceof Gt&&n.push(d)}}if(ia(r)){o(r.childrenRef());var s=n.length;if(s<a)for(var l=0,u=s;u<a;u++)n.push(zn(n[l++%s]));n.length=a}else{n=i({path:r,count:a});for(var v=r.getComputedTransform(),u=0;u<n.length;u++)n[u].setLocalTransform(v);if(n.length!==a)return console.error("Invalid morphing: unmatched splitted path"),Dn()}n=sa(n),e=sa(e);for(var c=t.individualDelay,u=0;u<a;u++){var h=c?j({delay:(t.delay||0)+c(u,a,n[u],e[u])},t):t;yi(n[u],e[u],h)}return{fromIndividuals:n,toIndividuals:e,count:e.length}}function Vs(r){return q(r[0])}function Ns(r,e){for(var t=[],a=r.length,n=0;n<a;n++)t.push({one:r[n],many:[]});for(var n=0;n<e.length;n++){var i=e[n].length,o=void 0;for(o=0;o<i;o++)t[o%a].many.push(e[n][o])}for(var s=0,n=a-1;n>=0;n--)if(!t[n].many.length){var l=t[s].many;if(l.length<=1)if(s)s=0;else return t;var i=l.length,u=Math.ceil(i/2);t[n].many=l.slice(u,i),t[s].many=l.slice(0,u),s++}return t}var nS={clone:function(r){for(var e=[],t=1-Math.pow(1-r.path.style.opacity,1/r.count),a=0;a<r.count;a++){var n=zn(r.path);n.setStyle("opacity",t),e.push(n)}return e},split:null};function tn(r,e,t,a,n,i){if(!r.length||!e.length)return;var o=On("update",a,n);if(!(o&&o.duration>0))return;var s=a.getModel("universalTransition").get("delay"),l=Object.assign({setToFinal:!0},o),u,v;Vs(r)&&(u=r,v=e),Vs(e)&&(u=e,v=r);function c(S,m,y,w,x){var b=S.many,_=S.one;if(b.length===1&&!x){var T=m?b[0]:_,I=m?_:b[0];if(ia(T))c({many:[T],one:I},!0,y,w,!0);else{var A=s?j({delay:s(y,w)},l):l;yi(T,I,A),i(T,I,T,I,A)}}else for(var D=j({dividePath:nS[t],individualDelay:s&&function(V,N,k,O){return s(V+y,w)}},l),E=m?rS(b,_,D):aS(_,b,D),P=E.fromIndividuals,C=E.toIndividuals,L=P.length,R=0;R<L;R++){var A=s?j({delay:s(R,L)},l):l;i(P[R],C[R],m?b[R]:S.one,m?S.one:b[R],A)}}for(var h=u?u===r:r.length>e.length,f=u?Ns(v,u):Ns(h?e:r,[h?r:e]),p=0,d=0;d<f.length;d++)p+=f[d].many.length;for(var g=0,d=0;d<f.length;d++)c(f[d],h,g,p),g+=f[d].many.length}function Ae(r){if(!r)return[];if(q(r)){for(var e=[],t=0;t<r.length;t++)e.push(Ae(r[t]));return e}var a=[];return r.traverse(function(n){n instanceof Gt&&!n.disableMorphing&&!n.invisible&&!n.ignore&&a.push(n)}),a}var iv=1e4,iS=0,Gs=1,ks=2,oS=fe();function sS(r,e){for(var t=r.dimensions,a=0;a<t.length;a++){var n=r.getDimensionInfo(t[a]);if(n&&n.otherDims[e]===0)return t[a]}}function lS(r,e,t){var a=r.getDimensionInfo(t),n=a&&a.ordinalMeta;if(a){var i=r.get(a.name,e);return n&&n.categories[i]||i+""}}function zs(r,e,t,a){var n=a?"itemChildGroupId":"itemGroupId",i=sS(r,n);if(i){var o=lS(r,e,i);return o}var s=r.getRawDataItem(e),l=a?"childGroupId":"groupId";if(s&&s[l])return s[l]+"";if(!a)return t||r.getId(e)}function Os(r){var e=[];return M(r,function(t){var a=t.data,n=t.dataGroupId;if(!(a.count()>iv))for(var i=a.getIndices(),o=0;o<i.length;o++)e.push({data:a,groupId:zs(a,o,n,!1),childGroupId:zs(a,o,n,!0),divide:t.divide,dataIndex:o})}),e}function en(r,e,t){r.traverse(function(a){a instanceof Gt&&Ht(a,{style:{opacity:0}},e,{dataIndex:t,isFrom:!0})})}function rn(r){if(r.parent){var e=r.getComputedTransform();r.setLocalTransform(e),r.parent.remove(r)}}function He(r){r.stopAnimation(),r.isGroup&&r.traverse(function(e){e.stopAnimation()})}function uS(r,e,t){var a=On("update",t,e);a&&r.traverse(function(n){if(n instanceof dr){var i=$c(n);i&&n.animateFrom({style:i},a)}})}function vS(r,e){var t=r.length;if(t!==e.length)return!1;for(var a=0;a<t;a++){var n=r[a],i=e[a];if(n.data.getId(n.dataIndex)!==i.data.getId(i.dataIndex))return!1}return!0}function ov(r,e,t){var a=Os(r),n=Os(e);function i(y,w,x,b,_){(x||y)&&w.animateFrom({style:x&&x!==y?W(W({},x.style),y.style):y.style},_)}var o=!1,s=iS,l=J(),u=J();a.forEach(function(y){y.groupId&&l.set(y.groupId,!0),y.childGroupId&&u.set(y.childGroupId,!0)});for(var v=0;v<n.length;v++){var c=n[v].groupId;if(u.get(c)){s=Gs;break}var h=n[v].childGroupId;if(h&&l.get(h)){s=ks;break}}function f(y,w){return function(x){var b=x.data,_=x.dataIndex;return w?b.getId(_):y?s===Gs?x.childGroupId:x.groupId:s===ks?x.childGroupId:x.groupId}}var p=vS(a,n),d={};if(!p)for(var v=0;v<n.length;v++){var g=n[v],S=g.data.getItemGraphicEl(g.dataIndex);S&&(d[S.id]=!0)}function m(y,w){var x=a[w],b=n[y],_=b.data.hostModel,T=x.data.getItemGraphicEl(x.dataIndex),I=b.data.getItemGraphicEl(b.dataIndex);if(T===I){I&&uS(I,b.dataIndex,_);return}T&&d[T.id]||I&&(He(I),T?(He(T),rn(T),o=!0,tn(Ae(T),Ae(I),b.divide,_,y,i)):en(I,_,y))}new je(a,n,f(!0,p),f(!1,p),null,"multiple").update(m).updateManyToOne(function(y,w){var x=n[y],b=x.data,_=b.hostModel,T=b.getItemGraphicEl(x.dataIndex),I=Rt(F(w,function(A){return a[A].data.getItemGraphicEl(a[A].dataIndex)}),function(A){return A&&A!==T&&!d[A.id]});T&&(He(T),I.length?(M(I,function(A){He(A),rn(A)}),o=!0,tn(Ae(I),Ae(T),x.divide,_,y,i)):en(T,_,x.dataIndex))}).updateOneToMany(function(y,w){var x=a[w],b=x.data.getItemGraphicEl(x.dataIndex);if(!(b&&d[b.id])){var _=Rt(F(y,function(I){return n[I].data.getItemGraphicEl(n[I].dataIndex)}),function(I){return I&&I!==b}),T=n[y[0]].data.hostModel;_.length&&(M(_,function(I){return He(I)}),b?(He(b),rn(b),o=!0,tn(Ae(b),Ae(_),x.divide,T,y[0],i)):M(_,function(I){return en(I,T,y[0])}))}}).updateManyToMany(function(y,w){new je(w,y,function(x){return a[x].data.getId(a[x].dataIndex)},function(x){return n[x].data.getId(n[x].dataIndex)}).update(function(x,b){m(y[x],w[b])}).execute()}).execute(),o&&M(e,function(y){var w=y.data,x=w.hostModel,b=x&&t.getViewOfSeriesModel(x),_=On("update",x,0);b&&x.isAnimationEnabled()&&_&&_.duration>0&&b.group.traverse(function(T){T instanceof Gt&&!T.animators.length&&T.animateFrom({style:{opacity:0}},_)})})}function Bs(r){var e=r.getModel("universalTransition").get("seriesKey");return e||r.id}function Fs(r){return q(r)?r.sort().join(","):r}function te(r){if(r.hostModel)return r.hostModel.getModel("universalTransition").get("divideShape")}function cS(r,e){var t=J(),a=J(),n=J();return M(r.oldSeries,function(i,o){var s=r.oldDataGroupIds[o],l=r.oldData[o],u=Bs(i),v=Fs(u);a.set(v,{dataGroupId:s,data:l}),q(u)&&M(u,function(c){n.set(c,{key:v,dataGroupId:s,data:l})})}),M(e.updatedSeries,function(i){if(i.isUniversalTransitionEnabled()&&i.isAnimationEnabled()){var o=i.get("dataGroupId"),s=i.getData(),l=Bs(i),u=Fs(l),v=a.get(u);if(v)t.set(u,{oldSeries:[{dataGroupId:v.dataGroupId,divide:te(v.data),data:v.data}],newSeries:[{dataGroupId:o,divide:te(s),data:s}]});else if(q(l)){var c=[];M(l,function(p){var d=a.get(p);d.data&&c.push({dataGroupId:d.dataGroupId,divide:te(d.data),data:d.data})}),c.length&&t.set(u,{oldSeries:c,newSeries:[{dataGroupId:o,data:s,divide:te(s)}]})}else{var h=n.get(l);if(h){var f=t.get(h.key);f||(f={oldSeries:[{dataGroupId:h.dataGroupId,data:h.data,divide:te(h.data)}],newSeries:[]},t.set(h.key,f)),f.newSeries.push({dataGroupId:o,data:s,divide:te(s)})}}}}),t}function Hs(r,e){for(var t=0;t<r.length;t++){var a=e.seriesIndex!=null&&e.seriesIndex===r[t].seriesIndex||e.seriesId!=null&&e.seriesId===r[t].id;if(a)return t}}function hS(r,e,t,a){var n=[],i=[];M(Ft(r.from),function(o){var s=Hs(e.oldSeries,o);s>=0&&n.push({dataGroupId:e.oldDataGroupIds[s],data:e.oldData[s],divide:te(e.oldData[s]),groupIdDim:o.dimension})}),M(Ft(r.to),function(o){var s=Hs(t.updatedSeries,o);if(s>=0){var l=t.updatedSeries[s].getData();i.push({dataGroupId:e.oldDataGroupIds[s],data:l,divide:te(l),groupIdDim:o.dimension})}}),n.length>0&&i.length>0&&ov(n,i,a)}function fS(r){r.registerUpdateLifecycle("series:beforeupdate",function(e,t,a){M(Ft(a.seriesTransition),function(n){M(Ft(n.to),function(i){for(var o=a.updatedSeries,s=0;s<o.length;s++)(i.seriesIndex!=null&&i.seriesIndex===o[s].seriesIndex||i.seriesId!=null&&i.seriesId===o[s].id)&&(o[s][_a]=!0)})})}),r.registerUpdateLifecycle("series:transition",function(e,t,a){var n=oS(t);if(n.oldSeries&&a.updatedSeries&&a.optionChanged){var i=a.seriesTransition;if(i)M(Ft(i),function(f){hS(f,n,a,t)});else{var o=cS(n,a);M(o.keys(),function(f){var p=o.get(f);ov(p.oldSeries,p.newSeries,t)})}M(a.updatedSeries,function(f){f[_a]&&(f[_a]=!1)})}for(var s=e.getSeries(),l=n.oldSeries=[],u=n.oldDataGroupIds=[],v=n.oldData=[],c=0;c<s.length;c++){var h=s[c].getData();h.count()<iv&&(l.push(s[c]),u.push(s[c].get("dataGroupId")),v.push(h))}})}$([_l]);$([B0]);$([Nl,Gl,Nh,xh,ef,Of,fp,Yp,cd,yd,Id,ig,Pg,kh,bh,Vg,Ug,Kg,ly,py,Ay,Ky]);$(kl);$(_m);$(jl);$(Gm);$(Su);$(wh);$(_h);$(Ah);$(Al);$(Gn);$(Km);$(Tl);$(Th);$(e0);$(Ih);$(Dh);$(Il);$(Ch);$(Lh);$(Ph);$(zl);$(Mh);$(Eh);$(o0);$(Ol);$(Dl);$(fS);$(Gh);const an="Trade Count",pS=Ws({__name:"TimePeriodChart",props:{dailyStats:{},showTitle:{type:Boolean,default:!0},profitCol:{}},setup(r){$([Gl,Nl,_l,kl,Dl,Il,Tl,Al,zl,Ol]);const e=r,t=ee(()=>e.profitCol==="abs_profit"?"Absolute profit":"Relative profit"),a=Us(),n=sv(),i=$s(null),o=ee(()=>{var c;return e.dailyStats.data.reduce((h,f)=>f[e.profitCol]<h?f[e.profitCol]:h,(c=e.dailyStats.data[0])==null?void 0:c[e.profitCol])*(e.profitCol==="rel_profit"?100:1)}),s=ee(()=>{var c;return e.dailyStats.data.reduce((h,f)=>f[e.profitCol]>h?f[e.profitCol]:h,(c=e.dailyStats.data[0])==null?void 0:c[e.profitCol])*(e.profitCol==="rel_profit"?100:1)});Yc({multiple:{type:"units:multiple",transform:function(c){const h=c.upstream.cloneRawData(),{dimension:f,factor:p}=c.config,d=h.map(g=>({...g,[f]:(g[f]*p).toFixed(2)}));return[{dimensions:c.upstream.cloneAllDimensionInfo(),data:d}]}}}.multiple);const u={type:"linear",x:0,y:0,x2:1,y2:0,colorStops:[{offset:0,color:n.colorProfit},{offset:.5,color:n.colorProfit},{offset:.5,color:n.colorLoss},{offset:1,color:n.colorLoss}]},v=ee(()=>({title:{text:"Daily profit",show:e.showTitle},backgroundColor:"rgba(0, 0, 0, 0)",dataset:[{dimensions:["date",e.profitCol,"trade_count"],source:e.dailyStats.data},{transform:{type:"units:multiple",config:{dimension:e.profitCol,factor:e.profitCol=="rel_profit"?100:1}}}],tooltip:{trigger:"axis",axisPointer:{type:"line",label:{backgroundColor:"#6a7985"}}},legend:{data:[{name:t.value,lineStyle:{color:u},itemStyle:{color:u}},{name:an}],right:"5%"},xAxis:[{type:"category"}],visualMap:[{dimension:1,seriesIndex:0,show:!1,pieces:[{max:0,min:o.value,color:n.colorLoss},{min:0,max:s.value,color:n.colorProfit}]}],yAxis:[{type:"value",name:t.value,splitLine:{show:!1},nameRotate:90,nameLocation:"middle",nameGap:35,axisLabel:{formatter:c=>e.profitCol==="rel_profit"?`${c}%`:`${c}`}},{type:"value",name:an,nameRotate:90,nameLocation:"middle",nameGap:30}],grid:{left:"50",right:"45",bottom:"15%"},series:[{type:"line",name:t.value,datasetIndex:1},{type:"bar",name:an,itemStyle:{color:"rgba(150,150,150,0.3)"},yAxisIndex:1,datasetIndex:1}]}));return(c,h)=>c.dailyStats.data?(Se(),Hr(nt(Zc),{key:0,ref_key:"dailyChart",ref:i,option:nt(v),theme:nt(a).chartTheme,style:{height:"100%"},autoresize:""},null,8,["option","theme"])):We("",!0)}}),dS=lv(pS,[["__scopeId","data-v-867a1e5d"]]),gS={class:"flex flex-col h-full"},yS={key:0,class:"mb-2"},mS={class:"me-auto inline text-xl"},SS={class:"flex align-center justify-between"},xS={class:"ps-1"},bS={key:1},LS=Ws({__name:"PeriodBreakdown",props:{multiBotView:{type:Boolean}},setup(r){const e=uv(),t=Us(),a=r,n=ee(()=>e.activeBot.botApiVersion>=2.33||a.multiBotView),i=ee(()=>{const v=[{value:ge.daily,text:"Days"}];return n.value&&(v.push({value:ge.weekly,text:"Weeks"}),v.push({value:ge.monthly,text:"Months"})),v}),o=$s([{value:"abs_profit",text:"Abs $"},{value:"rel_profit",text:"Rel %"}]),s=ee(()=>{if(a.multiBotView)switch(t.timeProfitPeriod){case ge.weekly:return e.allWeeklyStatsSelectedBots;case ge.monthly:return e.allMonthlyStatsSelectedBots;default:return e.allDailyStatsSelectedBots}switch(t.timeProfitPeriod){case ge.weekly:return e.activeBot.weeklyStats;case ge.monthly:return e.activeBot.monthlyStats;default:return e.activeBot.dailyStats}}),l=ee(()=>({...s.value,data:s.value.data?Object.values(s.value.data).sort((v,c)=>v.date>c.date?1:-1):[]}));function u(){a.multiBotView?e.allGetTimeSummary(t.timeProfitPeriod):e.activeBot.getTimeSummary(t.timeProfitPeriod)}return vv(()=>{u()}),(v,c)=>{const h=hv,f=cv,p=zh,d=dS,g=dv,S=pv;return Se(),xa("div",gS,[a.multiBotView?We("",!0):(Se(),xa("div",yS,[ba("h3",mS,Pr(nt(n)?"Period":"Daily")+" Breakdown",1),Qt(f,{class:"float-end",severity:"secondary",onClick:u},{icon:Qe(()=>[Qt(h)]),_:1})])),ba("div",SS,[nt(n)?(Se(),Hr(p,{key:0,id:"order-direction",modelValue:nt(t).timeProfitPeriod,"onUpdate:modelValue":c[0]||(c[0]=m=>nt(t).timeProfitPeriod=m),options:nt(i),name:"radios-btn-default",size:"small","allow-empty":!1,"option-label":"text","option-value":"value",onChange:u},null,8,["modelValue","options"])):We("",!0),Qt(p,{modelValue:nt(t).timeProfitPreference,"onUpdate:modelValue":c[1]||(c[1]=m=>nt(t).timeProfitPreference=m),name:"radios-btn-select",size:"small","allow-empty":!1,"option-label":"text","option-value":"value",options:nt(o),buttons:"","button-variant":"outline-primary"},null,8,["modelValue","options"])]),ba("div",xS,[nt(s)?(Se(),Hr(d,{key:0,"daily-stats":nt(l),"show-title":!1,"profit-col":nt(t).timeProfitPreference},null,8,["daily-stats","profit-col"])):We("",!0)]),a.multiBotView?We("",!0):(Se(),xa("div",bS,[Qt(S,{size:"small",value:nt(s).data},{default:Qe(()=>[Qt(g,{field:"date",header:"Day"}),Qt(g,{field:"abs_profit",header:"Profit"},{body:Qe(({data:m,field:y})=>[wa(Pr(("formatPrice"in v?v.formatPrice:nt(Si))(m[y],nt(e).activeBot.stakeCurrencyDecimals)),1)]),_:1}),Qt(g,{field:"fiat_value",header:`In ${nt(e).activeBot.dailyStats.fiat_display_currency}`},{body:Qe(({data:m,field:y})=>[wa(Pr(("formatPrice"in v?v.formatPrice:nt(Si))(m[y],2)),1)]),_:1},8,["header"]),Qt(g,{field:"trade_count",header:"Trades"}),nt(e).activeBot.botApiVersion>=2.16?(Se(),Hr(g,{key:0,field:"rel_profit",header:"Profit%"},{body:Qe(({data:m,field:y})=>[wa(Pr(("formatPercent"in v?v.formatPercent:nt(fv))(m[y],2)),1)]),_:1})):We("",!0)]),_:1},8,["value"])]))])}}});export{LS as _};
//# sourceMappingURL=PeriodBreakdown.vue_vue_type_script_setup_true_lang-BFChaFcs.js.map
