import{d as $,u as C,r as g,o as L,c as p,a as l,w as S,b as d,e as a,s as N,f as i,i as V,g as q,h,_ as E,j as F,k as b,l as x,F as z,m as M,n as U,p as j,q as R,t as T,v as A,x as k}from"./index-Cwqm8wBn.js";import{_ as D}from"./check-olqpNIE9.js";import{u as H,_ as O}from"./useSortable-66GE0llz.js";const W={class:"flex gap-1"},G=$({__name:"BotRename",props:{bot:{type:Object,required:!0}},emits:["cancelled","saved"],setup(y,{emit:e}){const n=y,c=e,_=C(),s=g("");L(()=>{s.value=n.bot.botName});const B=()=>{_.updateBot(n.bot.botId,{botName:s.value}),c("saved")};return(I,u)=>{const t=N,m=D,r=q,w=E;return l(),p("form",{class:"flex w-full gap-2",onSubmit:S(B,["prevent"])},[d(t,{modelValue:i(s),"onUpdate:modelValue":u[0]||(u[0]=f=>V(s)?s.value=f:null),size:"small",class:"w-full",placeholder:"Bot name",autofocus:""},null,8,["modelValue"]),a("div",W,[d(r,{type:"submit",size:"small",severity:"secondary",title:"Save",class:"w-8 h-8 p-0!"},{default:h(()=>[d(m)]),_:1}),d(r,{size:"small",severity:"secondary",title:"Cancel",class:"w-8 h-8 p-0!",onClick:u[1]||(u[1]=f=>I.$emit("cancelled"))},{default:h(()=>[d(w)]),_:1})])],32)}}}),J={key:0,class:"w-full mx-2"},K={key:0,class:"font-bold text-2xl mb-2"},P=["active","title","onClick"],Q=$({__name:"BotList",props:{small:{type:Boolean}},setup(y){const e=C(),n=g([]),c=g(),_=g(null),s=F(()=>e.availableBotsSorted);H(_,s,{handle:".handle",onUpdate:t=>{const m=s.value[t.oldIndex].botId,r=s.value[t.newIndex].botId;e.updateBot(m,{sortId:t.newIndex}),e.updateBot(r,{sortId:t.oldIndex})}});const B=t=>{n.value.includes(t)||n.value.push(t)},I=t=>{var r;const m={...e.botStores[t].getLoginInfo(),botId:t};(r=c.value)==null||r.openLoginModal(m)},u=t=>{n.value.includes(t)&&n.value.splice(n.value.indexOf(t),1)};return(t,m)=>{const r=O,w=G,f=j;return i(e).botCount>0?(l(),p("div",J,[t.small?b("",!0):(l(),p("h3",K,"Available bots")),a("ul",{ref_key:"sortContainer",ref:_,class:"flex flex-col divide-y border-x border-surface-500 rounded-sm border-y divide-solid divide-surface-500"},[(l(!0),p(z,null,M(i(s),o=>(l(),p("li",{key:o.botId,active:o.botId===i(e).selectedBot,button:"",title:`${o.botId} - ${o.botName} - ${o.botUrl} - ${i(e).botStores[o.botId].isBotLoggedIn?"":"Login info expired!"}`,class:U(["flex items-center p-2",{"bg-primary-100 dark:bg-primary-800 underline font-semibold":o.botId===i(e).selectedBot}]),onClick:v=>i(e).selectBot(o.botId)},[t.small?b("",!0):(l(),x(r,{key:0,class:"handle cursor-pointer me-2 fs-4"})),i(n).includes(o.botId)?(l(),x(w,{key:1,bot:o,onSaved:v=>u(o.botId),onCancelled:v=>u(o.botId)},null,8,["bot","onSaved","onCancelled"])):(l(),x(f,{key:2,bot:o,"no-buttons":t.small,onEdit:v=>B(o.botId),onEditLogin:v=>I(o.botId)},null,8,["bot","no-buttons","onEdit","onEditLogin"]))],10,P))),128))],512),t.small?b("",!0):(l(),x(R,{key:1,ref_key:"loginModal",ref:c,class:"mt-2","login-text":"Add new bot"},null,512))])):b("",!0)}}}),X={},Y={class:"mt-5"},Z={class:"flex justify-center"};function ee(y,e){const n=Q,c=A;return l(),p("div",Y,[a("div",Z,[d(n,{class:"max-w-xl"})]),d(c),e[0]||(e[0]=a("div",{title:"Freqtrade logo",class:"logo-svg my-5 mx-auto dark:bg-white bg-black sm:w-[250px] sm:h-[250px] w-[150px] h-[150px] transition-all duration-300"},null,-1)),e[1]||(e[1]=a("h1",{class:"font-bold text-2xl sm:text-4xl mb-4 transition-all"},"Welcome to the Freqtrade UI",-1)),e[2]||(e[2]=a("div",null,"This page allows you to control your trading bot.",-1)),d(c),e[3]||(e[3]=a("p",{class:"mb-2"},[k(" If you need any help, please refer to the "),a("a",{class:"text-primary underline cursor-pointer",href:"https://www.freqtrade.io/en/latest/",target:"_blank"}," Freqtrade Documentation "),k(" . ")],-1)),e[4]||(e[4]=a("p",{class:"mb-5"},[k("Have fun - "),a("i",null,"wishes you the Freqtrade team")],-1))])}const se=T(X,[["render",ee],["__scopeId","data-v-4307fab4"]]);export{se as default};
//# sourceMappingURL=HomeView-BHhfihOD.js.map
