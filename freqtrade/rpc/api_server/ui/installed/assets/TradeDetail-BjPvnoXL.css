/*! tailwindcss v4.1.11 | MIT License | https://tailwindcss.com */@layer properties{@supports (((-webkit-hyphens:none)) and (not (margin-trim:inline))) or ((-moz-orient:inline) and (not (color:rgb(from red r g b)))){[data-v-b0409db1],[data-v-b0409db1]:before,[data-v-b0409db1]:after,[data-v-b0409db1]::backdrop{--tw-border-style:solid;--tw-font-weight:initial}}}.detail-header[data-v-b0409db1]{margin-bottom:calc(var(--spacing,.25rem)*1);border-bottom-style:var(--tw-border-style);width:100%;padding-bottom:calc(var(--spacing,.25rem)*1);font-size:var(--text-xl,1.25rem);line-height:var(--tw-leading,var(--text-xl--line-height,calc(1.75/1.25)));--tw-font-weight:var(--font-weight-semibold,600);font-weight:var(--font-weight-semibold,600);border-bottom-width:1px;display:block}.color-up[data-v-b0409db1]{color:var(--a2f9fe70)}.color-down[data-v-b0409db1]{color:var(--19be3f8f)}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-font-weight{syntax:"*";inherits:false}
