import{d as g,a5 as _,u as h,j as u,U as x,o as f,c as b,a as B,b as s,a6 as V,f as i,g as L,h as M,y as w}from"./index-Cwqm8wBn.js";const C={class:"w-full flex"},k=g({__name:"ExchangeSelect",props:{modelValue:{required:!0},modelModifiers:{}},emits:["update:modelValue"],setup(m){const t=_(m,"modelValue"),n=h(),p=u(()=>{const o=n.activeBot.exchangeList.filter(e=>e.valid&&e.supported).sort((e,l)=>e.name.localeCompare(l.name)),a=n.activeBot.exchangeList.filter(e=>e.valid&&!e.supported).sort((e,l)=>e.name.localeCompare(l.name));return[{label:"Supported",options:o.map(e=>({value:e.classname??e.name,text:e.name}))},{label:"Unsupported",options:a.map(e=>({value:e.classname??e.name,text:e.name}))}]}),c=u(()=>{var a;return((a=n.activeBot.exchangeList.find(e=>e.name===t.value.exchange||e.classname===t.value.exchange))==null?void 0:a.trade_modes)??[]}),r=u(()=>c.value.map(o=>({text:`${o.margin_mode} ${o.trading_mode}`,value:o})));return x(()=>t.value.exchange,()=>{c.value.length<2&&(t.value.trade_mode=c.value[0])}),f(()=>{n.activeBot.exchangeList.length===0&&n.activeBot.getExchangeList()}),(o,a)=>{const e=V,l=w,v=L;return B(),b("div",C,[s(e,{id:"exchange-select",modelValue:t.value.exchange,"onUpdate:modelValue":a[0]||(a[0]=d=>t.value.exchange=d),size:"small",class:"min-w-52",filter:"","option-group-label":"label","option-group-children":"options","option-label":"text","option-value":"value",options:i(p)},null,8,["modelValue","options"]),s(e,{id:"tradeMode-select",modelValue:t.value.trade_mode,"onUpdate:modelValue":a[1]||(a[1]=d=>t.value.trade_mode=d),size:"small",class:"min-w-44",options:i(r),"option-label":"text","option-value":"value",disabled:i(r).length<2},null,8,["modelValue","options","disabled"]),s(v,{severity:"secondary",variant:"outlined",class:"ms-2 no-min-w",size:"small",onClick:i(n).activeBot.getExchangeList},{icon:M(()=>[s(l)]),_:1},8,["onClick"])])}}});export{k as _};
//# sourceMappingURL=ExchangeSelect.vue_vue_type_script_setup_true_lang-CExpuEN2.js.map
