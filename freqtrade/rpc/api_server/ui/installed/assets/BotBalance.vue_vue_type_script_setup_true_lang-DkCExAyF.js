import{$ as H,ab as S,d as G,r as g,bg as I,K as R,j as f,E as x,l as d,k as $,a as o,f as t,bh as q,t as O,H as C,c as h,e as i,u as K,o as U,z as b,b as u,g as J,h as _,P as Q,y as W,F as X,m as Y,aS as Z,x as ee,G as te,D as ne}from"./index-Cwqm8wBn.js";import{s as ae,a as oe}from"./index-DhBpwJns.js";import{u as re,E as se,i as ce,a as le,b as ie,c as ue,d as _e}from"./installCanvasRenderer-SA1tPojE.js";import{i as de,a as me}from"./install-_krYdrE6.js";var pe=H.extend({name:"columngroup"}),fe={name:"BaseColumnGroup",extends:S,props:{type:{type:String,default:null}},style:pe,provide:function(){return{$pcColumnGroup:this,$parentInstance:this}}},he={name:"ColumnGroup",extends:fe,inheritAttrs:!1,inject:["$columnGroups"],mounted:function(){var e;(e=this.$columnGroups)===null||e===void 0||e.add(this.$)},unmounted:function(){var e;(e=this.$columnGroups)===null||e===void 0||e.delete(this.$)},render:function(){return null}},ve={name:"Row",extends:S,inject:["$rows"],mounted:function(){var e;(e=this.$rows)===null||e===void 0||e.add(this.$)},unmounted:function(){var e;(e=this.$rows)===null||e===void 0||e.delete(this.$)},render:function(){return null}};const be=G({__name:"BalanceChart",props:{currencies:{required:!0,type:Array},showTitle:{required:!1,type:Boolean}},setup(r){re([de,ce,le,ie,ue,_e,me]);const e=g(null),{width:m}=I(e),a=r,B=R(),c=f(()=>({title:{text:"Balance",show:a.showTitle},center:["50%","50%"],backgroundColor:"rgba(0, 0, 0, 0)",dataset:{dimensions:["balance","currency","est_stake","free","used","stake"],source:a.currencies},tooltip:{trigger:"item",formatter:l=>`${x(l.value.balance,l.value.currency,8)}<br />${l.percent}% (${x(l.value.est_stake,l.value.stake)})`},series:[{type:"pie",radius:["40%","70%"],encode:{value:"est_stake",itemName:"currency",tooltip:["balance","currency"]},label:{formatter:"{b} - {d}%"},tooltip:{show:!0}}]}));return(l,v)=>r.currencies?(o(),d(t(se),{key:0,ref_key:"balanceChart",ref:e,option:t(c),theme:t(B).chartTheme,style:q({height:t(m)*.6+"px"}),autoresize:""},null,8,["option","theme","style"])):$("",!0)}}),ye=O(be,[["__scopeId","data-v-2b19abc8"]]),$e={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Be(r,e){return o(),h("svg",$e,e[0]||(e[0]=[i("path",{fill:"currentColor",d:"M12 9a3 3 0 0 0-3 3a3 3 0 0 0 3 3a3 3 0 0 0 3-3a3 3 0 0 0-3-3m0 8a5 5 0 0 1-5-5a5 5 0 0 1 5-5a5 5 0 0 1 5 5a5 5 0 0 1-5 5m0-12.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5"},null,-1)]))}const ke=C({name:"mdi-eye",render:Be}),ge={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Ce(r,e){return o(),h("svg",ge,e[0]||(e[0]=[i("path",{fill:"currentColor",d:"M11.83 9L15 12.16V12a3 3 0 0 0-3-3zm-4.3.8l1.55 1.55c-.05.21-.08.42-.08.65a3 3 0 0 0 3 3c.22 0 .44-.03.65-.08l1.55 1.55c-.67.33-1.41.53-2.2.53a5 5 0 0 1-5-5c0-.79.2-1.53.53-2.2M2 4.27l2.28 2.28l.45.45C3.08 8.3 1.78 10 1 12c1.73 4.39 6 7.5 11 7.5c1.55 0 3.03-.3 4.38-.84l.43.42L19.73 22L21 20.73L3.27 3M12 7a5 5 0 0 1 5 5c0 .64-.13 1.26-.36 1.82l2.93 2.93c1.5-1.25 2.7-2.89 3.43-4.75c-1.73-4.39-6-7.5-11-7.5c-1.4 0-2.74.25-4 .7l2.17 2.15C10.74 7.13 11.35 7 12 7"},null,-1)]))}const we=C({name:"mdi-eye-off",render:Ce}),xe={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Se(r,e){return o(),h("svg",xe,e[0]||(e[0]=[i("path",{fill:"currentColor",d:"M12 2a2 2 0 0 1 2 2c0 .74-.4 1.39-1 1.73V7h1a7 7 0 0 1 7 7h1a1 1 0 0 1 1 1v3a1 1 0 0 1-1 1h-1v1a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-1H2a1 1 0 0 1-1-1v-3a1 1 0 0 1 1-1h1a7 7 0 0 1 7-7h1V5.73c-.6-.34-1-.99-1-1.73a2 2 0 0 1 2-2M7.5 13A2.5 2.5 0 0 0 5 15.5A2.5 2.5 0 0 0 7.5 18a2.5 2.5 0 0 0 2.5-2.5A2.5 2.5 0 0 0 7.5 13m9 0a2.5 2.5 0 0 0-2.5 2.5a2.5 2.5 0 0 0 2.5 2.5a2.5 2.5 0 0 0 2.5-2.5a2.5 2.5 0 0 0-2.5-2.5"},null,-1)]))}const Ge=C({name:"mdi-robot",render:Se}),Ae={class:"flex flex-wrap flex-row mb-2 justify-end items-center"},Ve={class:"text-xl ms-1 me-auto mb-0"},Le={class:"flex flex-row gap-1"},Me={key:0},Ne=["title"],Ee=G({__name:"BotBalance",setup(r){const e=K(),m=g(!0),a=g(!0),B=f(()=>Number((1.1**e.activeBot.stakeCurrencyDecimals).toFixed(8))),c=f(()=>e.activeBot.botApiVersion>=2.26),l=f(()=>{var s;return(s=e.activeBot.balance.currencies)==null?void 0:s.filter(n=>(!m.value||n.est_stake>=B.value)&&(!c.value||!a.value||(n.is_bot_managed??!0)===!0))}),v=s=>s?ne(s,e.activeBot.stakeCurrencyDecimals):"",A=f(()=>{var s;return(s=l.value)==null?void 0:s.map(n=>({balance:a.value&&c.value&&n.bot_owned!=null?n.bot_owned:n.is_position===!0?n.position:n.balance,currency:n.currency,est_stake:a.value&&c.value?n.est_stake_bot??n.est_stake:n.est_stake,free:a.value&&c.value?n.bot_owned??n.free:n.free,used:n.used,stake:n.stake}))}),V=f(()=>[{field:"currency",header:"Currency"},{field:a.value&&c.value?"bot_owned":"free",header:"Available",asCurrency:!0},{field:a.value&&c.value?"est_stake_bot":"est_stake",header:`in ${e.activeBot.balance.stake}`,asCurrency:!0}]);async function w(){e.activeBot.getBalance()}return U(()=>{w()}),(s,n)=>{const L=Ge,M=Q,k=J,N=we,P=ke,T=W,z=ye,y=oe,D=ve,E=he,j=ae;return o(),h("div",null,[i("div",Ae,[i("label",Ve,b(t(a)?"Bot":"Account")+" Balance",1),i("div",Le,[t(c)?(o(),d(k,{key:0,severity:"secondary",tooltip:t(a)?"Showing Bot balance":"Showing Account balance",onClick:n[0]||(n[0]=p=>a.value=!t(a))},{icon:_(()=>[t(a)?(o(),d(L,{key:0})):(o(),d(M,{key:1}))]),_:1},8,["tooltip"])):$("",!0),u(k,{severity:"secondary",tooltip:t(m)?"Show all balances":"Hide small balances",onClick:n[1]||(n[1]=p=>m.value=!t(m))},{icon:_(()=>[t(m)?(o(),d(N,{key:0})):(o(),d(P,{key:1}))]),_:1},8,["tooltip"]),u(k,{severity:"secondary",onClick:w},{icon:_(()=>[u(T)]),_:1})])]),t(l)?(o(),d(z,{key:0,currencies:t(A)},null,8,["currencies"])):$("",!0),i("div",null,[t(e).activeBot.balance.note?(o(),h("p",Me,[i("strong",null,b(t(e).activeBot.balance.note),1)])):$("",!0),u(j,{value:t(l),footer:""},{default:_(()=>[(o(!0),h(X,null,Y(t(V),p=>(o(),d(y,{key:p.field,field:p.field,header:p.header},Z({_:2},[p.asCurrency?{name:"body",fn:_(({data:F})=>[ee(b(v(F[p.field])),1)]),key:"0"}:void 0]),1032,["field","header"]))),128)),u(E,{type:"footer"},{default:_(()=>[u(D,null,{default:_(()=>[u(y,{footer:"Total",f:""}),u(y,null,{footer:_(()=>[i("span",{class:"font-italic",title:`Increase over initial capital of ${v(t(e).activeBot.balance.starting_capital)} ${t(e).activeBot.balance.stake}`},b(("formatPercent"in s?s.formatPercent:t(te))(t(e).activeBot.balance.starting_capital_ratio)),9,Ne)]),_:1}),u(y,null,{footer:_(()=>[i("strong",null,b(t(a)&&t(c)?v(t(e).activeBot.balance.total_bot):v(t(e).activeBot.balance.total)),1)]),_:1})]),_:1})]),_:1})]),_:1},8,["value"])])])}}});export{Ee as _};
//# sourceMappingURL=BotBalance.vue_vue_type_script_setup_true_lang-DkCExAyF.js.map
