import{H as z,c as k,a as c,e as u,Z as Ie,$ as Ue,a0 as He,a1 as ie,a2 as Ne,N as je,d as M,u as H,r as P,j as V,U as se,b as s,f as e,i as D,h as m,F as S,m as U,z as x,k as v,g as N,x as $,n as Y,l as B,a3 as Oe,Y as Z,t as j,o as X,K as le,a4 as Fe,G as E,D as Q,a5 as Ee,a6 as Ge,y as de,Q as We,W as Ke,s as ue,v as Ze,a7 as Qe,a8 as re,w as G,B as me,I as Ye,a9 as Xe,V as Je,aa as et,X as tt}from"./index-Cwqm8wBn.js";import{_ as st,a as at,b as lt,c as ot,d as nt,s as it}from"./index-D1-At5Up.js";import{_ as oe}from"./DraggableContainer.vue_vue_type_script_setup_true_lang-Q62jSh7o.js";import{c as fe,i as rt,d as ct}from"./TradeList.vue_vue_type_script_setup_true_lang--83SPrIm.js";import{_ as pe,a as dt,b as ut,c as _e}from"./InfoBox.vue_vue_type_script_setup_true_lang-DKaN2Tbm.js";import{s as mt}from"./index-xjUaB_r9.js";import{_ as ft,s as pt}from"./CandleChartContainer-G8jE_PHU.js";import{_ as _t,a as kt,b as vt}from"./TradesLogChart-DuJ-8U1F.js";import{u as ke,E as ve,i as be,a as ye,b as ge,c as he,d as Be}from"./installCanvasRenderer-SA1tPojE.js";import{d as bt,e as yt,i as gt,a as ht,b as xe,c as $e,f as we,g as Bt}from"./chartZoom-DB0tK3Do.js";import{s as W,a as K}from"./index-DhBpwJns.js";import{g as Re,a as ce,s as xt,b as $t}from"./index-BmkrPNT-.js";import{s as wt}from"./index-D6LFPO4a.js";import{_ as Rt}from"./TimeRangeSelect.vue_vue_type_script_setup_true_lang-DJKJVTiO.js";import{s as Ct}from"./index-CONYmxgd.js";import{_ as Tt,a as Vt}from"./StrategySelect.vue_vue_type_script_setup_true_lang-BoCCaGKY.js";import{_ as St}from"./check-olqpNIE9.js";import"./index-BL9ilpkx.js";import"./index-Dt2Q_yjR.js";import"./plus-box-outline-CDxaZbJP.js";import"./index-ULt6J10p.js";import"./index-Bpiivi0c.js";const Mt={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function At(f,t){return c(),k("svg",Mt,t[0]||(t[0]=[u("path",{fill:"currentColor",d:"M8.59 16.58L13.17 12L8.59 7.41L10 6l6 6l-6 6z"},null,-1)]))}const ne=z({name:"mdi-chevron-right",render:At});var qt=Ie`
    .p-radiobutton-group {
        display: inline-flex;
    }
`,Pt={root:"p-radiobutton-group p-component"},Lt=Ue.extend({name:"radiobuttongroup",style:qt,classes:Pt}),Dt={name:"BaseRadioButtonGroup",extends:He,style:Lt,provide:function(){return{$pcRadioButtonGroup:this,$parentInstance:this}}},Ce={name:"RadioButtonGroup",extends:Dt,inheritAttrs:!1,data:function(){return{groupName:this.name}},watch:{name:function(t){this.groupName=t||ie("radiobutton-group-")}},mounted:function(){this.groupName=this.groupName||ie("radiobutton-group-")}};function zt(f,t,o,_,i,a){return c(),k("div",je({class:f.cx("root")},f.ptmi("root")),[Ne(f.$slots,"default")],16)}Ce.render=zt;const It={class:"flex justify-center"},Ut=["for"],Ht={class:"divide-y divide-surface-300 dark:divide-surface-700 divide-solid border-x border-y rounded-sm border-surface-300 dark:border-surface-700"},Nt=["title","onClick"],jt={class:"flex"},Ot={class:"flex flex-col"},Ft={key:0},Et={key:0},Gt={class:"px-3 m-0 list-disc list-inside"},Wt={key:0},Kt=M({__name:"TradeListNav",props:{trades:{required:!0,type:Array},backtestMode:{required:!1,default:!1,type:Boolean}},emits:["trade-select"],setup(f,{emit:t}){const o=f,_=t,i=H(),a=P({}),r=P(!0),l=P("openDate"),n=[{text:"Open date",value:"openDate"},{text:"Profit %",value:"profit"}],d=g=>{a.value=g,_("trade-select",g)},y=V(()=>{const g=l.value==="profit"?"profit_ratio":"open_timestamp";return r.value?o.trades.slice().sort((b,w)=>w[g]-b[g]):o.trades.slice().sort((b,w)=>b[g]-w[g])}),p=P(y.value.map(()=>!1));return se(()=>i.activeBot.selectedPair,()=>{p.value=y.value.map(()=>!1)}),(g,b)=>{const w=mt,T=Ce,h=N,A=pe,R=dt,F=ut,J=ne,ee=Oe;return c(),k("div",null,[u("div",It,[b[2]||(b[2]=u("span",{class:"me-2"},"Sort by:",-1)),s(T,{modelValue:e(l),"onUpdate:modelValue":b[0]||(b[0]=C=>D(l)?l.value=C:null),options:n,name:"radio-options"},{default:m(()=>[(c(),k(S,null,U(n,C=>u("div",{key:C.value,class:"flex items-center"},[s(w,{id:`id-${C.value}`,value:C.value},null,8,["id","value"]),u("label",{for:`id-${C.value}`},x(C.text),9,Ut)])),64))]),_:1},8,["modelValue"])]),u("ul",Ht,[s(h,{severity:"secondary",variant:"text",class:"w-full flex flex-wrap justify-center items-center",title:"Trade Navigation",onClick:b[1]||(b[1]=C=>r.value=!e(r))},{default:m(()=>[$("Trade Navigation "+x(e(r)?"↓":"↑"),1)]),_:1}),(c(!0),k(S,null,U(e(y),(C,I)=>(c(),k("li",{key:C.open_timestamp,class:Y(["flex flex-col py-1 px-1 items-stretch",{"bg-primary-100 dark:bg-primary-800 text-primary-contrast":C.open_timestamp===e(a).open_timestamp}]),title:`${C.pair}`,onClick:O=>d(C)},[u("div",jt,[u("div",Ot,[u("div",null,[e(i).activeBot.botState.trading_mode!=="spot"?(c(),k("span",Ft,x(C.is_short?"S-":"L-"),1)):v("",!0),s(A,{date:C.open_timestamp},null,8,["date"])]),s(R,{trade:C,class:"my-1"},null,8,["trade"]),f.backtestMode?(c(),B(F,{key:0,"profit-ratio":C.profit_ratio,"stake-currency":e(i).activeBot.stakeCurrency},null,8,["profit-ratio","stake-currency"])):v("",!0)]),s(h,{size:"small",class:"ms-auto mt-auto",variant:"outlined",severity:"secondary",onClick:O=>e(p)[I]=!e(p)[I]},{default:m(()=>[e(p)[I]?v("",!0):(c(),B(J,{key:0,width:"24",height:"24"})),e(p)[I]?(c(),B(ee,{key:1,width:"24",height:"24"})):v("",!0)]),_:2},1032,["onClick"])]),s(Z,null,{default:m(()=>{var O;return[e(p)[I]?(c(),k("div",Et,[u("ul",Gt,[(c(!0),k(S,null,U((O=C.orders)==null?void 0:O.filter(q=>q.order_filled_timestamp!==null),q=>(c(),k("li",{key:q.order_timestamp},x(q.ft_order_side)+" "+x(q.amount)+" at "+x(q.safe_price),1))),128))])])):v("",!0)]}),_:2},1024)],10,Nt))),128)),f.trades.length===0?(c(),k("div",Wt,"No trades to show...")):v("",!0)])])}}}),Zt=j(Kt,[["__scopeId","data-v-b76b1d1a"]]),Qt={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Yt(f,t){return c(),k("svg",Qt,t[0]||(t[0]=[u("path",{fill:"currentColor",d:"M15.41 16.58L10.83 12l4.58-4.59L14 6l-6 6l6 6z"},null,-1)]))}const Te=z({name:"mdi-chevron-left",render:Yt}),Xt={class:"flex flex-row mb-1 items-center"},Jt={class:"me-2"},es={class:"grow"},ts={class:"text-end"},ss={class:"text-center flex flex-row h-full items-stretch"},as=M({__name:"BacktestResultChart",props:{timeframe:{},strategy:{},freqaiModel:{},timerange:{},backtestResult:{}},setup(f){const t=f,o=H(),_=P({right:!0,left:!0}),i=P(),a=l=>{i.value={startValue:l.open_timestamp,endValue:l.close_timestamp}};function r(l,n){o.activeBot.getPairHistory({pair:l,timeframe:t.timeframe,timerange:t.timerange,strategy:t.strategy,freqaimodel:t.freqaiModel,columns:n})}return X(()=>{!o.activeBot.selectedPair&&t.backtestResult.pairlist.length>0&&([o.activeBot.selectedPair]=t.backtestResult.pairlist)}),(l,n)=>{const d=ne,y=Te,p=N,g=st,b=ft,w=Zt,T=fe,h=oe;return c(),k("div",null,[u("div",Xt,[u("div",Jt,[s(p,{"aria-label":"Close",title:"Pair Navigation",severity:"secondary",variant:"outlined",size:"small",onClick:n[0]||(n[0]=A=>e(_).left=!e(_).left)},{default:m(()=>[e(_).left?v("",!0):(c(),B(d,{key:0,width:"24",height:"24"})),e(_).left?(c(),B(y,{key:1,width:"24",height:"24"})):v("",!0)]),_:1})]),u("span",es,[n[2]||(n[2]=$(" Graph will always show the latest values for the selected strategy. ")),n[3]||(n[3]=u("br",null,null,-1)),$(" Timerange: "+x(l.timerange)+" - "+x(l.strategy),1)]),u("div",ts,[s(p,{"aria-label":"Close",variant:"outlined",title:"Trade Navigation",size:"small",severity:"secondary",onClick:n[1]||(n[1]=A=>e(_).right=!e(_).right)},{default:m(()=>[e(_).right?(c(),B(d,{key:0,width:"24",height:"24"})):v("",!0),e(_).right?v("",!0):(c(),B(y,{key:1,width:"24",height:"24"}))]),_:1})])]),u("div",ss,[s(Z,{name:"fadeleft"},{default:m(()=>[e(_).left?(c(),B(g,{key:0,class:"overflow-y-auto overflow-x-hidden",style:{"max-height":"calc(100vh - 200px)"},pairlist:l.backtestResult.pairlist,trades:l.backtestResult.trades,"starting-balance":l.backtestResult.starting_balance,"sort-method":"profit","backtest-mode":!0},null,8,["pairlist","trades","starting-balance"])):v("",!0)]),_:1}),s(b,{"available-pairs":l.backtestResult.pairlist,"historic-view":"","reload-data-on-switch":"",timeframe:l.timeframe,timerange:l.timerange,strategy:l.strategy,trades:l.backtestResult.trades,class:"flex-shrink-1 candle-chart-container w-full px-0 h-full align-self-stretch","slider-position":e(i),"freqai-model":l.freqaiModel,onRefreshData:r},null,8,["available-pairs","timeframe","timerange","strategy","trades","slider-position","freqai-model"]),s(Z,{name:"fade"},{default:m(()=>[e(_).right?(c(),B(w,{key:0,class:"overflow-y-auto overflow-x-visible min-w-56",style:{"max-height":"calc(100vh - 200px)"},trades:l.backtestResult.trades.filter(A=>A.pair===e(o).activeBot.selectedPair),onTradeSelect:a},null,8,["trades"])):v("",!0)]),_:1})]),s(h,{header:"Single trades",class:"row mt-2 w-full"},{default:m(()=>[s(T,{class:"row trade-history mt-2 w-full",trades:l.backtestResult.trades,"show-filter":!0},null,8,["trades"])]),_:1})])}}}),ls=j(as,[["__scopeId","data-v-1a79f494"]]),te="Market change %",os=M({__name:"MarketChangeChart",props:{marketChangeData:{},showTitle:{type:Boolean,default:!0}},setup(f){ke([gt,ht,be,xe,ye,$e,ge,he,Be,we]);const t=f,o=le(),_=P(null),i=V(()=>{if(!t.marketChangeData)return{};const a=t.marketChangeData.columns.findIndex(l=>l==="__date_ts"),r=t.marketChangeData.columns.findIndex(l=>l==="rel_mean");return{title:{text:"Market change %",left:"center",show:t.showTitle},backgroundColor:"rgba(0, 0, 0, 0)",dataset:{source:t.marketChangeData.data},tooltip:{trigger:"axis",axisPointer:{type:"line",label:{backgroundColor:"#6a7985"}}},grid:{...yt},legend:{data:[te],right:"5%",selectedMode:!1},xAxis:[{type:"time",axisLine:{onZero:!1},axisTick:{show:!0},axisLabel:{show:!0},axisPointer:{label:{show:!1}},splitLine:{show:!1},splitNumber:20,min:"dataMin",max:"dataMax"}],yAxis:[{type:"value",name:te,splitLine:{show:!1},nameRotate:90,nameLocation:"middle",nameGap:35}],dataZoom:[{type:"inside",start:0,end:100},{bottom:10,start:0,end:100,...bt}],series:[{type:"line",name:te,showSymbol:!1,color:o.chartTheme==="dark"?"#c2c2c2":"black",encode:{x:a,y:r}}]}});return(a,r)=>{var l;return(l=a.marketChangeData)!=null&&l.data?(c(),B(e(ve),{key:0,ref_key:"marketChangeChart",ref:_,option:e(i),theme:e(o).chartTheme,autoresize:""},null,8,["option","theme"])):v("",!0)}}}),ns=j(os,[["__scopeId","data-v-2efdd613"]]),is=M({__name:"TradeDurationChart",props:{trades:{},showTitle:{type:Boolean,default:!0}},setup(f){const t=f,o=le();ke([ye,he,Be,xe,ge,$e,rt,ct,be,we,Bt]);const _=V(()=>t.trades.map(n=>(n.close_timestamp-n.open_timestamp)/(60*1e3))),i=V(()=>t.trades.filter(n=>n.profit_ratio>0).map(n=>(n.close_timestamp-n.open_timestamp)/(60*1e3))),a=V(()=>t.trades.filter(n=>n.profit_ratio<=0).map(n=>(n.close_timestamp-n.open_timestamp)/(60*1e3))),r=V(()=>({title:{text:"Trades durations",left:"center",show:t.showTitle},backgroundColor:"rgba(0, 0, 0, 0)",dataset:[{id:"allTrades",source:[_.value,i.value,a.value]},{id:"allTradesBoxplot",fromDatasetId:"allTrades",transform:{type:"boxplot",config:{itemNameFormatter:n=>{if(n.value===0)return"All trades";if(n.value===1)return"Winning trades";if(n.value===2)return"Losing trades"}}}},{id:"outlier",fromDatasetIndex:1,fromTransformResult:1}],xAxis:{type:"category",show:!0},yAxis:[{type:"value",name:"Trade duration",splitArea:{show:!0},axisLabel:{formatter:l}}],tooltip:{formatter:n=>{if(n.seriesType==="boxplot"){const d=n.data;return`
            <div>${n.name}</div>
            <div>Min: ${l(d[1])}</div>
            <div>Q1: ${l(d[2])}</div>
            <div>Median: ${l(d[3])}</div>
            <div>Q3: ${l(d[4])}</div>
            <div>Max: ${l(d[5])}</div>
          `}return""}},visualMap:[{type:"piecewise",show:!1,dimension:0,pieces:[{min:0,max:0,label:"All Trades",color:"#5470c6"},{min:1,max:1,label:"Winning Trades",color:"#12bb7b"},{min:2,max:2,label:"Losing Trades",color:"#ef5350"}]}],series:[{name:"Trade durations",type:"boxplot",datasetId:"allTradesBoxplot",colorBy:"data"},{name:"outlier",type:"scatter",datasetId:"outlier"}]}));function l(n){if(n>=60){const d=Math.floor(n/60),y=Math.floor(n%60);return`${d}h ${y}m`}return`${Math.floor(n)}m`}return(n,d)=>n.trades.length>0?(c(),B(e(ve),{key:0,option:e(r),autoresize:"",theme:e(o).chartTheme},null,8,["option","theme"])):v("",!0)}}),rs=j(is,[["__scopeId","data-v-fe032d24"]]),cs={class:"text-center flex-fill flex flex-col h-full gap-1"},ds=M({__name:"BacktestGraphs",props:{trades:{required:!0,type:Array}},setup(f){const t=H(),{state:o}=Fe(()=>t.activeBot.getBacktestMarketChange(),null);return(_,i)=>{const a=_t,r=kt,l=ns,n=vt;return c(),k("div",cs,[s(a,{trades:f.trades,class:"flex-grow-1 chart-equal-height"},null,8,["trades"]),s(rs,{class:"flex-grow-1 chart-equal-height",trades:f.trades,"show-title":!0},null,8,["trades"]),s(r,{trades:f.trades,class:"flex-grow-1 chart-equal-height","show-title":!0},null,8,["trades"]),e(o)?(c(),B(l,{key:0,"market-change-data":e(o),class:"flex-grow-1 chart-equal-height"},null,8,["market-change-data"])):v("",!0),s(n,{class:"flex-grow-1 chart-equal-height",trades:f.trades,"show-title":!0},null,8,["trades"])])}}}),us=j(ds,[["__scopeId","data-v-98097119"]]),ms={class:"flex flex-col me-2 text-start"},fs={class:"font-bold"},ps={class:"text-sm"},_s={key:0,class:"text-sm",style:{"white-space":"pre-wrap"}},Ve=M({__name:"BacktestResultSelectEntry",props:{backtestResult:{required:!0,type:Object},selectedBacktestResultKey:{required:!1,default:"",type:String},canUseModify:{required:!1,default:!1,type:Boolean}},setup(f){return(t,o)=>(c(),k("div",ms,[u("div",fs,x(f.backtestResult.metadata.strategyName)+" - "+x(f.backtestResult.strategy.timeframe),1),u("div",ps," TradeCount: "+x(f.backtestResult.strategy.total_trades)+" - Profit: "+x(("formatPercent"in t?t.formatPercent:e(E))(f.backtestResult.strategy.profit_total)),1),f.canUseModify?(c(),k("div",_s,x(f.backtestResult.metadata.notes),1)):v("",!0)]))}});function ae(f,t){return Object.entries(f).reduce((_,[i,a])=>(a.forEach(r=>{const[l,n]=Object.entries(r)[0],d=_.find(y=>y[t]===l);d?d[i]=n:_.push({[t]:l,[i]:n})}),_),[])}const ks={class:"px-0 mw-full"},vs={class:"flex flex-col text-start ms-0 me-2 gap-2"},bs={class:"flex flex-col flex-xl-row"},ys={class:"px-0 xl:px-0 pt-2 xl:pt-0 xl:ps-1 flex-fill"},gs={key:1},hs=M({__name:"BacktestResultComparison",props:{backtestResults:{required:!0,type:Object}},setup(f){const t=f,o=V(()=>{const i={};return Object.entries(t.backtestResults).forEach(([a,r])=>{const l=Re(r.strategy);i[a]=l}),console.log(i),ae(i,"metric")}),_=V(()=>{const i=[{key:"metric",label:"Metric"}];return Object.entries(t.backtestResults).forEach(([a,r])=>{i.push({key:a,label:r.metadata.strategyName})}),i});return(i,a)=>{const r=Ve,l=K,n=W;return c(),k("div",ks,[a[0]||(a[0]=u("div",{class:"flex justify-center"},[u("h3",{class:"font-bold text-3xl"},"Backtest-result comparison")],-1)),u("div",vs,[u("div",bs,[u("div",ys,[s(n,{bordered:"",value:e(o),size:"small","show-gridlines":""},{default:m(()=>[(c(!0),k(S,null,U(e(_),d=>(c(),B(l,{key:d.key,field:d.key,label:d.label},{header:m(()=>[d.key&&d.key in f.backtestResults?(c(),B(r,{key:0,"backtest-result":f.backtestResults[d.key]},null,8,["backtest-result"])):(c(),k("span",gs,x(d.label),1))]),_:2},1032,["field","label"]))),128))]),_:1},8,["value"])])])])])}}}),Bs=M({__name:"BacktestResultPeriodBreakdown",props:{periodicBreakdown:{}},setup(f){const t=f,o=V(()=>{const i=[{value:"day",text:"Days"},{value:"week",text:"Weeks"},{value:"month",text:"Months"}];return t.periodicBreakdown.year&&i.push({value:"year",text:"Years"}),i}),_=P("month");return(i,a)=>{const r=wt,l=K,n=W;return c(),k(S,null,[s(r,{modelValue:e(_),"onUpdate:modelValue":a[0]||(a[0]=d=>D(_)?_.value=d:null),options:e(o),size:"small","allow-empty":!1,class:"m-2","option-label":"text","option-value":"value"},null,8,["modelValue","options"]),s(n,{size:"small",stacked:"sm",value:i.periodicBreakdown[e(_)]},{default:m(()=>[s(l,{field:"date",header:"Date"}),s(l,{field:"trades",header:"Trades"},{body:m(({data:d,field:y})=>[$(x(d[y]??"N/A"),1)]),_:1}),s(l,{field:"profit_abs",header:"Total Profit",body:"formatPrice"in i?i.formatPrice:e(Q)},{body:m(({data:d,field:y})=>[$(x(d[y]?d[y].toFixed(2):"N/A"),1)]),_:1},8,["body"]),s(l,{field:"profit_factor",header:"Profit Factor"},{body:m(({data:d,field:y})=>[$(x(("formatPrice"in i?i.formatPrice:e(Q))(d[y],2)),1)]),_:1}),s(l,{field:"wins",header:"Wins"}),s(l,{field:"draws",header:"Draws"}),s(l,{field:"losses",header:"Losses"},{body:m(({data:d})=>[$(x(d.loses??d.losses??"N/A"),1)]),_:1}),s(l,{field:"wins",header:"Win Rate"},{body:m(({data:d})=>[$(x((d.wins/(d.wins+d.draws+(d.loses??d.losses))*100).toFixed(2)+"%"),1)]),_:1})]),_:1},8,["value"])],64)}}}),xs={class:"flex flex-row w-full justify-between items-center"},$s=M({__name:"BacktestResultTablePer",props:{title:{},results:{},stakeCurrency:{},stakeCurrencyDecimals:{},keyHeader:{default:""},keyHeaders:{default:()=>[]}},setup(f){const t=f,o=V(()=>t.results.map(r=>t.keyHeaders.length>0?{...r,key:typeof r.key=="string"?Array(t.keyHeaders.length).fill(r.key):r.key}:r)),_=V(()=>{const r=[];if(t.keyHeaders.length>0)for(let l=0;l<t.keyHeaders.length;l+=1)r.push({key:`key[${l}]`,label:t.keyHeaders[l],formatter:(n,d)=>Array.isArray(n)?n[l]:n||d.exit_reason||"OTHER"});else r.push({key:"key",label:t.keyHeader,formatter:(l,n)=>l||n.exit_reason||"OTHER"});return r}),i=le(),a=V(()=>ce.value.filter(r=>r.field!=="key"&&i.backtestAdditionalMetrics.includes(r.field)));return(r,l)=>{const n=xt,d=K,y=W,p=oe;return c(),B(p,null,{header:m(()=>[u("div",xs,[$(x(r.title)+" ",1),u("div",null,[l[1]||(l[1]=$(" Shown metrics: ")),s(n,{id:"backtestMetrics",modelValue:e(i).backtestAdditionalMetrics,"onUpdate:modelValue":l[0]||(l[0]=g=>e(i).backtestAdditionalMetrics=g),options:"availableBacktestMetrics"in r?r.availableBacktestMetrics:e(ce),"option-label":"header","option-value":"field",size:"small"},null,8,["modelValue","options"])])])]),default:m(()=>[s(y,{size:"small",hover:"",stacked:"sm",value:e(o)},{default:m(()=>[(c(!0),k(S,null,U(e(_),g=>(c(),B(d,{key:g.key,field:g.key,header:g.label},{body:m(({data:b})=>[$(x(g.formatter(b.key,b)),1)]),_:2},1032,["field","header"]))),128)),s(d,{field:"trades",header:"Trades"}),s(d,{field:"profit_mean",header:"Avg Profit %"},{body:m(({data:g,field:b})=>[$(x(("formatPercent"in r?r.formatPercent:e(E))(g[b],2)),1)]),_:1}),s(d,{field:"profit_total_abs",header:`Tot Profit ${t.stakeCurrency}`},{body:m(({data:g,field:b})=>[$(x(("formatPrice"in r?r.formatPrice:e(Q))(g[b],t.stakeCurrencyDecimals)),1)]),_:1},8,["header"]),s(d,{field:"profit_total",header:"Tot Profit %"},{body:m(({data:g,field:b})=>[$(x(("formatPercent"in r?r.formatPercent:e(E))(g[b],2)),1)]),_:1}),s(d,{field:"wins",header:"Wins"}),s(d,{field:"draws",header:"Draws"}),s(d,{field:"losses",header:"Losses"}),(c(!0),k(S,null,U(e(a),g=>(c(),B(d,{key:g.field,field:g.field,header:g.header},{body:m(({data:b,field:w})=>[$(x(g.is_ratio?("formatPercent"in r?r.formatPercent:e(E))(b[w],2):("formatPrice"in r?r.formatPrice:e(Q))(b[w],2)),1)]),_:2},1032,["field","header"]))),128))]),_:1},8,["value"])]),_:1})}}}),ws={class:"px-0 w-full"},Rs={class:"flex justify-center"},Cs={class:"font-bold text-2xl mb-2"},Ts={class:"flex flex-col text-start ms-0 me-2 gap-2"},Vs={class:"flex flex-col xl:flex-row"},Ss={class:"px-0 px-xl-0 pe-xl-1 grow"},Ms={class:"px-0 xl:px-0 pt-2 xl:pt-0 xl:ps-1 grow"},As=M({__name:"BacktestResultAnalysis",props:{backtestResult:{}},setup(f){const t=f,o=V(()=>{const i=Re(t.backtestResult);return ae({value:i},"metric")}),_=V(()=>{const i=$t(t.backtestResult);return ae({value:i},"setting")});return(i,a)=>{const r=K,l=W,n=oe,d=$s,y=Bs,p=fe;return c(),k("div",ws,[u("div",Rs,[u("h3",Cs," Backtest-result for "+x(i.backtestResult.strategy_name),1)]),u("div",Ts,[u("div",Vs,[u("div",Ss,[s(n,{header:"Strategy settings"},{default:m(()=>[s(l,{size:"small",value:e(_)},{default:m(()=>[s(r,{field:"setting",header:"Setting"}),s(r,{field:"value",header:"Value"})]),_:1},8,["value"])]),_:1})]),u("div",Ms,[s(n,{header:"Metrics"},{default:m(()=>[s(l,{size:"small",borderless:"",value:e(o)},{default:m(()=>[s(r,{field:"metric",header:"Metric"}),s(r,{field:"value",header:"Value"})]),_:1},8,["value"])]),_:1})])]),s(d,{title:"Results per Enter tag",results:i.backtestResult.results_per_enter_tag,"stake-currency":i.backtestResult.stake_currency,"key-header":"Enter Tag","stake-currency-decimals":i.backtestResult.stake_currency_decimals},null,8,["results","stake-currency","stake-currency-decimals"]),s(d,{title:"Results per Exit reason",results:i.backtestResult.exit_reason_summary??[],"stake-currency":i.backtestResult.stake_currency,"key-header":"Exit Reason","stake-currency-decimals":i.backtestResult.stake_currency_decimals},null,8,["results","stake-currency","stake-currency-decimals"]),i.backtestResult.mix_tag_stats?(c(),B(d,{key:0,title:"Results Mixed Tag",results:i.backtestResult.mix_tag_stats??[],"stake-currency":i.backtestResult.stake_currency,"key-headers":["Enter Tag","Exit Tag"],"stake-currency-decimals":i.backtestResult.stake_currency_decimals},null,8,["results","stake-currency","stake-currency-decimals"])):v("",!0),s(d,{title:"Results per pair",results:i.backtestResult.results_per_pair,"stake-currency":i.backtestResult.stake_currency,"key-header":"Pair","stake-currency-decimals":i.backtestResult.stake_currency_decimals},null,8,["results","stake-currency","stake-currency-decimals"]),i.backtestResult.periodic_breakdown?(c(),B(n,{key:1,header:"Periodic breakdown"},{default:m(()=>[s(y,{"periodic-breakdown":i.backtestResult.periodic_breakdown},null,8,["periodic-breakdown"])]),_:1})):v("",!0),s(n,{header:"Single trades"},{default:m(()=>[s(p,{trades:i.backtestResult.trades,"show-filter":!0,"stake-currency":i.backtestResult.stake_currency},null,8,["trades","stake-currency"])]),_:1})])])}}}),qs={class:"w-full flex"},Ps={class:"ms-2"},Ls=M({__name:"FreqaiModelSelect",props:{modelValue:{},modelModifiers:{}},emits:["update:modelValue"],setup(f){const t=Ee(f,"modelValue"),o=H();return X(()=>{o.activeBot.freqaiModelList.length===0&&o.activeBot.getFreqAIModelList()}),(_,i)=>{const a=Ge,r=de,l=N;return c(),k("div",null,[u("div",qs,[s(a,{modelValue:t.value,"onUpdate:modelValue":i[0]||(i[0]=n=>t.value=n),options:e(o).activeBot.freqaiModelList,fluid:"",size:"small"},null,8,["modelValue","options"]),u("div",Ps,[s(l,{severity:"secondary",variant:"outlined",size:"small",onClick:e(o).activeBot.getFreqAIModelList},{icon:m(()=>[s(r)]),_:1},8,["onClick"])])])])}}}),Se=We("btStore",{state:()=>({strategy:"",selectedTimeframe:"",selectedDetailTimeframe:"",timerange:"",maxOpenTrades:null,stakeAmount:null,startingCapital:null,allowCache:!0,enableProtections:!1,stakeAmountUnlimited:!1,freqAI:{enabled:!1,model:"",identifier:""}}),getters:{canRunBacktest:f=>f.strategy!==""},actions:{}}),Ds={class:"mb-2"},zs=["disabled"],Is={for:"timeframe-detail-select",class:"flex justify-end items-center gap-2"},Us={class:"flex items-center"},Hs={class:"flex basis-full"},Ns={class:"flex justify-end items-center"},js={class:"flex flex-wrap md:flex-nowrap justify-between md:justify-center"},Os=M({__name:"BacktestRun",setup(f){const t=H(),o=Se();function _(){const i={strategy:o.strategy,timerange:o.timerange,enable_protections:o.enableProtections};if(o.maxOpenTrades&&(i.max_open_trades=o.maxOpenTrades),o.stakeAmountUnlimited)i.stake_amount="unlimited";else{const r=Number(o.stakeAmount);r&&(i.stake_amount=r.toString())}const a=Number(o.startingCapital);a&&(i.dry_run_wallet=a),o.selectedTimeframe&&(i.timeframe=o.selectedTimeframe),o.selectedDetailTimeframe&&(i.timeframe_detail=o.selectedDetailTimeframe),o.allowCache||(i.backtest_cache="none"),o.freqAI.enabled&&(i.freqaimodel=o.freqAI.model,o.freqAI.identifier!==""&&(i.freqai={identifier:o.freqAI.identifier})),t.activeBot.startBacktest(i)}return(i,a)=>{const r=Tt,l=Vt,n=_e,d=Ct,y=Ke,p=ue,g=Ls,b=Ze,w=Rt,T=N;return c(),k(S,null,[u("div",Ds,[a[13]||(a[13]=u("span",null,"Strategy",-1)),s(r,{modelValue:e(o).strategy,"onUpdate:modelValue":a[0]||(a[0]=h=>e(o).strategy=h)},null,8,["modelValue"])]),u("div",{class:"grid grid-cols-2 border border-surface-500 rounded-sm gap-y-2 gap-2 items-center p-1 pt-3",disabled:e(t).activeBot.backtestRunning},[a[20]||(a[20]=u("h3",{class:"font-bold mb-2 col-span-2 text-center"},"Backtesting parameters",-1)),a[21]||(a[21]=u("label",{for:"timeframe-select"},"Timeframe:",-1)),s(l,{id:"timeframe-select",modelValue:e(o).selectedTimeframe,"onUpdate:modelValue":a[1]||(a[1]=h=>e(o).selectedTimeframe=h),size:"small"},null,8,["modelValue"]),u("label",Is,[a[14]||(a[14]=$("Detail Timeframe: ")),s(n,{hint:"Detail timeframe, to simulate intra-candle results. Not setting this will not use this functionality."})]),s(l,{id:"timeframe-detail-select",modelValue:e(o).selectedDetailTimeframe,"onUpdate:modelValue":a[2]||(a[2]=h=>e(o).selectedDetailTimeframe=h),size:"small","below-timeframe":e(o).selectedTimeframe},null,8,["modelValue","below-timeframe"]),a[22]||(a[22]=u("label",{for:"max-open-trades"},"Max open trades:",-1)),s(d,{id:"max-open-trades",modelValue:e(o).maxOpenTrades,"onUpdate:modelValue":a[3]||(a[3]=h=>e(o).maxOpenTrades=h),size:"small",placeholder:"Use strategy default",type:"number"},null,8,["modelValue"]),a[23]||(a[23]=u("label",{for:"starting-capital"},"Starting capital:",-1)),s(d,{id:"starting-capital",modelValue:e(o).startingCapital,"onUpdate:modelValue":a[4]||(a[4]=h=>e(o).startingCapital=h),size:"small",placeholder:"Use config default",type:"number",step:.001},null,8,["modelValue"]),a[24]||(a[24]=u("label",{for:"stake-amount-bool"},"Stake amount:",-1)),u("div",Us,[u("div",Hs,[s(y,{id:"stake-amount-bool",modelValue:e(o).stakeAmountUnlimited,"onUpdate:modelValue":a[5]||(a[5]=h=>e(o).stakeAmountUnlimited=h)},{default:m(()=>a[15]||(a[15]=[$("Unlimited stake")])),_:1,__:[15]},8,["modelValue"])]),s(d,{id:"stake-amount",modelValue:e(o).stakeAmount,"onUpdate:modelValue":a[6]||(a[6]=h=>e(o).stakeAmount=h),placeholder:"Use strategy default",step:.01,size:"small",disabled:e(o).stakeAmountUnlimited},null,8,["modelValue","disabled"])]),a[25]||(a[25]=u("label",{for:"enable-protections"},"Enable Protections:",-1)),s(y,{id:"enable-protections",modelValue:e(o).enableProtections,"onUpdate:modelValue":a[7]||(a[7]=h=>e(o).enableProtections=h)},null,8,["modelValue"]),e(t).activeBot.botApiVersion>=2.22?(c(),k(S,{key:0},[a[16]||(a[16]=u("label",{for:"enable-cache"},"Cache Backtest results:",-1)),s(y,{id:"enable-cache",modelValue:e(o).allowCache,"onUpdate:modelValue":a[8]||(a[8]=h=>e(o).allowCache=h)},null,8,["modelValue"])],64)):v("",!0),e(t).activeBot.botApiVersion>=2.22?(c(),k(S,{key:1},[u("div",Ns,[a[17]||(a[17]=u("span",{class:"me-2"},"Enable FreqAI:",-1)),s(n,{hint:"Assumes freqAI configuration is setup in the configuration, and the strategy is a freqAI strategy. Will fail if that's not the case."})]),s(y,{id:"enable-freqai",modelValue:e(o).freqAI.enabled,"onUpdate:modelValue":a[9]||(a[9]=h=>e(o).freqAI.enabled=h)},null,8,["modelValue"]),e(o).freqAI.enabled?(c(),k(S,{key:0},[a[18]||(a[18]=u("label",{for:"freqai-identifier"},"FreqAI identifier:",-1)),s(p,{id:"freqai-identifier",modelValue:e(o).freqAI.identifier,"onUpdate:modelValue":a[10]||(a[10]=h=>e(o).freqAI.identifier=h),placeholder:"Use config default",size:"small"},null,8,["modelValue"])],64)):v("",!0),e(o).freqAI.enabled?(c(),k(S,{key:1},[a[19]||(a[19]=u("label",{for:"freqai-model"},"FreqAI Model:",-1)),s(g,{id:"freqai-model",modelValue:e(o).freqAI.model,"onUpdate:modelValue":a[11]||(a[11]=h=>e(o).freqAI.model=h)},null,8,["modelValue"])],64)):v("",!0)],64)):v("",!0),s(b,{class:"col-span-2"}),s(w,{modelValue:e(o).timerange,"onUpdate:modelValue":a[12]||(a[12]=h=>e(o).timerange=h),class:"mx-auto mt-2 col-span-2"},null,8,["modelValue"])],8,zs),a[30]||(a[30]=u("h3",{class:"mt-3 font-bold text-2xl"},"Backtesting summary",-1)),u("div",js,[s(T,{id:"start-backtest",severity:"primary",disabled:!e(o).canRunBacktest||e(t).activeBot.backtestRunning||!e(t).activeBot.canRunBacktest,class:"mx-1",onClick:_},{default:m(()=>a[26]||(a[26]=[$(" Start backtest ")])),_:1,__:[26]},8,["disabled"]),s(T,{severity:"secondary",disabled:e(t).activeBot.backtestRunning||!e(t).activeBot.canRunBacktest,class:"mx-1",onClick:e(t).activeBot.pollBacktest},{default:m(()=>a[27]||(a[27]=[$(" Load backtest result ")])),_:1,__:[27]},8,["disabled","onClick"]),s(T,{severity:"secondary",class:"mx-1",disabled:!e(t).activeBot.backtestRunning,onClick:e(t).activeBot.stopBacktest},{default:m(()=>a[28]||(a[28]=[$(" Stop Backtest ")])),_:1,__:[28]},8,["disabled","onClick"]),s(T,{severity:"secondary",class:"mx-1",disabled:e(t).activeBot.backtestRunning||!e(t).activeBot.canRunBacktest,onClick:e(t).activeBot.removeBacktest},{default:m(()=>a[29]||(a[29]=[$(" Reset Backtest ")])),_:1,__:[29]},8,["disabled","onClick"])])],64)}}}),Fs=j(Os,[["__scopeId","data-v-d4cb60b1"]]),Es={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Gs(f,t){return c(),k("svg",Es,t[0]||(t[0]=[u("path",{fill:"currentColor",d:"M4 11v2h12l-5.5 5.5l1.42 1.42L19.84 12l-7.92-7.92L10.5 5.5L16 11z"},null,-1)]))}const Ws=z({name:"mdi-arrow-right",render:Gs}),Ks={key:0,class:"flex align-center"},Zs={key:0,class:"ms-1"},Qs={class:"flex items-center"},Ys=M({__name:"BacktestHistoryLoad",setup(f){const t=H(),o=P(),_=P(""),i=Qe(_,350,{maxWait:1e3});X(()=>{t.activeBot.getBacktestHistory()});function a(n){var y;const d={title:"Delete result",message:`Delete result ${n.filename} from disk?`,accept:()=>{t.activeBot.deleteBacktestHistoryResult(n)}};(y=o.value)==null||y.show(d)}const r=V(()=>t.activeBot.backtestHistoryList.filter(n=>n.filename.toLowerCase().includes(i.value.toLowerCase())||n.strategy.toLowerCase().includes(i.value.toLowerCase())));function l(n){t.activeBot.getBacktestHistoryResult(n.data)}return(n,d)=>{const y=de,p=N,g=ue,b=K,w=pe,T=Ws,h=me,A=W;return c(),k(S,null,[u("div",null,[s(p,{class:"float-end",title:"Refresh","aria-label":"Refresh",variant:"outlined",severity:"secondary",onClick:e(t).activeBot.getBacktestHistory},{default:m(()=>[s(y)]),_:1},8,["onClick"]),d[1]||(d[1]=u("p",null," Load Historic results from disk. You can click on multiple results to load all of them into freqUI. ",-1)),e(t).activeBot.backtestHistoryList.length>0?(c(),k("div",Ks,[s(g,{id:"trade-filter",modelValue:e(_),"onUpdate:modelValue":d[0]||(d[0]=R=>D(_)?_.value=R:null),type:"text",size:"small",placeholder:"Filter results",title:"Filter results"},null,8,["modelValue"])])):v("",!0),e(t).activeBot.backtestHistoryList.length>0?(c(),B(A,{key:1,class:"mt-2",responsive:"",size:"small",scrollable:"","scroll-height":"50rem","virtual-scroller-options":{itemSize:46},"show-gridlines":"",value:e(r),onRowClick:l},{default:m(()=>[s(b,{field:"strategy",header:"Strategy2"}),s(b,{field:"timeframe",header:"Details"},{body:m(({data:R})=>[u("strong",null,x(R.timeframe),1),R.backtest_start_ts&&R.backtest_end_ts?(c(),k("span",Zs,x(("timestampToTimeRangeString"in n?n.timestampToTimeRangeString:e(re))(R.backtest_start_ts*1e3))+"-"+x(("timestampToTimeRangeString"in n?n.timestampToTimeRangeString:e(re))(R.backtest_end_ts*1e3)),1)):v("",!0)]),_:1}),s(b,{field:"backtest_start_time",header:"Backtest Time"},{body:m(({data:R})=>[s(w,{date:R.backtest_start_time*1e3},null,8,["date"])]),_:1}),s(b,{field:"filename",header:"Filename"}),s(b,{field:"actions",header:"Actions"},{body:m(({data:R})=>[u("div",Qs,[e(t).activeBot.botApiVersion>=2.32?(c(),B(_e,{key:0,class:Y(R.notes?"opacity-100":"opacity-0"),hint:R.notes??""},null,8,["class","hint"])):v("",!0),e(t).activeBot.botApiVersion>=2.31?(c(),B(p,{key:1,class:"ms-1",size:"small",title:"Load this Result.",disabled:R.run_id in e(t).activeBot.backtestHistory,onClick:G(F=>e(t).activeBot.getBacktestHistoryResult(R),["stop"])},{icon:m(()=>[s(T)]),_:2},1032,["disabled","onClick"])):v("",!0),e(t).activeBot.botApiVersion>=2.31?(c(),B(p,{key:2,class:"ms-1",size:"small",severity:"secondary",title:"Delete this Result.",disabled:R.run_id in e(t).activeBot.backtestHistory,onClick:G(F=>a(R),["stop"])},{icon:m(()=>[s(h)]),_:2},1032,["disabled","onClick"])):v("",!0)])]),_:1})]),_:1},8,["value"])):v("",!0)]),s(Ye,{ref_key:"msgBox",ref:o},null,512)],64)}}}),Xs={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function Js(f,t){return c(),k("svg",Xs,t[0]||(t[0]=[u("path",{fill:"currentColor",d:"M21 8c-1.5 0-2.3 1.4-1.9 2.5l-3.6 3.6c-.3-.1-.7-.1-1 0l-2.6-2.6c.4-1.1-.4-2.5-1.9-2.5c-1.4 0-2.3 1.4-1.9 2.5L3.5 16c-1.1-.3-2.5.5-2.5 2c0 1.1.9 2 2 2c1.4 0 2.3-1.4 1.9-2.5l4.5-4.6c.3.1.7.1 1 0l2.6 2.6c-.3 1 .5 2.5 2 2.5s2.3-1.4 1.9-2.5l3.6-3.6c1.1.3 2.5-.5 2.5-1.9c0-1.1-.9-2-2-2m-6 1l.9-2.1L18 6l-2.1-.9L15 3l-.9 2.1L12 6l2.1.9zM3.5 11L4 9l2-.5L4 8l-.5-2L3 8l-2 .5L3 9z"},null,-1)]))}const ea=z({name:"mdi-chart-timeline-variant-shimmer",render:Js}),ta={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function sa(f,t){return c(),k("svg",ta,t[0]||(t[0]=[u("path",{fill:"currentColor",d:"M4 19v1h18v2H2V2h2v15c3 0 6-2 8.1-5.6c3-5 6.3-7.4 9.9-7.4v2c-2.8 0-5.5 2.1-8.1 6.5C11.3 16.6 7.7 19 4 19"},null,-1)]))}const aa=z({name:"mdi-chart-bell-curve-cumulative",render:sa}),la={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function oa(f,t){return c(),k("svg",la,t[0]||(t[0]=[u("path",{fill:"currentColor",d:"M9 14H2v2h7v3l4-4l-4-4zm6-1v-3h7V8h-7V5l-4 4z"},null,-1)]))}const na=z({name:"mdi-compare-horizontal",render:oa}),ia={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function ra(f,t){return c(),k("svg",ia,t[0]||(t[0]=[u("path",{fill:"currentColor",d:"M17 16.88c.56 0 1 .44 1 1s-.44 1-1 1s-1-.45-1-1s.44-1 1-1m0-3c2.73 0 5.06 1.66 6 4c-.94 2.34-3.27 4-6 4s-5.06-1.66-6-4c.94-2.34 3.27-4 6-4m0 1.5a2.5 2.5 0 0 0 0 5a2.5 2.5 0 0 0 0-5M18 3H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h5.42c-.16-.32-.3-.66-.42-1c.12-.34.26-.68.42-1H4v-4h6v2.97c.55-.86 1.23-1.6 2-2.21V13h1.15c1.16-.64 2.47-1 3.85-1c1.06 0 2.07.21 3 .59V5c0-1.1-.9-2-2-2m-8 8H4V7h6zm8 0h-6V7h6z"},null,-1)]))}const ca=z({name:"mdi-table-eye",render:ra}),da={viewBox:"0 0 24 24",width:"1.2em",height:"1.2em"};function ua(f,t){return c(),k("svg",da,t[0]||(t[0]=[u("path",{fill:"currentColor",d:"M6.5 20q-2.28 0-3.89-1.57Q1 16.85 1 14.58q0-1.95 1.17-3.48q1.18-1.53 3.08-1.95q.58-2.02 2.14-3.4Q8.95 4.38 11 4.08v8.07L9.4 10.6L8 12l4 4l4-4l-1.4-1.4l-1.6 1.55V4.08q2.58.35 4.29 2.31T19 11q1.73.2 2.86 1.5q1.14 1.28 1.14 3q0 1.88-1.31 3.19T18.5 20Z"},null,-1)]))}const ma=z({name:"mdi-cloud-download",render:ua}),fa={class:"flex flex-col items-stretch"},pa={class:"ms-2 divide-y border-x border-surface-500 rounded-sm border-y divide-solid divide-surface-500"},_a=["onClick"],ka={class:"flex"},va=M({__name:"BacktestResultSelect",props:{backtestHistory:{required:!0,type:Object},selectedBacktestResultKey:{required:!1,default:"",type:String},canUseModify:{required:!1,default:!1,type:Boolean}},emits:["selectionChange","removeResult","updateResult"],setup(f,{emit:t}){const o=t,_=a=>{o("selectionChange",a)};function i(a,r){r.metadata.editing=!r.metadata.editing,r.metadata.filename&&o("updateResult",{run_id:a,notes:r.metadata.notes??"",filename:r.metadata.filename,strategy:r.metadata.strategyName})}return(a,r)=>{const l=Ve,n=Xe,d=N,y=me,p=pt,g=St;return c(),k("div",fa,[r[0]||(r[0]=u("h3",{class:"font-bold text-2xl"},"Available results:",-1)),u("ul",pa,[(c(!0),k(S,null,U(Object.entries(f.backtestHistory),([b,w])=>(c(),k("li",{key:b,button:"",class:Y([{"bg-primary dark:border-primary text-primary-contrast":b===f.selectedBacktestResultKey},"flex justify-between items-center py-1 px-1"]),onClick:T=>_(b)},[w.metadata.editing?v("",!0):(c(),k(S,{key:0},[s(l,{"backtest-result":w,"can-use-modify":f.canUseModify},null,8,["backtest-result","can-use-modify"]),u("div",ka,[f.canUseModify?(c(),B(d,{key:0,class:"flex-nowrap",size:"small",severity:"secondary",title:"Modify result notes.",onClick:G(T=>w.metadata.editing=!w.metadata.editing,["stop"])},{icon:m(()=>[s(n)]),_:2},1032,["onClick"])):v("",!0),s(d,{size:"small",class:"flex-nowrap",severity:"secondary",title:"Delete this Result from UI.",onClick:G(T=>o("removeResult",b),["stop"])},{icon:m(()=>[s(y)]),_:2},1032,["onClick"])])],64)),w.metadata.editing?(c(),k(S,{key:1},[s(p,{modelValue:w.metadata.notes,"onUpdate:modelValue":T=>w.metadata.notes=T,placeholder:"notes",size:"small"},null,8,["modelValue","onUpdate:modelValue"]),s(d,{size:"small",title:"Confirm",onClick:G(T=>i(b,w),["stop"])},{icon:m(()=>[s(g)]),_:2},1032,["onClick"])],64)):v("",!0)],10,_a))),128))])])}}}),ba={class:"flex flex-row pt-1 me-1 relative",style:{height:"calc(100vh - 60px)"}},ya={key:0,class:"me-3 flex flex-col fixed",style:{"max-height":"calc(100vh - 60px)"}},ga={class:"flex flex-col w-full"},ha={key:0},Ba={class:"w-full"},xa=M({__name:"BacktestingView",setup(f){const t=H(),o=Se(),_=V(()=>t.activeBot.backtestHistory?Object.keys(t.activeBot.backtestHistory).length!==0:!1),i=V(()=>t.activeBot.backtestHistory?Object.keys(t.activeBot.backtestHistory).length>1:!1),a=V(()=>{try{return t.activeBot.selectedBacktestResult.timeframe}catch{return""}}),r=P(!1),l=P("run"),n=P(null),d=()=>{o.strategy=t.activeBot.selectedBacktestResult.strategy_name,t.activeBot.getStrategy(o.strategy),o.selectedTimeframe=t.activeBot.selectedBacktestResult.timeframe,o.selectedDetailTimeframe=t.activeBot.selectedBacktestResult.timeframe_detail||"",o.timerange=t.activeBot.selectedBacktestResult.timerange};return se(()=>t.activeBot.selectedBacktestResultKey,()=>{d()}),X(()=>t.activeBot.getState()),se(()=>t.activeBot.backtestRunning,()=>{t.activeBot.backtestRunning===!0?n.value=window.setInterval(t.activeBot.pollBacktest,1e3):n.value&&(clearInterval(n.value),n.value=null)}),(y,p)=>{const g=ne,b=Te,w=N,T=va,h=ma,A=lt,R=et,F=ca,J=na,ee=aa,C=ea,I=at,O=Ys,q=nt,Me=Fs,Ae=As,qe=hs,Pe=us,Le=ls,De=ot,ze=it;return c(),k("div",ba,[u("div",{class:Y(["flex md:flex-row h-full w-16",{"w-96!":e(r)}])},[e(l)!=="visualize"?(c(),k("div",ya,[s(w,{class:"self-start","aria-label":"Close",size:"small",severity:"secondary",variant:"outlined",onClick:p[0]||(p[0]=L=>r.value=!e(r))},{default:m(()=>[e(r)?v("",!0):(c(),B(g,{key:0,width:"24",height:"24"})),e(r)?(c(),B(b,{key:1,width:"24",height:"24"})):v("",!0)]),_:1}),s(Z,{name:"fade"},{default:m(()=>[e(r)?(c(),B(T,{key:0,"backtest-history":e(t).activeBot.backtestHistory,"selected-backtest-result-key":e(t).activeBot.selectedBacktestResultKey,"can-use-modify":e(t).activeBot.botApiVersion>=2.32,onSelectionChange:e(t).activeBot.setBacktestResultKey,onRemoveResult:e(t).activeBot.removeBacktestResultFromMemory,onUpdateResult:e(t).activeBot.saveBacktestResultMetadata},null,8,["backtest-history","selected-backtest-result-key","can-use-modify","onSelectionChange","onRemoveResult","onUpdateResult"])):v("",!0)]),_:1})])):v("",!0)],2),u("div",ga,[p[13]||(p[13]=u("h2",{class:"ms-5 text-3xl font-bold"},"Backtesting",-1)),e(t).activeBot.canRunBacktest?v("",!0):(c(),k("p",ha," Bot must be in webserver mode to enable Backtesting. ")),u("div",Ba,[s(ze,{value:"run",lazy:""},{default:m(()=>[s(I,null,{default:m(()=>[e(t).activeBot.botApiVersion>=2.15?(c(),B(A,{key:0,modelValue:e(l),"onUpdate:modelValue":p[1]||(p[1]=L=>D(l)?l.value=L:null),class:"flex items-center",value:"historicResults",disabled:!e(t).activeBot.canRunBacktest},{default:m(()=>[s(h,{class:"me-2"}),p[7]||(p[7]=$("Load Results"))]),_:1,__:[7]},8,["modelValue","disabled"])):v("",!0),s(A,{modelValue:e(l),"onUpdate:modelValue":p[2]||(p[2]=L=>D(l)?l.value=L:null),class:"flex items-center",value:"run",disabled:!e(t).activeBot.canRunBacktest},{default:m(()=>[s(R,{class:"me-2"}),p[8]||(p[8]=$("Run backtest"))]),_:1,__:[8]},8,["modelValue","disabled"]),s(A,{id:"bt-analyze-btn",modelValue:e(l),"onUpdate:modelValue":p[3]||(p[3]=L=>D(l)?l.value=L:null),class:"flex items-center",value:"results",disabled:!e(_)},{default:m(()=>[s(F,{class:"me-2"}),p[9]||(p[9]=$("Analyze result"))]),_:1,__:[9]},8,["modelValue","disabled"]),e(i)?(c(),B(A,{key:1,modelValue:e(l),"onUpdate:modelValue":p[4]||(p[4]=L=>D(l)?l.value=L:null),class:"flex items-center",value:"compare-results",disabled:!e(i)},{default:m(()=>[s(J,{class:"me-2"}),p[10]||(p[10]=$("Compare results"))]),_:1,__:[10]},8,["modelValue","disabled"])):v("",!0),s(A,{modelValue:e(l),"onUpdate:modelValue":p[5]||(p[5]=L=>D(l)?l.value=L:null),class:"flex items-center",value:"visualize-summary",disabled:!e(_)},{default:m(()=>[s(ee,{class:"me-2"}),p[11]||(p[11]=$("Visualize summary"))]),_:1,__:[11]},8,["modelValue","disabled"]),s(A,{modelValue:e(l),"onUpdate:modelValue":p[6]||(p[6]=L=>D(l)?l.value=L:null),class:"flex items-center",value:"visualize",disabled:!e(_)},{default:m(()=>[s(C,{class:"me-2"}),p[12]||(p[12]=$("Visualize result"))]),_:1,__:[12]},8,["modelValue","disabled"])]),_:1}),s(De,null,{default:m(()=>[s(q,{value:"historicResults"},{default:m(()=>[s(O)]),_:1}),s(q,{value:"run"},{default:m(()=>[s(Me)]),_:1}),s(q,{value:"results"},{default:m(()=>[e(_)?(c(),B(Ae,{key:0,"backtest-result":e(t).activeBot.selectedBacktestResult,class:"flex-fill"},null,8,["backtest-result"])):v("",!0)]),_:1}),s(q,{value:"compare-results"},{default:m(()=>[e(i)?(c(),B(qe,{key:0,"backtest-results":e(t).activeBot.backtestHistory,class:"flex-fill"},null,8,["backtest-results"])):v("",!0)]),_:1}),s(q,{value:"visualize-summary"},{default:m(()=>[e(_)?(c(),B(Pe,{key:0,trades:e(t).activeBot.selectedBacktestResult.trades,class:"flex-fill"},null,8,["trades"])):v("",!0)]),_:1}),s(q,{value:"visualize",l:""},{default:m(()=>[s(Le,{timeframe:e(a),strategy:e(o).strategy,timerange:e(o).timerange,"backtest-result":e(t).activeBot.selectedBacktestResult,"freqai-model":e(o).freqAI.enabled?e(o).freqAI.model:void 0},null,8,["timeframe","strategy","timerange","backtest-result","freqai-model"])]),_:1})]),_:1})]),_:1}),Je(u("small",{class:"text-end bt-running-label"},"Backtest running: "+x(e(t).activeBot.backtestStep)+" "+x(("formatPercent"in y?y.formatPercent:e(E))(e(t).activeBot.backtestProgress,2)),513),[[tt,e(t).activeBot.backtestRunning]])])])])}}}),Ga=j(xa,[["__scopeId","data-v-e47e09f6"]]);export{Ga as default};
//# sourceMappingURL=BacktestingView-DJ1uF4ie.js.map
